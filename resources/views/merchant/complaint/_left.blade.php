<div class="ser-shop">
  <div class="ser-shop-item">
    <div class="ser-shop-title">Order Details</div>
  </div>
  <div class="ser-shop-item">
    <div class="ser-shop-item-cont1">
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Order Number:</p>
          <span>@if(!empty($order)) {{ $order['order_number'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont-item-col2">
          <p>Channel:</p>
          <span>@if(!empty($order) && isset($order['supplier_name'])) {{ $order['supplier_name'] }} @else - @endif</span>
        </div>
      </div>
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Merchant ID:</p>
          <span>@if(!empty($order)) {{ $order['merchant_id'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont-item-col2">
          <p>Merchant Name:</p>
          <span>@if(!empty($order)) {{ $order['merchant_name'] }} @else - @endif</span>
        </div>
      </div>
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Card Bill:</p>
          <span>@if(!empty($order) && isset($order['card_bill'])) {{ $order['card_bill'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont-item-col2">
          <p>3D:</p>
          <span>@if(!empty($order) && $order['is_3d'] == 1) Yes @else No @endif</span>
        </div>
      </div>
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Bill Name:</p>
          <span>@if($orderAddress) {{ $orderAddress['bill_name'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont-item-col2">
          <p>Bill Email:</p>
          <span>@if($orderAddress) {{ $orderAddress['bill_email'] }} @else - @endif</span>
        </div>
      </div>
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Bill Phone:</p>
          <span>@if($orderAddress) {{ $orderAddress['bill_phone'] }} @else - @endif</span>
        </div>
      </div>
    </div>
  </div>
  <div class="ser-shop-item items-purchased-btn shrink-btn">
    <div class="ser-shop-title">
      Items Purchased
      <b>+</b>
    </div>
  </div>
  <div class="ser-shop-item items-purchased-box shrink-box">
    <div class="ser-shop-item-cont1">
      @if(is_string($orderProduct))
        <div class="ser-shop-cont">
          <div class="ser-shop-cont-item-col2">
            <span>{{ $orderProduct }}</span>
          </div>
        </div>
      @else
        @foreach($orderProduct as $value)
          <div class="ser-shop-cont">
            <div class="ser-shop-cont-item-col2">
              <span>{{ $value['name'] }}</span>
            </div>
            <div class="ser-shop-cont-item-col2">
              <a href="javascript:;">{{ $value['url'] }}</a>
            </div>
          </div>
        @endforeach
      @endif
      <div class="ser-shop-cont">
        <div class="ser-shop-cont-item-col2">
          <p>Status:</p>
          <span>@if(!empty($order)) {{ $order['status_des'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont-item-col2">
          <p>Total Amount:</p>
          <span>@if(!empty($order)) {{ $order['amount'] . '&nbsp;&nbsp;' . $order['currency'] }} @else - @endif</span>
        </div>
        <div class="ser-shop-cont">
          <div class="ser-shop-cont-item-col2">
            <p>Date Payment:</p>
            <span>@if(!empty($order)) {{ date('Y-m-d H:i:s', strtotime('-8 hours', strtotime($order['updated_at']))) . 'UTC' }} @else - @endif</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="ser-shop-item shipping-status-btn shrink-btn">
    <div class="ser-shop-title">
      Shipping Status
      <b>+</b>
    </div>
  </div>
  <div class="ser-shop-item shipping-status-box shrink-box">
    <div class="ser-shop-cont">
      <div class="ser-shop-cont-item">
        <p>Street Address:</p>
        <span>@if(!empty($orderAddress)) {{ $orderAddress['ship_address'] }} @else - @endif </span>
      </div>
    </div>
    <div class="ser-shop-cont">
      <div class="ser-shop-cont-item-col4 ">
        <p>City:</p>
        <span>@if(!empty($orderAddress)) {{ $orderAddress['ship_city'] }} @else - @endif </span>
      </div>
      <div class="ser-shop-cont-item-col4">
        <p>State/Province:</p>
        <span>@if(!empty($orderAddress)) {{ $orderAddress['ship_state'] }} @else - @endif </span>
      </div>
      <div class="ser-shop-cont-item-col4">
        <p>Country:</p>
        <span>@if(!empty($orderAddress)) {{ $orderAddress['ship_country'] }} @else - @endif </span>
      </div>
      <div class="ser-shop-cont-item-col4">
        <p>Zip/Postal Code:</p>
        <span>@if(!empty($orderAddress)) {{ $orderAddress['ship_postcode'] }} @else - @endif </span>
      </div>
    </div>
    <div class="ser-shop-cont">
      <div class="ser-shop-cont-item-col2">
        <p>Tracking Number:</p>
        <span>@if(!empty($orderDelivery)) {{ $orderDelivery['delivery_number'] }} @else Not Available Currently @endif </span>
      </div>
      <div class="ser-shop-cont-item-col2">
        <p>Carrier:</p>
        <span>@if(!empty($orderDelivery)) {{ $orderDelivery['delivery_type'] }} @else Not Available Currently @endif </span>
      </div>
    </div>
    <div class="ser-shop-cont">
      <div class="ser-shop-cont-item" style="overflow: hidden;">
        <p>Shipping Status:</p>
        @if(empty($orderDelivery))
          <span>Package is being prepared by merchant</span>
        @else
          <div class="shipping-status-tabs">
            @foreach($trackingList as $deliveryNumber => $list)
              <ul class="shipping-status-tabs-head">
                <li class="shipping-status-tabs-head-active"><a href="javascript:;">{{ $deliveryNumber }}</a></li>
              </ul>
              <ul class="shipping-status-tabs-main">
                <li class="shipping-status-tabs-main-list shipping-status-tabs-main-list-active">
                  <div class="shipping-status-tabs-main-list-title">Destination Country - @if(isset($list['destination_country']) && !empty($list['destination_country'])) {{ $list['destination_country'] }} @else 查无信息 @endif</div>
                  {{ $destinationList = isset($list['destination_event_list']) && !empty($list['destination_event_list']) ? $list['destination_event_list'] : array() }}

                  @foreach($destinationList as $value)
                    <div class="shipping-status-tabs-main-list-item">{{ $value['date'] . ',' . (!empty($value['address']) ? $value['address'] . ',' : '') . $value['details'] }}</div>
                  @endforeach

                  <div>&nbsp;</div>

                  <div class="shipping-status-tabs-main-list-title">Original Country - @if(isset($list['original_country']) && !empty($list['original_country'])) {{ $list['original_country'] }} @else 查无信息 @endif</div>
                  {{ $originalList = isset($list['original_event_list']) && !empty($list['original_event_list']) ? $list['original_event_list'] : array() }}

                  @foreach($originalList as $value)
                    <div class="shipping-status-tabs-main-list-item">{{ $value['date'] . ',' . (!empty($value['address']) ? $value['address'] . ',' : '') . $value['details'] }}</div>
                  @endforeach
                </li>
              </ul>
            @endforeach
          </div>
        @endif
      </div>
    </div>
  </div>
</div>
