<?php
return [
    'labels'  => [
        '持卡人信息列表'         => 'Cardholder Info List',
        'add_to_card_holder'     => 'add_to_card_holder',
        'add_to_new_card_holder' => 'add_to_new_card_holder',
        'gender'                 => [
            'female', 'male',
        ],

        'id_type' => [
            1 => 'Identity card',
            2 => 'Police ID card',
            3 => 'Hong Kong, Macao and Taiwan Passport',
            4 => 'passport',
            5 => 'Other valid travel documents',
            6 => 'Other types of personal valid documents',
        ],

        'company_position' => [
            1 => 'legal representative',
            2 => 'director',
            3 => 'senior administrator',
            4 => 'manager',
            5 => 'staff',
        ],

        'subject_type' => [
            1 => 'employee',
//            2 => 'cooperative enterprises',
        ],

        'holder_type'                        => [
            1 => 'legal',
            2 => 'other administrator',
        ],
        'security_index'                     => [
            "1" => "What was your first pet's name?",
            "2" => "What is your maternal grandmother's maiden name?",
            "3" => "What is the name of your favourite childhood friend?",
            "4" => "What was the make of your first car?",
            "5" => "In what city or town did your mother and father meet?"
        ],
        '该商户下已有默认持卡人, 无法设置'   => 'There is already a default cardholder under the merchant, and it cannot be set',
        '是否修改该默认持卡人的状态'         => 'Whether to modify the status of the default cardholder',
        '修改失败'                           => 'Modification failed',
        '修改成功'                           => 'Modification succeeded',
        '已有默认持卡人, 无法设置'           => 'There is already a default cardholder, and it cannot be set',
        '是否修改该持卡人状态'               => 'Whether to modify the status of the cardholder',
        '已有审核完成的持卡人信息, 无法修改' => 'There is already an audit-completed cardholder information, and it cannot be updated',
        '虚拟卡数据不存在'                   => 'Virtual card data does not exist',
        '该虚拟卡已绑定持卡人信息'           => 'The virtual card has been bound to the cardholder information',
        '绑定成功'                           => 'Binding succeeded',
        '您确定要绑定该持卡人吗'             => 'Are you sure you want to bind the cardholder?',
        '该持卡人已审核, 无法修改'           => 'The cardholder has been audited, and it cannot be modified',
        '启用'                               => 'Enable',
        '禁用'                               => 'Disable',
        '删除'                               => 'Delete',
        '您确定要启用该持卡人吗'             => 'Are you sure you want to enable the cardholder?',
        '您确定要禁用该持卡人吗'             => 'Are you sure you want to disable the cardholder?',
        '您确定要删除该持卡人吗'             => 'Are you sure you want to delete the cardholder?',
        '该持卡人不存在'                     => 'The cardholder does not exist',
        '该持卡人已被删除'                   => 'The cardholder has been deleted',
        '该持卡人已绑定虚拟卡, 无法删除'     => 'The cardholder has been bound to a virtual card, and it cannot be deleted',
        '默认持卡人'                         => 'Default Cardholder',
        '取消默认持卡人'                     => 'Cancel Default Cardholder',
        '只能设置一个默认持卡人'             => 'You can only set one default cardholder',
        '您确定设置为默认持卡人吗'           => 'Are you sure you want to set the default cardholder?',
        '您确定取消默认持卡人吗'             => 'Are you sure you want to cancel the default cardholder?',
        '您确定进行批量删除吗'               => 'Are you sure you want to perform batch deletion?',
        '批量删除'                           => 'Batch Delete',
        '删除失败'                           => 'Delete failed',
        '删除成功'                           => 'Delete succeeded',
        '存在已绑定虚拟卡的持卡人，无法删除'  => 'There is a cardholder with a bound virtual card, and it cannot be deleted',
        '持卡人信息审核未通过'               => 'Cardholder information audit not passed',
        '持卡人年龄在18~65岁之间'            => 'The cardholder age is between 18 and 65 years old',
        '国家/地区与手机号码前缀不匹配'      => 'Country/region does not match mobile phone number prefix',
    ],
    'fields'  => [
        'cardholder_id'   => 'Card Holder',
        'security_index'  => 'Security Question',
        'security_answer' => 'Security Answer',
    ],
    'options' => [
        'gender' => [
            '男' => 'male',
            '女' => 'female'
        ],

        'id_type' => [
            '居民身份证'         => 'Identity card',
            '军人或武警身份证'   => 'Police ID card',
            '港澳台通行证'       => 'Hong Kong, Macao and Taiwan Passport',
            '护照'               => 'passport',
            '其他有效旅行证件'   => 'Other valid travel documents',
            '其他类个人有效证件' => 'Other types of personal valid documents'
        ],

        'company_position' => [
            '法人代表'   => 'legal representative',
            '董事'       => 'director',
            '高级管理员' => 'senior administrator',
            '经理'       => 'manager',
            '职员'       => 'staff'
        ],

        'subject_type' => [
            '员工'     => 'employee',
//            '合作企业' => 'cooperative enterprises'
        ],

        'holder_type' => [
            '法人'       => 'legal',
            '其他管理员' => 'other administrator'
        ],

        'audit_status'          => [
            '待审核'     => 'pending',
            '审核通过'   => 'pass',
            '审核不通过' => 'fail'
        ],
        'status'                => [
            '启用' => 'enable',
            '禁用' => 'disable',
        ],
        'is_default_cardholder' => [
            '是' => 'yes',
            '否' => 'no',
        ],

    ],
];
