<?php

return [
    'labels'  => [
        'status'            => [
            'To be reviewed', 'Approved', 'Reject',
        ],
        'company_details'   => 'Company details',
        'holding_co'        => 'Holding company',
        'ubo'               => 'UBO',
        'director'          => 'Director',
        'contacts'          => 'Contacts',
        'kyc'               => 'Basic information of KYC',
        'audit_information' => 'Review information',
        'business_info'     => 'Business Information',
        'authorized'        => 'Authorized Signatory',
        'case'              => 'Customer Due Diligence',
        'hc_ln_scan_missing'=> 'There are holding companies that have not been scanned by LN.',
        'director_ln_scan_missing' => 'There is at least one director who has not been scanned by LN.',
        'beneficiary_ln_scan_missing' => 'There is at least one ultimate beneficiary who has not been scanned by LN.',
        'authorized_ln_scan_missing' => 'There is at least one authorized person who has not been scanned by LN.'
    ],
    'fields'  => [
        'register'                       => 'Register',
        'submit'                         => 'Submit',
        'operation'                      => 'Operation',
        'add_to'                         => 'Add',
        'delete'                         => 'Delete',
        'back_to_login'                  => 'Back',
        'title'                          => 'Fill In Merchant KCY',
        'imgTip'                         => 'The image has not been uploaded, please click the Upload button after uploading!',
        'xlsxTip'                        => 'The table has not been uploaded, please click the Upload button after uploading!',
        'company_entity'                 => 'Company',
        'company_name'                   => 'MerchantLegal entity name',
        'company_type'                   => [
            'chinese_mainland' => 'Chinese Mainland',
            'chinese_hongkong' => 'Hong Kong',
            'overseas'         => 'Foreign'
        ],
        'business_type'                  => 'Business type',
        'business_select'                => [
            'order'   => 'Order',
            'virtual' => 'Virtual',
        ],
        'register_img'                   => 'Copy of Certificate of Incorporation',
        'registered_address'             => 'Company Registered Address',
        'office_phone'                   => 'Office Phone',
        'office_address'                 => 'Office Address',
        'mainland_type_info'             => [
            'certificate_no'     => 'Business License Number',
            'certificate_img'    => 'Copy of Business License',
            'enforcer_name'      => 'Name of Legal Representative',
            'enforcer_code'      => 'Copy of ID Card of Legal Representative',
            'enforcer_hold_code' => 'Copy of Legal Representative Holding ID Card',
            'contacts_email'     => 'E-Mail',
        ],
        'hongkong_type_info'             => [
            'certificate_no'     => 'Business Registration Certificate Number',
            'certificate_img'    => 'Business Registration Certificate',
            'enforcer_name'      => 'Name of Director',
            'enforcer_code'      => "Copy of Director's Passport",
            'enforcer_hold_code' => 'Copy of Director Holding Passport',
            'contacts_email'     => 'Business Contact Person E-Mail',
        ],
        'overseas_type_info'             => [
            'certificate_no'     => 'Registration Certificate Number',
            'certificate_img'    => 'Copy of Registration Certificate',
            'enforcer_name'      => 'Name of Director',
            'enforcer_code'      => "Copy of Director's Passport",
            'enforcer_hold_code' => 'Copy of Director Holding Passport',
            'contacts_email'     => 'Business Contact Person E-Mail',
        ],
        'mainland_shareholder_info'      => [
            'info'             => 'Shareholder Information (Shareholding Ratio Exceed 25%)',
            'shareholder_name' => 'Name of Shareholder',
            'shareholder_code' => "Shareholder's ID Card (Front And Back)",
        ],
        'hongkong_shareholder_info'      => [
            'info'             => 'Shareholder Information (Shareholding Ratio Exceed 25%)',
            'shareholder_name' => 'Name of Shareholder',
            'shareholder_code' => "Copy of Shareholder's Passport",
        ],
        'overseas_shareholder_info'      => [
            'info'             => 'Shareholder Information (Shareholding Ratio Shall Exceed 25%)',
            'shareholder_name' => 'Name of Shareholder',
            'shareholder_code' => "Copy of Shareholder's Passport",
        ],
        'equity_certificate_img'         => 'Equity Certificate',
        'bank_account_img'               => 'Bank Account Certificate',
        'merchant_apply_file'            => 'Merchant Application Form',
        'contacts_name'                  => 'Business Contact Person Name',
        'contacts_phone'                 => 'Phone',
        'contacts_position'              => 'Position',
        'contacts_email'                 => 'E-Mail',
        'password'                       => 'Password',
        'confirm_password'               => 'Confirm Password',
        'tip'                            => [
            'success'                => 'Please pay attention to the email information when the application is successful!',
            'shareholders_max'       => 'Add up to four shareholders!',
            'creation_failed'        => 'Registration failed. The business email has been registered or the information filled in is incorrect!',
            'shareholder_name_empty' => "Incomplete filling of shareholder's name",
            'register_img_empty'     => 'Certificate of incorporation not uploaded!',
            'status_error'           => 'The state is already the final state!',
            'password_error'         => 'The two passwords are inconsistent!',
            'password_min'           => 'Mandatory and must be at least 8-14 characters!Contains at least two types including  of letters/numbers/punctuation marks!',
            'merchant_apply_file'    => 'Please fill in and submit according to the appendix!',
            'template_one'           => 'Appendix I',
            'template_two'           => 'Appendix II',
            'img_restrictions'       => 'Supported file types: jpg, jpeg, png, pdf',
            'file_restrictions'      => 'Supported file types：xlsx',
            'system_error'           => 'The system is busy, please contact operations',
            'ln_scan_pending'        => 'The LN scan result has not been processed yet. Please process it before submitting!'
        ],
        'already_registered'             => 'The merchant has applied, please log in the system to check the review progress!',
        'country'                        => 'Country of incorporation',
        'company_address'                => 'Company registered address',
        'cert_validity'                  => 'Validity of business registration certificate',
        'export_country'                 => 'Targeted countries',
        'reg_cert_no'                    => 'Company registration number',
        'business_reg_no'                => 'Business registration number',
        'found_date'                     => 'Date of establishment',
        'fin_statement'                  => 'Financial statement',
        'comp_reg_cert'                  => 'Certificate of incorporation',
        'business_reg_cert'              => 'Business registration document',
        'shareholder_structure'          => 'Shareholding chart',
        'annual_rpt'                     => 'Annual return (NAR1 (NNC1 only for merchants established less than 1 year)',
        'bank_statement'                 => 'Bank statement',
        'comp_addr_cert'                 => 'Company Proof of Address',
        'equity_ratio'                   => 'Shareholding percentage',
        'cert_type'                      => 'Document type',
        'nationality'                    => 'nationality',
        'name'                           => 'Full Name',
        'birth_date'                     => 'Date of birth',
        'cert_number'                    => 'ID/Passport number',
        'document_valid'                 => 'Validity Period',
        'is_valid_indefinitely'          => 'This document is valid indefinitely',
        'address'                        => 'Residential address',
        'is_signatory'                   => 'Is the UBO also acting as the Authorised signatory',
        'cert_file'                      => 'Coloured Passport/HKID/CNID (both sides) ',
        'residence_doc'                  => 'Residential address documentation',
        'hold_cert_photo'                => 'Photo of UBO holding ID card',
        'hold_cert_photo_b'              => 'Photo of Director holding ID card',
        'hold_cert_photo_c'              => 'Power of Attorney (PoA)',
        'is_listed_in_nar1'              => 'Is the UBO also acting as the director',
        'is_listed_in_nar1_b'            => 'Is the director listed on the NAR1?',
        'is_signatory_b'                 => 'Director acting as Authorised signatory',
        'is_signatory_c'                 => 'Authorized Signatory',
        'holding_co'                     => 'Holding company',
        'ubo'                            => 'UBO',
        'director'                       => 'Director',
        'authorized'                     => 'Authorized Signatory',
        'share_ratio'                    => 'Ultimate Shareholding percentage',
        'random_password'                => 'random password',
        'get_password'                   => 'Get a random password',
        'download_template'              => 'Click to download template',
        'nature_of_business'             => 'Nature of Business',
        'business_model'                 => 'Business Model',
        'merchant_portal_url_app'        => 'Merchant URL/APP name',
        'mcc_code'                       => 'MCC',
        'payment_channel'                => 'Business Type',
        'transaction_fee_rate_type'      => 'Transaction Fee Rate Type',
        'transaction_fee_rate'           => 'Transaction Fee Rate',
        'additional_fees'                => 'Additional Fees/Surcharges',
        'settlement_cycle'               => 'Settlement Cycle',
        'transaction_currency'           => 'Transaction Currency',
        'settlement_currency'            => 'Settlement Currency',
        'store_info'                     => 'Store Profile',
        'store_name_zh'                  => 'Store Name (Chinese)',
        'store_name_en'                  => 'Store Name (English)',
        'store_address'                  => 'Store Address',
        'store_business_address'         => 'Business Address',
        'store_es_address'               => 'Additional Address(es)',
        'average_transaction_value'      => 'Average Transaction Value (ATV)',
        'avg_daily_transaction_count'    => 'Average Daily Transaction Count',
        'avg_daily_transaction_volume'   => 'Average Daily Transaction Volume',
        'avg_monthly_transaction_count'  => 'Average Monthly Transaction Count',
        'avg_monthly_transaction_volume' => 'Average Monthly Transaction Volume',
        'merchant'                       => ['source' => 'Merchant source'],
        'case'                           => ['merchant_id'   => 'Merchant ID',
                                             'case_number'   => 'Case number',
                                             'case_type'     => 'Case type',
                                             'auditor'       => 'Onboarding analyst',
                                             'completion_at' => 'Completion time',
                                             'created_at'    => 'Submission time'],
        'help' => [
            'comp_reg_cert'         => 'Please provide the Certificate of Incorporation that is within its validity period.',
            'business_reg_cert'     => 'Please provide a valid business registration certificate.',
            'fin_statement'         => "Latest financial statements to assess the company's financial situation",
            'shareholder_structure' => 'Company shareholdings, including a description of the shareholding structure to the ultimate beneficiary Must be signed and dated within the last three months.',
            'annual_rpt'            => 'A NAR1 valid within the past 12 months, or a NNC1 for merchants established less than 1 year ago.',
            'bank_statement'        => 'Official account opening letter or bank statement of the company account under the name of the Hong Kong entity, with complete account name, account number and bank information',
            'comp_addr_cert'        => "Proof of the company's address can be a utility bill from the last three months, a phone bill, a tax bill, a lease agreement from the last year, etc. Any one of these will do",
            'residence_doc'         => "To verify the authenticity of a natural person's registered address, a utility bill, lease agreement, or government-issued letter can be provided. Must be dated within the last three months.",
            'random_password'       => 'Successfully copied random password',
            'generate_password'     => 'Click to generate a random password',
            'ubo'                   => 'Add at least one UBO',
            'director'              => 'Add at least one Director',
            'annual_rpt_required'   => 'Enterprises established for less than one year are required to submit an annual report',
            'authorized'            => 'Add at least one Authorized Signatory',
            'copy_ubo'              => 'Will this UBO information be automatically copied to the director information?',
        ],
    ],
    'options' => [
        'type'            => [
            'Chinese Mainland', 'Chinese Hong Kong', 'Overseas'
        ],
        'business_type'   => [
            'Order', 'Virtual',
        ],
        'confirm'         => [
            true  => 'yes',
            false => 'no',
        ],
        'business_model'  => [
            1 => 'Online Business', 2 => 'Offline Business',
        ],
        'payment_channel' => [
            1 => 'Online Card-Not-Present (CNP)', 2 => 'Digital Wallet Payment (WeChat Pay/Alipay)', 3 => 'In-Store Card-Present (CP)'
        ],
        'store_address'   => [1 => 'Same as Registered Address', 2 => 'Business Address', 3 => 'Additional Address(es)'],
        'cert_type'       => [1 => 'CNID', 2 => 'HKID', 3 => 'Coloured Passport'],
        'is_signatory'    => [true => 'ubo/director', false => 'authorized'],
        'audit'           => [1 => 'approve', 2 => 'reject', 4 => 'Awaiting Materials'],
        'source'          => ['Please select', 'Outbound lead', 'inbound lead'],
        'fee_type'        => ['IC++', 'Blended Fee'],
    ],
];
