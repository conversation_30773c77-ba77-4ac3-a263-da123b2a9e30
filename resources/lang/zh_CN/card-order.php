<?php

use App\Models\CardTransaction;

return [
    'labels'  => [
        'transaction_type'          => CardTransaction::$transactionTypeMap,
        'transaction_status'        => CardTransaction::$transactionStatusMap,
        'transaction_settle_status' => CardTransaction::$transactionChannelStatusMap,
        'Not settled'               => '未结算',
    ],
    'fields'  => [
        'transaction_order_id'      => '渠道订单号',
        'card_number'               => '卡号',
        'amount'                    => '交易金额',
        'currency'                  => '交易币种',
        'settle_amount'             => '结算金额',
        'settle_currency'           => '结算币种',
        'transaction_type'          => '交易类型',
        'transaction_status'        => '交易状态',
        'fail_reason'               => '失败原因',
        'transaction_description'   => '交易描述',
        'transaction_mcc'           => 'mcc',
        'transaction_settle_status' => '结算状态',
        'auth_code'                 => '授权码',
    ],
    'options' => [
        'transaction_type_map' => [
            '常规卡' => 'Regular Card',
            '共享卡' => 'Sharecard Card',
        ],
        'internal_status_map'  => [
            '待处理'   => 'Pending',
            '处理中'   => 'Processing',
            '处理失败' => 'Processing failed',
            '完成'     => 'Complete',
        ],
    ],
];
