<?php
return [
    'labels' => [
        'RefundError'               => 'RefundError',
        'ConfirmExport'             => '您确定导出退款信息吗?',
        'order_info_failed'         => '获取订单信息失败!',
        'refund_amount_tip'         => '退款金额须大于0.00,小于',
        'get_merchant_token_failed' => '获取商户api_token失败 !',
    ],
    'fields' => [
        'refund_id'                 => '退款订单号',
        'order_id'                  => '订单号',
        'payment_refund_id'         => '渠道订单号',
        'currency'                  => '币种',
        'amount'                    => '金额',
        'status'                    => '状态',
        'code'                      => '返回代码',
        'result'                    => '返回结果',
        'remark'                    => '备注',
        'completed_at'              => '完成时间',
        'payment_info'              => '渠道信息',
        'merchant_id'               => 'MID',
        'order_number'              => '商户订单号',
        'available_refund_amount'   => '可退金额',
        'apply_refund_amount'       => '退款金额',
        'code/result/remark'        => '返回代码/返回结果/备注',
        'paymentCode/paymentResult' => '渠道返回代码/返回结果',
        'order' => [
            'order_number'  => '商户订单号',
            'merchant_name' => '商户名',
            'currency'      => '原始订单币种',
            'amount'        => '原始订单金额',
        ],
        'refund_stats' => [
            'success'  => '退款成功',
            'pending'  => '退款待审核',
            'failed'   => '退款失败',
            'overtime' => '退款超时！',
        ],
    ],
    'options' => [],
];
