<?php

namespace App\Jobs;

use App\Models\MerchantBusinessChargeDishonour;
use App\Models\Order;
use App\Services\SettlementService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Classes\Pay\Contracts\Support;
use App\Models\ChargebackPenalty as ModelsChargebackPenalty;

class ChargebackPenalty implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $chargeback   = [];
    protected $chargebackId = '0';
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $delay = 0)
    {
        $this->chargeback   = $data['chargeback'];
        $this->chargebackId = $data['chargeback_id'];
		// 设置延迟的时间，delay() 方法的参数代表多少秒之后执行
		$this->delay($delay);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // 设置时区为UTC，因为队列时区为PRC，导致时间错误
		date_default_timezone_set('UTC');
        //获取订单信息
        $order = Order::query()->with(['business:business_id', 'business.chargeDishonours:id,type,business_id,dishonour_rate,penalty_type,penalty_value', 'business.chargeCc:business_id,dishonour_fee'])->whereHas('business.chargeDishonours', function(Builder $query) {
            $query->where('penalty_status', MerchantBusinessChargeDishonour::PENALTY_STATUS_ENABLE)->where('type', MerchantBusinessChargeDishonour::TYPE_1);
        })->selectRaw('business_id,order_number,merchant_id,merchant_name')->find($this->chargeback['order_id']);

        if (empty($order)) {
            return;
        }

        $order            = $order->toArray();
        $dishonourFee     = $order['business']['charge_cc']['dishonour_fee'] ?? 0;  //拒付处理费
        $chargeDishonours = $order['business']['charge_dishonours'] ?? [];          //拒付罚金*配置
        $penaltyInsert    = [];
        foreach ($chargeDishonours as $val) {
            if ($val['type'] != MerchantBusinessChargeDishonour::TYPE_1) {
                continue;
            }

            $penaltyAmount = 0;
            $type          = 0;
            switch ($val['penalty_type']) {
                case MerchantBusinessChargeDishonour::PENALTY_TYPE_REGULAR:
                    //固定罚金
                    $penaltyAmount = Support::amount_format($val['penalty_value']);
                    $type          = ModelsChargebackPenalty::TYPE_FIXED_PENALTY_SPECIAL;
                    break;

                case MerchantBusinessChargeDishonour::PENALTY_TYPE_PROPORTION:
                    //比例罚金
                    $penaltyAmount = Support::amount_format(($val['penalty_value'] * $dishonourFee) / 100);
                    $type          = ModelsChargebackPenalty::TYPE_PROPORTIONAL_PENALTY_SPECIAL;
                    break;
            }

            if ($penaltyAmount > 0) {
                $penaltyInsert = [
                    'order_id'            => $this->chargeback['order_id'],
                    'order_number'        => $order['order_number'],
                    'chargeback_id'       => $this->chargebackId,
                    'merchant_id'         => $order['merchant_id'],
                    'business_id'         => $order['business_id'],
                    'merchant_name'       => $order['merchant_name'],
                    'business_history_id' => SettlementService::getBusinessHistoryId($order['business_id']),
                    'type'                => $type,
                    'penalty_rate'        => 0,
                    'penalty_value'       => $val['penalty_value'],
                    'penalty_amount'      => $penaltyAmount,
                    'chargeback_at'       => $this->chargeback['created_at'],
                    'settle_at'           => now('PRC')->addMonth()->startOfMonth()->toDateString(),
                    'created_at'          => now(),
                    'updated_at'          => now(),
                ];

                ModelsChargebackPenalty::query()->insert($penaltyInsert);
            }
        }
    }
}
