<?php

namespace App\Classes\Pay\Gateways\Gee;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Gee;

class RetrieveChargebackCaseGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $data      = [
            'reqTime'  => (string) time(),
            'version'  => '1.0',
            'mchNo'    => $payConfig['mchNo'],
            'appId'    => $payConfig['appId'],
            'reqId'    => $payload['order_id'],
            'state'    => '0',
            'signType' => 'RSA',
        ];

        // 配置服务商号
        if (isset($payConfig['isvNo'])) {
            $data['isvNo'] = $payConfig['isvNo'];
        }

        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 生成签名
        $data['sign'] = Support::sign($data, $payConfig['privateKey']);

        $header =[
            'Content-Type' => 'application/json',
        ];

        $post = [
            'transitIp' => $transitIp,
            'header'    => $header,
            'data'      => $data
        ];

        return Gee::retrieveChargebackCaseParser(Support::requestApi('/pay/unifiedInfo/cbAlertQuery', $post, 'post', $payload['order_id']));
    }
}
