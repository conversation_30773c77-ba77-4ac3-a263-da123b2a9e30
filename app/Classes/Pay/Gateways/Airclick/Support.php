<?php

namespace App\Classes\Pay\Gateways\Airclick;

use App\Classes\Pay\Gateways\Airclick;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Airclick';
        $this->baseUri     = Airclick::URL[$config->get('mode', Airclick::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param array $data
     * @param string $endpoint
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
    {
        $logData = Support::handleLogData($data);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId
        ));
       
        $result = self::$instance->post($endpoint, $data);
        
        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }
    
    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['CardExpireMonth'], $data['CardExpireYear'], $data['CardSecurityCode'], $data['CardNo'])) {
            $data['CardExpireMonth']  = get_mark_data($data['CardExpireMonth']);
            $data['CardExpireYear']   = get_mark_data($data['CardExpireYear']);
            $data['CardSecurityCode'] = get_mark_data($data['CardSecurityCode']);
            $data['CardNo']           = get_markcard($data['CardNo']); 
        }

        return $data;
    }
}
