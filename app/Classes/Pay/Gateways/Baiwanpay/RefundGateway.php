<?php

namespace App\Classes\Pay\Gateways\Baiwanpay;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Baiwanpay;

class RefundGateway extends Gateway
{
	/**
	 * Pay an order.
	 *
	 * @param string $endpoint
	 *
	 * @param array $payload
	 * @return Collection
	 */
	public function pay($endpoint, array $payload)
	{
		$payConfig    = $payload['pay_config'];
		$paymentOrder = $payload['payment_order'];

		$data = array(
			'merchantNo' => $payConfig['merchantNo'],
			'terminalNo' => $payConfig['terminalNo'],
			'tradeNo'    => $paymentOrder['payment_order_id'],
			'amount'     => Support::amount_format($payload['payment_refund']['amount']),
			'currency'   => $payload['payment_refund']['currency'],
		);

		$signStr               = implode('', $data) . $payConfig['signKey'];
		$data['securityValue'] = hash('sha256',$signStr);

		return Baiwanpay::refundParser(Support::requestApi('after-sales/refund.json', $data, $payload['refund_id']));
	}
}
