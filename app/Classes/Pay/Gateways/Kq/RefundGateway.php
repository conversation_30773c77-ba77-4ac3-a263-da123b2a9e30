<?php

namespace App\Classes\Pay\Gateways\Kq;

use App\Classes\Pay\Parser\Kq;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig     = $payload['pay_config'];
        $paymentOrder  = $payload['payment_order'];
        $paymentRefund = $payload['payment_refund'];
        
        // 组装退款数据
        $head = [
            'version'           => '1.0.0',
            'messageType'       => 'A9007',
            'memberCode'        => $payConfig['memberCode'],
            'externalRefNumber' => $paymentRefund['refund_id'],
        ];
        
        $dataTemp = json_encode([
            'merchantId'    => $payConfig['merchantId'],
            'terminalId'    => $payConfig['terminalId'],
            'origRefNumber' => $paymentOrder['payment_order_id'],
            'entryTime'     => date('YmdHis', strtotime($paymentOrder['created_at'])),
            'amount'        => Support::amount_format($paymentRefund['amount'] * 100, 0), //支付金额 （单位：分，整数)
        ], JSON_UNESCAPED_UNICODE);

        $responseBody = [
            'signedData'    => Support::sign($dataTemp),
            'envelopedData' => Support::enveloped($dataTemp),
        ];
        
        $header = [
            'Content-Type' => 'application/json;charset=utf-8',
        ];

        $post = [
            'json'    => [
                'head'         => $head,
                'requestBody' => $responseBody
            ],
            'headers' => $header,
        ];

        return Kq::refundParser(Support::requestApi('', $post, $payload['order_id']));
    }
}
