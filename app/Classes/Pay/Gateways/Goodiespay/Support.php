<?php

namespace App\Classes\Pay\Gateways\Goodiespay;

use App\Classes\Pay\Gateways\Goodiespay;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;


/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Goodiespay';
        $this->baseUri = Goodiespay::URL[$config->get('mode', Goodiespay::MODE_NORMAL)];
        $this->config = $config;
        $this->setHttpOptions();
    }

	/**
	 * Get API result.
	 *
	 * @param array $data
	 * @param string $endpoint
	 * @param string $type
	 * @param string $requestId
	 * @return Collection
	 */
	public static function requestApi(string $endpoint, array $data, $type = '', string $requestId = '' ): Collection
	{
        $logData = Support::handleLogData($data);
        
		Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId
        ));

		switch ($type) {
			case 'json':
				$result = self::$instance->post($endpoint, '', ['json' => $data]);
			break;
			default:
				$result = self::$instance->post($endpoint, $data);
		}

		Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

		return self::processingApiResult($result);
	}
	
	/**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['cardInfo.cardnum'], $data['cardInfo.cvv2'], $data['cardInfo.month'], $data['cardInfo.year'])) {
            $data['cardInfo.year']    = get_mark_data($data['cardInfo.year']);
            $data['cardInfo.month']   = get_mark_data($data['cardInfo.month']);
            $data['cardInfo.cvv2']    = get_mark_data($data['cardInfo.cvv2']);
            $data['cardInfo.cardnum'] = get_markcard($data['cardInfo.cardnum']);
        }

        return $data;
    }
}
