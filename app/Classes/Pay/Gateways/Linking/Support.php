<?php

namespace App\Classes\Pay\Gateways\Linking;

use App\Classes\Pay\Gateways\Linking;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Linking';
        $this->baseUri     = Linking::URL[$config->get('mode', Linking::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, $method, string $requestId = ''): Collection
    {
        // 设置中转请求渠道地址
        $tempUrl = !empty($data['transitIp']) ? $data['transitIp'] : self::$instance->getBaseUri() . $endpoint;
        $logData = Support::handleLogData($data);
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$tempUrl, $logData],
            $requestId,
            'info'
        ));

        $result = [];
        try {
            if (!empty($data['transitIp'])) {
                // 转发到中转
                $methodArr = [
                    'post' => 'post',
                ];

                $data['embMethod'] = $methodArr[$method];
                $data['doUrl']     = self::$instance->getBaseUri() . $endpoint;

                $result = self::$instance->post($tempUrl, $data);

                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }
            } else {
                $result = self::$instance->$method($tempUrl, $data['data']);
            }
        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                $e->getMessage(),
                [],
                $requestId,
                'error'
            ));
        }

        if (isset($result['message']) && preg_match('/\b\d{14,19}\b/', $result['message'], $matches)) {
            // 渠道返回内容有卡号替换为卡掩码
            $cardNumber = $matches[0];
            // 替换中间部分为XXXXXX
            $maskedCardNumber = get_markcard($cardNumber);
            // 替换原字符串中的卡号
            $result['message'] = str_replace($cardNumber, $maskedCardNumber, $result['message']);
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', $tempUrl, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * MD5签名
     *
     * @param $string
     * @return string
     */
    public static function sign($string)
    {
        return strtoupper(md5($string));
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['data'] ?? [];
        if (isset($card['cardNo'], $card['cardExpireMonth'], $card['cardExpireYear'], $card['cardSecurityCode'])) {
            $card                     = &$data['data'];
            $card['cardExpireMonth']  = get_mark_data($card['cardExpireMonth']);
            $card['cardExpireYear']   = get_mark_data($card['cardExpireYear']);
            $card['cardSecurityCode'] = get_mark_data($card['cardSecurityCode']);
            $card['cardNo']           = get_markcard($card['cardNo']);
        }

        return $data;
    }

    /**
     * 金额格式处理
     *
     * @param $amount
     * @param $currency
     * @return string
    */
    public static function amountHandle($amount, $currency): string
    {
        //金额精确到元币种
        $currencyData = [
            'JPY',
        ];

        if (in_array($currency, $currencyData)) {
            $amount   = ceil($amount);
            $decimals = 0;
        } else {
            $decimals = 2;
        }

        return self::amount_format($amount, $decimals);
    }
}
