<?php

namespace App\Classes\Pay\Gateways\Inno;

use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Parser\Inno;
use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use Ramsey\Uuid\Uuid;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     * @param array $payload
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payAddress  = $payload['address'];
        $payCard     = $payload['card'];
        $payProducts = $payload['products'];

        // 年份处理
        if (strlen($payCard['expiration_year']) == 2) {
            $payCard['expiration_year'] = substr(date('Y'), 0, 2) . $payCard['expiration_year'];
        }

        // 商品信息
        $product = [];
        foreach ($payProducts as $vo) {
            $product[] = [
                'productNo'   => $vo['sku'],
                'description' => $vo['name'],
                'currency'    => 'USD',
                'quantity' => (string)$vo['qty'],
                'amount'      => $vo['price'],
            ];
        }

        // 缓存key
        $mark      = MD5($payOrder['order_id'] . 'inno');
        $markArr   = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Inno',
        ];

        Cache::add($mark, $markArr, 24 * 60 * 60);

        $amount = Support::amount_format($payOrder['amount']);
        $data   = [
            'outOrderNo'    => $payOrder['order_id'],
            'orderCurrency' => $payOrder['currency'],
            'orderAmount'   => $amount,
            'paymentMethod' => 'CR01',
            'transType' => '00',
            'ipAddress'     => $payAddress['ip'],
            'extra'         => [
                'card'     => [
                    'cardHolder'  => $payAddress['bill_name'],
                    'cardNumber'  => $payCard['card_number'],
                    'expiryYear'  => $payCard['expiration_year'],
                    'expiryMonth' => $payCard['expiration_month'],
                    'cvvCode'     => $payCard['cvv'],
                ],
                'billing'  => [
                    'firstName' => $payAddress['bill_first_name'],
                    'lastName'  => $payAddress['bill_last_name'],
                    'email'     => $payAddress['bill_email'],
                    'phone'     => $payAddress['bill_phone'],
                    'country'   => $payAddress['bill_country_isoa2'],
                    'city'      => $payAddress['bill_city'],
                    'address'   => $payAddress['bill_address'],
                    'zipCode'   => $payAddress['bill_postcode'],
                ],
                'shipping' => [
                    'firstName' => $payAddress['ship_first_name'],
                    'lastName'  => $payAddress['ship_last_name'],
                    'email'     => $payAddress['ship_email'],
                    'phone'     => $payAddress['ship_phone'],
                    'country'   => $payAddress['ship_country_isoa2'],
                    'city'      => $payAddress['ship_city'],
                    'address'   => $payAddress['ship_address'],
                    'zipCode'   => $payAddress['ship_postcode'],
                ],
                'products' => $product,
            ],
        ];

        $transitIp = '';
        if (!empty($payConfig['transitUrl']) && !empty($payConfig['transitIp'])) {
            // 有中转网址
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 生成签名
        $nonceStr = (string)Uuid::uuid4(); // uuid
        $token    = Support::getAccessToken($payConfig, $endpoint);

        $header = [
            'Authorization' => 'Bearer ' . $token['access_token'],
            'Content-Type'  => 'application/json',
            'secretId'      => $payConfig['secretId'],
            'nonceStr'      => $nonceStr,
            'signature'     => Support::sign($data, $nonceStr, $payConfig),
        ];

        // 测试环境
        if ($payConfig['mode'] == 'dev') {
            $header['sandbox'] = 'sandbox';
        }

        $post = [
            'transitIp' => $transitIp,
            'data'      => $data,
            'header'    => $header,
        ];

        return Inno::authParser(Support::requestApi('/transactions', $post, 'post', $payload['order_id']));
    }
}
