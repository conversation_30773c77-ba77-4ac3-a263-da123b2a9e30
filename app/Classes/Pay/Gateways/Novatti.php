<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Novatti\Support;
use App\Classes\Supports\Config;

class <PERSON>tti extends Gateway
{
    protected $gatewayName = 'Novatti';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://api.novattipayments.com/',
        self::MODE_DEV    => 'https://test-api.novattipayments.com/',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
