<?php

namespace App\Classes\Pay\Gateways\Authorize;

use App\Classes\Pay\Gateways\Authorize;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;
use Psr\Http\Message\ResponseInterface;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Authorize';
        $this->baseUri     = Authorize::URL[$config->get('mode', Authorize::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
    {
        $logData = Support::handleLogData($data);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $logData));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId
        ));

        try {
            $result = self::$instance->post($endpoint,[], ['json' => $data['data'], 'headers' => $data['header']]);
        } catch (\Exception $e) {
            $result=[];
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * Convert response.
     *
     * @return array|string
     */
    public function unwrapResponse(ResponseInterface $response)
    {
        $contentType = $response->getHeaderLine('Content-Type');
        $contents = $response->getBody()->getContents();

        if (false !== stripos($contentType, 'json') || stripos($contentType, 'javascript')) {
            return json_decode(trim($contents,chr(239).chr(187).chr(191)),true);
        }

        return $contents;
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        $card = $data['data']['createTransactionRequest']['transactionRequest']['payment']['creditCard'] ?? [];
        
        if (isset($card['expirationDate'], $card['cardCode'], $card['cardNumber'])) {
            $card                   = &$data['data']['createTransactionRequest']['transactionRequest']['payment']['creditCard'];
            $card['expirationDate'] = get_mark_data($card['expirationDate']);
            $card['cardCode']       = get_mark_data($card['cardCode']);
            $card['cardNumber']     = get_markcard($card['cardNumber']); 
        }

        return $data;
    }
}
