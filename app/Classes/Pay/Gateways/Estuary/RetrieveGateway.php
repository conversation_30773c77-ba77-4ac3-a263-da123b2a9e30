<?php

namespace App\Classes\Pay\Gateways\Estuary;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Estuary;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 组装支付数据

        $data = [
            'payOrdNo'     => $payload['order_id'],
            'merchantId'   => $payConfig['merchant_id'],
            'payChannelNo' => $payConfig['pay_channel_no'],
        ];

        $signature = Support::sign($data, $payConfig['privateKey']);

        // 组装请求头
        $header = array(
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagency_code'],
        );

        $post = array(
            'data'   => $data,
            'header' => $header,
        );

        return Estuary::retrieveParser(Support::requestApi('/mhlapi/bussiness/unionintl-api/queryPayStatus', $post, $payload['order_id']));
    }
}
