<?php

namespace App\Classes\Pay\Gateways\Bill;

use App\Classes\Pay\Events;
use App\Classes\Pay\Exceptions\GatewayException;
use App\Classes\Pay\Exceptions\InvalidSignException;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Gateways\Bill;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string signKey
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Bill';
        $this->baseUri     = Bill::URL[$config->get('mode', Bill::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     * @throws GatewayException
     * @throws InvalidSignException
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
    {
        $logData = Support::handleLogData($data);

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, Support::handleLogData($data)));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $logData], 
            $requestId
        ));

        $result = [];

        try {

            $result = self::$instance->post($endpoint, $data, ['headers' => ['Content-Type' => 'application/x-www-form-urlencoded']]);

        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 签名
     *
     * @param $data
     * @param $privateKey
     * @return string
     */
    public static function sign($data, $privateKey)
    {
        ksort($data);
        $arr = array();

        foreach ($data as $key => $value) {
            if (trim($value) != "") {
                $arr[] = $key . '=' . $value;
            }
        }

        return hash("sha256", implode('&', $arr) . '&key=' . $privateKey);
    }

//    /**
//     * Verify sign.
//     *
//     * @param array $data
//     * @return bool
//     */
//    public static function verifySign(array $data): bool
//    {
//        $sign = $data['sign'];
//        unset($data['sign']);
//
//        return self::sign($data, self::$instance->signKey) === $sign;
//    }

    /**
     * 毫秒数时间戳
     *
     * @return false|string
     */
    public static function timestamp()
    {
        return substr(str_replace('.', '', microtime(true)), 0, 13);
    }
    
    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['year'], $data['month'], $data['cvv'], $data['cardNo'])) {
            $data['year']   = get_mark_data($data['year']);
            $data['month']  = get_mark_data($data['month']);
            $data['cvv']    = get_mark_data($data['cvv']);
            $data['cardNo'] = get_markcard($data['cardNo']); 
        }

        return $data;
    }
}
