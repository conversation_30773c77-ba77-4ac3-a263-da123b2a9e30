<?php

namespace App\Classes\Pay\Gateways;

use App\Classes\Pay\Contracts\Gateway;
use App\Classes\Pay\Gateways\Austpay\Support;
use App\Classes\Supports\Config;

class Austpay extends Gateway
{
    protected $gatewayName = 'Austpay';

    /**
     * Const url.
     */
    const URL = [
        self::MODE_NORMAL => 'https://www.austpay.biz',
        self::MODE_DEV    => 'https://www.austpay.biz',
    ];

    public function __construct(Config $config)
    {
        $this->gateway = Support::create($config)->getBaseUri();
        parent::__construct($config);
    }
}
