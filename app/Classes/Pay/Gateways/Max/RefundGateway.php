<?php

namespace App\Classes\Pay\Gateways\Max;

use App\Classes\Pay\Parser\Max;
use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use Illuminate\Support\Facades\Cache;

class RefundGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        // 缓存key
        $mark    = MD5($payload['refund_id'] . 'max');
        $markArr = [
            'order_id'  => $payload['order_id'],
            'refund_id' => $payload['refund_id'],
            'class'     => 'Max',
        ];
        Cache::add($mark, $markArr, 30 * 24 * 60 * 60);

        $amount = Support::amount_format($payload['payment_refund']['amount']);
        $data   = array(
            'merchant_id'     => $payConfig['merchant_id'],
            'merch_order_id'  => $payload['payment_refund']['order_number'],
            'order_id'        => $payload['payment_order']['payment_order_id'],
            'refund_amount'   => $amount,
            'mer_refund_id'   => $payload['refund_id'],
            'refund_url_sync' => route('api.v1.notify', ['mark' => $mark], true),
            'signature'       => ''
        );

        $data['signature'] = md5($payConfig['order_hash'] . $data['merchant_id'] . $data['merch_order_id'] . $data['order_id'] . $data['refund_amount'] . $data['mer_refund_id']);

        return Max::refundParser(Support::requestApi('/Payment/payRefAPI.aspx', $data, 'get', $payload['refund_id']));
    }
}
