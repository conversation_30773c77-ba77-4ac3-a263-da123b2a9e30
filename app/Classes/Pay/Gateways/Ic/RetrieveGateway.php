<?php

namespace App\Classes\Pay\Gateways\Ic;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Parser\Ic;

class RetrieveGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $payOrder  = $payload['payment_order'];

        // 组装查询数据
        $data = [
            'orderNo' => $payOrder['order_id'],
        ];

        $post = [
            'appid'    => $payConfig['appid'],
            'reqTrx'   => Support::getRandomString(),
            'dateTime' => now('PRC')->format('YmdHis'),
            'service'  => 'QUERY',
            'charset'  => 'UTF-8',
            'signType' => 'MD5',
            'format'   => 'JSON',
            'content'  => Support::aesEncrypt(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE), $payConfig['key'])
        ];
        
        // MD5加密
        $sign         = Support::sign($post, $payConfig['key']);
        $post['sign'] = $sign;

        return Ic::retrieveParser(Support::requestApi('', $post, $payload['order_id']));
    }
}
