<?php

namespace App\Classes\Pay\Gateways\Fdc;

use App\Classes\Pay\Gateways\Fdc;
use App\Classes\Supports\Collection;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Fdc';
        $this->baseUri     = Fdc::URL[$config->get('mode', Fdc::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '' ): Collection
    {
        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Requesting To Api',
            [self::$instance->getBaseUri() . $endpoint, $data], 
            $requestId
        ));
        
        $result = [];

        try {

            $result = self::$instance->post($endpoint, [], ['json' => $data['data'], 'headers' => $data['header']]);

        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName, 
                $e->getMessage(),
                [], 
                $requestId,
                'error'
            ));
        }
        
        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName, 
            'Result Of Api',
            $result, 
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * 签名
     *
     * @param $data
     * @param $privateKey
     * @return string
     */
    public static function sign(&$data, $privateKey)
    {
        return self::genSign(self::genSignContent($data), $privateKey);
    }


    /**
     * 生成签名内容
     *
     * @param $req
     * @return string
     */
    private static function genSignContent(&$req)
    {
        $arr  = array($req);
        $strs = array();

        ksort($arr);
        self::items(0, $arr, $strs);
        $msg = implode('&', $strs);

        return $msg;
    }

    /**
     * 递归深度优先排序
     *
     * @param $x
     * @param $y
     * @param $strs
     */
    private static function items($x, $y, &$strs)
    {
        if ($y == null) {
            return;
        }

        if (is_array($y)) {
            ksort($y);
            foreach ($y as $key => $value) {
                self::items($key, $value, $strs);
            }

            return;
        }

        $strs[] = $x . '=' . $y;
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign, $privateKey)
    {
        //这里他是拼接成和pem文件一样的格式
        $privateKey       = "-----BEGIN RSA PRIVATE KEY-----\n" .
            wordwrap($privateKey, 64, "\n", true) .
            "\n-----END RSA PRIVATE KEY-----";
        $binary_signature = "";
        $algo             = "SHA256";
        openssl_sign($toSign, $binary_signature, $privateKey, $algo);
        $sign = base64_encode($binary_signature);
        return $sign;
    }
    
    /**
     * 加密
     *
     * @param $str
     * @param $key
     * @return string
     */
    public static function encryptNew($str, $key,$iv)
    {
        return base64_encode(openssl_encrypt($str, "AES-128-CBC", $key, OPENSSL_RAW_DATA,$iv));
    }

}
