<?php

namespace App\Classes\Pay\Gateways\Pp;

use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Pp;
use App\Classes\Supports\Collection;
use App\Models\TrackLibrary;
use Illuminate\Support\Facades\Cache;

class AuthGateway extends Gateway
{
    protected $_stateData = [
        'ONTARIO'                   => 'ON',
        'QUÉBEC'                    => 'QC/PQ',
        'QUEBEC'                    => 'QC/PQ',
        'NOVA SCOTIA'               => 'NS',
        'NEW BRUNSWICK'             => 'NB',
        'MANITOBA'                  => 'MB',
        'BRITISH COLUMBIA'          => 'BC',
        'PRINCE EDWARD ISLAND'      => 'PE',
        'ALBERTA'                   => 'AB',
        'SASKATCHEWAN'              => 'SK',
        'NEWFOUNDLAND AND LABRADOR' => 'NL',

        'ALABAMA'        => 'AL',
        'ALASKA'         => 'AK',
        'ARIZONA'        => 'AZ',
        'ARKANSAS'       => 'AR',
        'CALIFORNIA'     => 'CA',
        'COLORADO'       => 'CO',
        'CONNECTICUT'    => 'CT',
        'DELAWARE'       => 'DE',
        'FLORIDA'        => 'FL',
        'GEORGIA'        => 'GA',
        'HAWAII'         => 'HI',
        'IDAHO'          => 'ID',
        'ILLINOIS'       => 'IL',
        'INDIANA'        => 'IN',
        'IOWA'           => 'IA',
        'KANSAS'         => 'KS',
        'KENTUCKY'       => 'KY',
        'LOUISIANA'      => 'LA',
        'MAINE'          => 'ME',
        'MARYLAND'       => 'MD',
        'MASSACHUSETTS'  => 'MA',
        'MICHIGAN'       => 'MI',
        'MINNESOTA'      => 'MN',
        'MISSISSIPPI'    => 'MS',
        'MISSOURI'       => 'MO',
        'MONTANA'        => 'MT',
        'NEBRASKA'       => 'NE',
        'NEVADA'         => 'NV',
        'NEW HAMPSHIRE'  => 'NH',
        'NEW JERSEY'     => 'NJ',
        'NEW MEXICO'     => 'NM',
        'NEW YORK'       => 'NY',
        'NORTH CAROLINA' => 'NC',
        'NORTH DAKOTA'   => 'ND',
        'OHIO'           => 'OH',
        'OKLAHOMA'       => 'OK',
        'OREGON'         => 'OR',
        'PENNSYLVANIA'   => 'PA',
        'RHODE ISLAND'   => 'RI',
        'SOUTH CAROLINA' => 'SC',
        'SOUTH DAKOTA'   => 'SD',
        'TENNESSEE'      => 'TN',
        'TEXAS'          => 'TX',
        'UTAH'           => 'UT',
        'VERMONT'        => 'VT',
        'VIRGINIA'       => 'VA',
        'WASHINGTON'     => 'WA',
        'WEST VIRGINIA'  => 'WV',
        'WISCONSIN'      => 'WI',
        'WYOMING'        => 'WY',
    ];

    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];
        $address   = $payload['address'];
        $card      = $payload['card'];
        $products  = $payload['products'];

        $trackLibrary = TrackLibrary::where('merchant_id', $payload['merchant_id'])->where('status', TrackLibrary::ENABLE)->first();
        if (!empty($trackLibrary['ship_first_name'])) {
            $address['bill_first_name']    = $trackLibrary['ship_first_name'];
            $address['bill_last_name']     = $trackLibrary['ship_last_name'];
            $address['bill_country_isoa2'] = $trackLibrary['ship_country_isoa2'];
            $address['bill_address']       = $trackLibrary['ship_address'];
            $address['bill_city']          = $trackLibrary['ship_city'];
            $address['bill_state']         = $trackLibrary['ship_state'];
            $address['bill_phone']         = $trackLibrary['ship_phone'];
            $address['bill_postcode']      = $trackLibrary['ship_postcode'];

            $addressMark = MD5($payload['order_id'] . 'pp' . 'order_address');
            Cache::add($addressMark, $trackLibrary, 24 * 60 * 60);
            $trackLibrary->status = TrackLibrary::NOT_ENABLE;
            $trackLibrary->save();
        }

        // 组装支付数据
        $goods = [];

        foreach ($products as $product) {
            $goods[] = [
                'name'             => $product['name'],
                'sku'              => $product['sku'],
                'averageUnitPrice' => $product['price'],
                'number'           => $product['qty'],
                'imgUrl'           => $product['url'],
            ];
        }

        //美国加拿大地区二字码
        if (in_array($address['bill_country_isoa2'], ['US', 'CA'])) {
            $address['bill_state'] = $this->_stateData[mb_strtoupper($address['bill_state'])] ?? $address['bill_state'];
        }

        $riskInfo = Support::randomRiskInfo();

        // 缓存key
        $mark    = MD5($payload['order_id'] . 'pp');
        $markArr = [
            'order_id' => $payload['order_id'],
            'class'    => 'Pp',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        $amount = Support::amount_format($payload['payment_order']['amount']);
        $post   = [
            'accId'                 => $payConfig['accId'],
            'signType'              => 'MD5',
            'sign'                  => '',
            'amount'                => $amount,
            'currency'              => $payload['payment_order']['currency'],
            'merchantTransactionId' => $payload['order_id'],
            'paymentType'           => 'SALE',
            'threeDSecure'          => 'N',
            'notificationUrl'       => route('api.v1.asyncNotify', ['mark' => $mark], true),
            'payMethodInfo'         => [
                'card' => [
                    'cvv'             => $card['cvv'],
                    'expireMonth'     => $card['expiration_month'],
                    'expireYear'      => '20' . $card['expiration_year'],//补20
                    'holderFirstName' => $address['bill_first_name'],
                    'holderLastName'  => $address['bill_last_name'],
                    'number'          => $card['card_number']
                ]
            ],
            'riskInfo'              => [
                'customer'    => [
                    'firstName'  => $address['bill_first_name'],
                    'lastName'   => $address['bill_last_name'],
                    'email'      => $address['bill_email'],
                    'phone'      => $address['bill_phone'],
                    'orderTime'  => date('YmdHms', strtotime($payload['created_at'])),
                    'orderIp'    => $address['ip'],
                    'payIp'      => $address['ip'],
                    'payCountry' => $address['bill_country_isoa2'],
                ],
                'shipping'    => [
                    'firstName' => $address['bill_first_name'],
                    'lastName'  => $address['bill_last_name'],
                    'phone'     => $address['bill_phone'],
                    'email'     => $address['bill_email'],
                    'street'    => $address['bill_address'],
                    'postcode'  => $address['bill_postcode'],
                    'city'      => $address['bill_city'],
                    'state'     => $address['bill_state'],
                    'country'   => $address['bill_country_isoa2'],
                ],
                'billing'     => [
                    'firstName' => $address['bill_first_name'],
                    'lastName'  => $address['bill_last_name'],
                    'phone'     => $address['bill_phone'],
                    'email'     => $address['bill_email'],
                    'street'    => $address['bill_address'],
                    'postcode'  => $address['bill_postcode'],
                    'city'      => $address['bill_city'],
                    'state'     => $address['bill_state'],
                    'country'   => $address['bill_country_isoa2']
                ],
                'goods'       => $goods,
                'eCommerce'   => $riskInfo['eCommerce'],
                'device'      => $riskInfo['device'],
                'browserInfo' => $riskInfo['browserInfo']
            ]
        ];

        $md5Arr = [
            'accId'                 => $post['accId'],
            'signType'              => $post['signType'],
            'amount'                => $post['amount'],
            'currency'              => $post['currency'],
            'merchantTransactionId' => $post['merchantTransactionId'],
            'notificationUrl'       => $post['notificationUrl']
        ];

        $post['sign'] = Support::createSign($md5Arr, trim($payConfig['salt']));

        return Pp::authParser(Support::requestApi('/v2/payment', $post, $payload['order_id']));
    }
}
