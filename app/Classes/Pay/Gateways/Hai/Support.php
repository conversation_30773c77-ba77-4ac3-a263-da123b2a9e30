<?php

namespace App\Classes\Pay\Gateways\Hai;

use App\Classes\Pay\Gateways\Hai;
use App\Classes\Supports\Config;
use App\Classes\Pay\Contracts\Support as BaseSupport;
use App\Classes\Pay\Events;
use App\Classes\Pay\Log;
use App\Classes\Supports\Collection;

/**
 * @property array http  http options
 * @property string mode current mode
 * @property array log   log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Instance.
     *
     * @var Support
     */
    private static $instance;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Hai';
        $this->baseUri     = Hai::URL[$config->get('mode', Hai::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }

    /**
     * Get API result.
     *
     * @param string $endpoint
     * @param array $data
     * @param string $requestId
     * @param array $customize
     * @return Collection
     */
    public static function requestApi(string $endpoint, array $data, string $requestId = '', array $customize = []): Collection
    {
        $doUrl   = self::$instance->getBaseUri() . $endpoint; // 渠道最终请求地址
        $tempUrl = $data['transit_ip'] ?: $doUrl; // 当前发起的请求地址，有中转则走中转

        Events::dispatch(new Events\ApiRequesting(self::$gatewayName, '', $tempUrl, $data));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Requesting To Api',
            [$tempUrl, $data],
            $requestId,
            'info',
            self::handleLogData($customize)
        ));

        $result = [];

        try {
            if (!empty($data['transit_ip'])) {
                // 转发到中转
                $data['embMethod'] = 'curlPost';
                $data['doUrl']     = $doUrl;

                if (!empty($data['header'])) {
                    $header = [];
                    foreach ($data['header'] as $key => $vo) {
                        $header[] = $key . ':' . $vo;
                    }

                    $data['header'] = $header;
                }

                $result = self::$instance->post($data['transit_ip'], $data);

                if (gettype($result) === 'string') {
                    $result = json_decode($result, true) ?? [];
                }

            } else {
                $result = self::$instance->post($endpoint, [], ['json' => $data['data'], 'headers' => $data['header']]);
            }

        } catch (\Exception $e) {
            // 记录日志
            Log::error($e->getMessage());
            Events::dispatch(new Events\SlsLog(
                self::$gatewayName,
                $e->getMessage(),
                [],
                $requestId,
                'error'
            ));
        }

        Events::dispatch(new Events\ApiRequested(self::$gatewayName, '', self::$instance->getBaseUri() . $endpoint, $result));
        Events::dispatch(new Events\SlsLog(
            self::$gatewayName,
            'Result Of Api',
            $result,
            $requestId
        ));

        return self::processingApiResult($result);
    }

    /**
     * AES/CBC/PKCS5Padding Encrypter
     *
     * @param $str
     * @param $key
     * @param string $iv
     * @return string
     */
    public static function encryptNew($str, $key, string $iv = "0123456789ABCDEF"): string
    {
        return base64_encode(openssl_encrypt($str, "AES-128-CBC", $key, OPENSSL_RAW_DATA, $iv));
    }

    /**
     * 签名
     *
     * @param $data
     * @param $privateKey
     * @return string
     */
    public static function sign(&$data, $privateKey)
    {
        return self::genSign(self::genSignContent($data), $privateKey);
    }


    /**
     * 生成签名内容
     *
     * @param $req
     * @return string
     */
    private static function genSignContent(&$req): string
    {
        $arr  = array($req);
        $strs = array();

        ksort($arr);
        self::items(0, $arr, $strs);
        $msg = implode('&', $strs);

        return $msg;
    }

    /**
     * 递归深度优先排序
     *
     * @param $x
     * @param $y
     * @param $stars
     */
    private static function items($x, $y, &$stars)
    {
        if ($y == null) {
            return;
        }

        if (is_array($y)) {
            ksort($y);
            foreach ($y as $key => $value) {
                self::items($key, $value, $stars);
            }

            return;
        }

        $stars[] = $x . '=' . $y;
    }

    /**
     * 生成签名
     *
     * @param $toSign
     * @param $privateKey
     * @return string
     */
    public static function genSign($toSign, $privateKey): string
    {
        //这里他是拼接成和pem文件一样的格式
        $privateKey       = "-----BEGIN RSA PRIVATE KEY-----\n" .
                            wordwrap($privateKey, 64, "\n", true) .
                            "\n-----END RSA PRIVATE KEY-----";
        $binary_signature = "";
        $algo             = "SHA256";
        openssl_sign($toSign, $binary_signature, $privateKey, $algo);
        $sign = base64_encode($binary_signature);
        return $sign;
    }

    /**
     * 处理日志数据
     *
     * @param array $data
     * @return array
     */
    public static function handleLogData($data)
    {
        if (isset($data['tradeCardNo'], $data['tradeCardCvv'], $data['tradeCardExpirationDate'])) {
            $data['tradeCardExpirationDate'] = get_mark_data($data['tradeCardExpirationDate']);
            $data['tradeCardCvv']            = get_mark_data($data['tradeCardCvv']);
            $data['tradeCardNo']             = get_markcard($data['tradeCardNo']);
        }

        return $data;
    }
}
