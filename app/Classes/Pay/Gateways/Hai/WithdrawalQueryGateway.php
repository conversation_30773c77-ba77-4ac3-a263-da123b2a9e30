<?php

namespace App\Classes\Pay\Gateways\Hai;

use App\Classes\Supports\Collection;
use App\Classes\Pay\Events;
use App\Classes\Pay\Parser\Hai;

class WithdrawalQueryGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig = $payload['pay_config'];

        $data = [
            'platformMerchantId' => $payConfig['merchant_id'],
            'withdrawNo'         => $payload['order_id'],
        ];

        $signature = Support::sign($data, $payConfig['privateKey']);

        $transitIp = '';
        if (!empty($payConfig['transitIp'])) {
            // 走中转
            $transitIp = $payConfig['transitIp'] . '?' . Support::urlPayment($payConfig);
        }

        // 组装请求头
        $header = array(
            'sign'         => $signature,
            'coagencyCode' => $payConfig['coagency_code'],
            'Content-Type' => 'application/json',
        );

        $post = array(
            'data'       => $data,
            'header'     => $header,
            'transit_ip' => $transitIp,
        );

        return Hai::withdrawalQueryParser(Support::requestApi('/mhlapi/bussiness/bussinessMcip-api/withdrawQuery', $post, $payload['order_id']));
    }
}
