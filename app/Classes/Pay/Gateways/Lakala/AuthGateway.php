<?php

namespace App\Classes\Pay\Gateways\Lakala;

use App\Classes\Supports\Collection;
use Illuminate\Support\Facades\Cache;
use App\Classes\Pay\Parser\Lakala;
use App\Services\Sm4HelperService;

class AuthGateway extends Gateway
{
    /**
     * Pay an order.
     *
     * @param string $endpoint
     *
     * @param array $payload
     * @return Collection
     */
    public function pay($endpoint, array $payload): Collection
    {
        $payConfig   = $payload['pay_config'];
        $payOrder    = $payload['payment_order'];
        $payCard     = $payload['card'];
        $payAddress  = $payload['address'];
        $payProducts = $payload['products'];

        // 缓存key
        $mark    = MD5($payOrder['order_id'] . 'lakala');
        $markArr = [
            'order_id' => $payOrder['order_id'],
            'class'    => 'Lakala',
        ];
        Cache::add($mark, $markArr, 24 * 60 * 60);

        //商品信息
        $goodsDetail = [
            'goods_name'           => 'generalGoods',
            'goods_pricing_unit'   => '2',
            'goods_price'          => Support::amount_format($payOrder['amount'] * 100, 0),
            'goods_type'           => '5',
            'goods_number'         => '1',
            'goods_desc'           => 'generalGoods',
            'commercelatform_type' => '2',
            'commercelatform_name' => $payConfig['commercelatformName']
        ];

        $time = time();
        $date = date('YmdHis', $time);
        $data = [
            'req_time' => $date,
            'version'  => '3.0',
            'req_data' => [
                'merchant_no'   => $payConfig['merchantNo'],
                'term_no'       => $payConfig['termNo'],
                'out_trade_no'  => $payload['order_id'],
                'total_amount'  => Support::amount_format($payOrder['amount'] * 100, 0),   //支付金额 （单位：分，整数),,
                'notify_url'    => \route('api.v1.asyncNotify', ['mark' => $mark], true),
                'device_type'   => 'Web',
                'card_info'     => [
                    'card_number' => $payCard['card_number'],
                    'expiry_date' => $payCard['expiration_year'] . $payCard['expiration_month'],
                    'cvc'         => $payCard['cvv'],
                ],
                'location_info' => [
                    'request_ip' => $payAddress['ip']
                ],
                'return_url'    => \route('api.v1.syncNotify', ['mark' => $mark], true),
                'origin_url'    => $payConfig['originUrl'],
                'platform'      => 'WEB',
                'user_info'     => [
                    'user_agent'           => Support::randomBrowerInfo(),
                    'user_email'           => $payAddress['bill_email'],
                    'user_id'              => rand(10000, 99999),
                    'user_acc_create_date' => $date
                ],
                'goods_detail' => $goodsDetail
            ],
        ];

        $body = json_encode($data);
        $sm4  = new Sm4HelperService();
        $body = $sm4->encrypt(base64_decode($payConfig['sm4Key']), $body);

        //生成签名
        $authorization = Support::getAuthorization($body, $payConfig, $time);

        //组装请求头
        $header = [
            'Content-Type'  => 'application/json',
            'Authorization' => $authorization,
        ];

        $post = [
            'data'      => $body,
            'header'    => $header,
            'plaintext' => Support::handleLogData($data)
        ];

        return Lakala::authParser(Support::requestApi('/api/v3/ips/trans/wcard/noCardOnlinePay', $post, $payload['order_id']));
    }
}