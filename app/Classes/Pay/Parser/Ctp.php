<?php

namespace App\Classes\Pay\Parser;


use App\Classes\Pay\Pay;
use App\Classes\Supports\Arr;
use App\Classes\Supports\Collection;
use App\Models\Channel;
use App\Models\Order;
use App\Models\Refund;

class Ctp extends BaseParser
{
    protected static $_supplierName = 'Ctp';

    public static function _authParser($params): Collection
    {
        $status = Order::STATUS_PENDING;


        if ($params["Code"] == '1001' && $params["Status"] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (!isset($params["Status"]) || $params["Status"] == '2') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['OrderNo'] ?? '0');
        $paymentOrderCollection->set('code', $params["Code"] ?? '');
        $paymentOrderCollection->set('result', $params["Msg"] ?? '');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('remark', '');
        $paymentOrderCollection->set('html', '');
        $paymentOrderCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentOrderCollection->result] ?? get_system_code('099');

        if ($status == Order::STATUS_PENDING && isset($params['Form']) && !empty($params['Form'])) {
            $code = get_system_code('200');
            $paymentOrderCollection->set('html', $params['Form']);
        }

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        $orderCollection = new Collection();
        $orderCollection->set('type', Order::TYPES_SALE);
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);

        // 判断是否3d
        if ($code == get_system_code('200') && $paymentOrderCollection->html) {
            $orderCollection->set('is_3d', 1);
        }

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function captureParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _refundParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        if ($params["Code"] == '1001' && $params["Status"] == '1') {
            $status = Order::STATUS_APPROVED;
        }

        if (!isset($params["Status"]) || $params["Status"] == '2') {
            $status = Order::STATUS_DECLINED;
        }

        $paymentRefundCollection = new Collection();
        $paymentRefundCollection->set('payment_refund_id', $params['orderId'] ?? '0');
        $paymentRefundCollection->set('code', $params["Code"] ?? '');
        $paymentRefundCollection->set('result', $params["Msg"] ?? '');
        $paymentRefundCollection->set('remark', '');
        $paymentRefundCollection->set('status', $status);

        $code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

        $refundCollection = new Collection();
        $refundCollection->set('status', self::$_respCode[$code][0]);
        $refundCollection->set('code', $code);
        $refundCollection->set('result', self::$_respCode[$code][1]);
        $refundCollection->set('remark', self::$_respCode[$code][2]);

        $collection = new Collection();
        $collection->set('refund', $refundCollection->toArray());
        $collection->set('payment_refund', $paymentRefundCollection->toArray());

        return $collection;
    }

    public static function _retrieveParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function syncParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function asyncParser($params): Collection
    {
        return self::authParser($params);
    }

    public static function _notifyParser($params): Collection
    {
        return self::authParser($params);
    }
}
