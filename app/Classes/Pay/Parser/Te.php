<?php

namespace App\Classes\Pay\Parser;

use App\Classes\Supports\Collection;
use App\Models\Order;
use App\Models\Refund;

class Te extends BaseParser
{
	protected static $_supplierName = 'te';

    public static function _authParser($params) : Collection
    {
    	$status = Order::STATUS_PENDING;

        if (isset($params['orderStatus'])) {
            if ($params['orderStatus'] == '0') {
                $status = Order::STATUS_DECLINED;
            } elseif ($params['orderStatus'] == '1') {
                $status = Order::STATUS_APPROVED;
            }
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['tradeNo'] ?? '');
        $paymentOrderCollection->set('code', $params['orderInfo'] ?? '');
        $paymentOrderCollection->set('type', Order::TYPES_SALE);
        $paymentOrderCollection->set('result', $params['errorMsg'] ?? '');
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('remark', $params['status'] ?? '');
        $paymentOrderCollection->set('html', '');

	    $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('095');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);
        $orderCollection->set('type', Order::TYPES_SALE);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

	public static function _refundParser($params) : Collection
	{
		$status = Refund::STATUS_PENDING;

		if (isset($params['code'])) {
			if ($params['code'] == '00') {
				$status = Refund::STATUS_APPROVED;
			}
		}

		$paymentRefundCollection = new Collection();
		$paymentRefundCollection->set('payment_refund_id', $params['tradeNo'] ?? '0');
		$paymentRefundCollection->set('code', $params['code'] ?? '');
        $paymentRefundCollection->set('result', $params['errorMsg'] ?? '');
        $paymentRefundCollection->set('remark', $params['remark'] ?? '');
		$paymentRefundCollection->set('status', $status);

		$code = self::$_channelExternalCode[$paymentRefundCollection->code] ?? get_system_code('099');

        if ($status == Refund::STATUS_APPROVED) {
            $code = get_system_code('000');
        }

		$refundCollection = new Collection();
		$refundCollection->set('status', self::$_respCode[$code][0]);
		$refundCollection->set('code', $code);
		$refundCollection->set('result', self::$_respCode[$code][1]);
		$refundCollection->set('remark', self::$_respCode[$code][2]);

		$collection = new Collection();
		$collection->set('refund', $refundCollection->toArray());
		$collection->set('payment_refund', $paymentRefundCollection->toArray());

		return $collection;
	}

    public static function _retrieveParser($params): Collection
    {
        $status = Order::STATUS_PENDING;

        //-1   待处理
        // 0   失败
        // 1   成功
        // 2   订单不存在
        // 3   传入参数不完整
        // 4   订单查询过多,(最多只能查询100笔)
        // 5   商户号或者网关接入号不存在
        // 6   singInfo 加密信息错误
        // 7   你方服务器 IP 未登记
        // 999 系统异常

        if (isset($params['queryResult'])) {
            if ($params['queryResult'] == '1') {
                $status = Order::STATUS_APPROVED;
            } elseif (in_array($params['queryResult'], ['2', '3', '4', '5', '6', '7', '999'])) {
                $status = Order::STATUS_DECLINED;
            }
        }

        $paymentOrderCollection = new Collection();
        $paymentOrderCollection->set('payment_order_id', $params['tradeNo'] ?? '0');
        $paymentOrderCollection->set('code', $params['queryResult'] ?? '');
        $paymentOrderCollection->set('result', $params['errorMsg'] ?? '');
        $paymentOrderCollection->set('remark', $params['queryResult'] ?? '');
        $paymentOrderCollection->set('status', $status);
        $paymentOrderCollection->set('type', Order::TYPES_SALE);

        $code = self::$_channelExternalCode[$paymentOrderCollection->code] ?? get_system_code('099');

        if ($status == Order::STATUS_APPROVED) {
            $code = get_system_code('000');
        } elseif ($status == Order::STATUS_PENDING) {
            $code = get_system_code('200');
        }

        $orderCollection = new Collection();
        $orderCollection->set('status', self::$_respCode[$code][0]);
        $orderCollection->set('code', $code);
        $orderCollection->set('result', self::$_respCode[$code][1]);
        $orderCollection->set('remark', self::$_respCode[$code][2]);
        $orderCollection->set('type', Order::TYPES_SALE);

        $collection = new Collection();
        $collection->set('order', $orderCollection->toArray());
        $collection->set('payment_order', $paymentOrderCollection->toArray());

        return $collection;
    }

    public static function syncParser($params) : Collection
    {
	    return self::authParser($params);
    }

	public static function asyncParser($params) : Collection
	{
		return self::authParser($params);
	}

	public static function _notifyParser($params) : Collection
	{
		return new Collection($params);
	}
}
