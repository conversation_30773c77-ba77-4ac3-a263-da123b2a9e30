<?php

namespace App\Classes\LocalPay\Gateways\Request;

use App\Classes\LocalPay\Contracts\Gateway;
use App\Classes\LocalPay\Gateways\Request;
use App\Classes\Supports\Config;
use App\Classes\LocalPay\Contracts\Support as BaseSupport;

/**
 * @property array http http options
 * @property string mode current mode
 * @property array log log options
 * @property string sign_key
 */
class Support
{
    use BaseSupport;

    /**
     * Bootstrap.
     *
     * @param Config $config
     */
    public function __construct(Config $config)
    {
        self::$gatewayName = 'Request';
        $this->baseUri     = Request::URL[$config->get('mode', Gateway::MODE_NORMAL)];
        $this->config      = $config;
        $this->setHttpOptions();
    }
}
