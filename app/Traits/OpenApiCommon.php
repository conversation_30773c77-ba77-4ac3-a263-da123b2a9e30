<?php

namespace App\Traits;

use App\Models\MerchantApiLog;
use Illuminate\Http\Request;
use App\Jobs\SendSlsLog;

trait OpenApiCommon
{
    /**
     * 常用HTTP状态码
     *
     * @var array
     */
    protected $_statusCodeList = [
        // 系统错误码
        '200' => 'OK',
        '204' => 'No Content',
        '400' => 'Bad Request',
        '401' => 'Unauthorized',
        '403' => 'Forbidden',
        '404' => 'Not Found',
        '405' => 'Method Not Allowed',
        '500' => 'Internal Server Error',
        '503' => 'Service Unavailable',
    ];


    protected $serviceErrCode = [
        // 业务错误码
        '0000' => 'Request is Success', // 请求成功
        '0348' => 'Invalid Merchant',   // 无效商户
        '1000' => 'Invalid Parameters', // 参数不合法
        '4000' => 'Request is Fail',    // 请求失败
    ];


    /**
     * 组装返回参数
     *
     * @param Request $request
     * @param $code
     * @param $result
     * @param array $requestData
     * @param string $id
     * @return \Illuminate\Http\Response|\Illuminate\Contracts\Routing\ResponseFactory
     */
    protected function getResponse(Request $request, $code, $result = [], $requestData = [], $logResult = [])
    {
        $result['message'] = $result['message'] ?? $this->serviceErrCode[$result['code']] ?? '';
        $returnData        = [
            'code'   => $code,
            'msg'    => $this->_statusCodeList[$code] ?? '',
            'result' => $result,
        ];
        $mid               = $request->input('mid', 0);
        $ip                = $request->getClientIp();
        $name              = $request->route()->getName();
        $isSucceed         = $code == '200' && $result['code'] == '0000' ? true : false;
        $logId             = $this->logRequestId();
        $requestData       = count($requestData) > 0 ? $requestData : $request->toArray();
        $logData           = $returnData;

        if (count($logResult) > 0) {
            $logData['result'] = $logResult;
        }
        // 记录请求日志
        $this->logToDB(json_encode($requestData, JSON_UNESCAPED_UNICODE), json_encode($logData, JSON_UNESCAPED_UNICODE), $mid, $isSucceed, $name, $ip, $logId);
        // 记录本地sls日志
        logger()->channel('apiRequest')->info(
            $logId,
            ['requestData' => $requestData, 'returnData' => $logData]
        );
        dispatch(new SendSlsLog(
            ['message' => 'apiRequest'],
            ['apiRequestId' => $logId, 'requestData' => $requestData, 'returnData' => $logData],
            'info',
        ));

        if (isset($this->_statusCodeList[$code])) {
            return response()->json($returnData, $code);
        }

        return response()->json($returnData);
    }

    /**
     * 记录请求日志
     *
     * @param $inData
     * @param $outData
     * @param $mid
     * @param $isSucceed
     * @param $api
     * @param $ip
     */
    protected function logToDB($inData, $outData, $mid, $isSucceed, $api, $ip, $logId)
    {
        $logArr = [
            'request_id'  => $logId,
            'in_data'     => $inData,
            'out_data'    => $outData,
            'merchant_id' => $mid,
            'is_succeed'  => $isSucceed,
            'api'         => $api,
            'ip'          => $ip,
            'created_at'  => date_create(),
            'updated_at'  => date_create(),
        ];

        //写入数据库
        MerchantApiLog::insert($logArr);
    }

    protected function logRequestId(): string
    {
        $time = explode(' ', microtime());
        return $time[1] . str_pad(intval($time[0] * 1000000), 6, '0', STR_PAD_LEFT) . '-' . uniqid();
    }

    protected function setRedisToken(string $token = ''): string
    {
        return "OPENAPIACCESSTOKEN_" . $token;
    }


    private function modifyValueOfKey($haystack)
    {
        return array_map(function ($key, $value) {
            if (is_array($value)) {
                return $this->modifyValueOfKey($value);
            }

            switch ($key) {
                case $key === 'card_number':
                    $value = get_markcard(DES3::encrypt($value, env('DES3_CARD_VIRTUAL')));
                    break;
                case $key === 'expiration_year':
                case $key === 'expiration_month':
                case $key === 'cvv':
                    $value = get_mark_data(DES3::encrypt($value, env('DES3_CARD_VIRTUAL')));
                    break;
            }
            return $value;
        }, array_keys($haystack), $haystack);
    }
}
