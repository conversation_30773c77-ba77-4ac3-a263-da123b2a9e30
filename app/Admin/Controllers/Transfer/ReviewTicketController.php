<?php

namespace App\Admin\Controllers\Transfer;

use App\Admin\Actions\Grid\Transfer\ReviewFail;
use App\Admin\Actions\Grid\Transfer\ReviewPass;
use App\Admin\Repositories\TransferTicket;
use App\Models\Merchant;
use App\Models\TransferTicket as TransferTicketModel;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Grid;
use Dcat\Admin\Widgets\Modal;

class ReviewTicketController extends AdminController
{
    protected $title = '提现复核';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new TransferTicket(['accounts']), function (Grid $grid) {
            $grid->model()
                ->where('status', TransferTicketModel::TRANSFER_STATUS_PROCESS)
                ->orderBy('created_at', 'desc')
                ->orderBy('id', 'desc');

            $grid->column('operation')->display(function () {
                return Modal::make()
                    ->title('提现失败')
                    ->lg()
                    ->body(ReviewFail::make()->payload(['id' => $this->id]))
                    ->button('<button class="btn btn-danger">失败</button>' . '&emsp;');
            })->append(function () {
                return Modal::make()
                    ->title('复核通过')
                    ->lg()
                    ->body(ReviewPass::make()->payload(['id' => $this->id]))
                    ->button('<button class="btn btn-success">通过</button>');
            });
            $grid->column('type')->display(function ($value) {
                return $value == 0 ? '商户提现' : '其他';
            });
            $grid->column('applicant_id');
            $grid->column('applicant_name');
            $grid->column('accounts.bank_account')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->bank_account . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.bank_name')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->bank_name . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.bank_address')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->bank_address . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.cardholder')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->cardholder . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.swift_iban')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->swift_iban . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('accounts.note')->display(function () {
                $accountsInfo = '';
                foreach ($this->accounts as $account) {
                    $accountsInfo .= $account->pivot->note . '<br />';
                }
                return $accountsInfo;
            });
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('check_amount');
            $grid->column('fee');
            $grid->column('fee_currency');
            $grid->column('fee_type')->display(function ($value) {
                return TransferTicketModel::$deductionMap[$value] ?? '未知';
            });
            $grid->column('deduction_amount');
            $grid->column('convert_currency');
            $grid->column('remarks');
            $grid->column('clearing_remarks');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableActions();
            // 字段翻译
            $grid->columns()->each(function ($col) {
                $col->setLabel(admin_trans('ticket.fields.' . $col->getName()));
            });

            $grid->fixColumns(1, 0);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('id')->width(3);
                $filter->equal('type', '提现类型')->select(['0' => '商户提现', '1' => '其他'])->width(3);
                $filter->equal('applicant_id', 'MID')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->width(3);
                $filter->equal('accounts.cardholder', '持卡人')->width(3);
                $filter->between('created_at')->datetime(['sideBySide'=>true])->width(3);
                $filter->between('updated_at')->datetime(['sideBySide'=>true])->width(3);
            });
        });
    }
}
