<?php

namespace App\Admin\Controllers\Channel;

use App\Admin\Actions\Grid\Channel\WithdrawalQuery;
use App\Admin\Actions\Grid\Channel\WithdrawalTable;
use App\Admin\Repositories\ChannelPid;
use App\Models\ChannelPid as ChannelPidModel;
use App\Models\ChannelSupplier;
use App\Models\DirectoryChannelMainBody;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class WithdrawalController extends AdminController
{
	protected $title = '渠道提现';

	/**
	 * Make a grid builder.
	 *
	 * @return Grid
	 */
	protected function grid()
	{
		return Grid::make(new ChannelPid(['channelSupplier', 'directoryChannelMainBody:id,full_name']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');

            $channel_supplier_id = ChannelSupplier::where('is_withdrawal', ChannelSupplier::IS_WITHDRAWAL_YES)->pluck('id');

            $grid->model()->whereIn('channel_supplier_id', $channel_supplier_id);

			$grid->column('channel_supplier.supplier_name');
            $grid->channel_pid;
			$grid->column('directory_channel_main_body.full_name', '主体名称');
			$grid->available_amount;
			$grid->remarks;
            $grid->created_at;
			$grid->updated_at->sortable();

            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableQuickEditButton();

            //同步金额
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new WithdrawalQuery($actions->row->id));
            });

            //提现详情
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $url = admin_route('withdrawal.index', ['channel_pid' => $this->id]);
                $actions->append('&nbsp<button class="btn btn-primary filter-btn-MmAXzmjA btn-outline"><a href="' . $url .'"> 详情</a></button>');
            });

			$grid->filter(function (Grid\Filter $filter) {
                $filter->equal('channel_supplier_id')->select(ChannelSupplier::all()->pluck('supplier_name', 'id')->toArray())->load('channel_pid', '/channel_pids/get_pid/channel_pid');
				$filter->equal('channel_pid')->select(ChannelPidModel::pluck('channel_pid', 'channel_pid')->toArray());
				$filter->equal('main_bodys_id', '主体名称')->select(DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray());
			});
		});
	}

}
