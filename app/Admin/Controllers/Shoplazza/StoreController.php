<?php

namespace App\Admin\Controllers\Shoplazza;

use App\Admin\Repositories\ShoplazzaStore;
use App\Models\MerchantBusiness;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use DES3;
use Dcat\Admin\Admin;
use Illuminate\Support\Facades\Cache;

class StoreController extends AdminController
{
    protected $title = '店铺数据';
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ShoplazzaStore(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableCreateButton();
            
            $grid->model()->orderBy('id','desc');
            $grid->column('id');
            $grid->column('store_id');
            $grid->column('store_url');
            $grid->column('merchant_id');
            $grid->column('business_id')->display(function() {
                return DES3::decrypt($this->merchant_id);
            });
            $grid->column('sign_key');
            $grid->column('is_fingerprint_device')->switch('', true);
            $grid->column('remarks');
            $grid->column('created_at');
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('store_id');
                $filter->equal('store_url');
                $filter->where('business_id', function($query) {
                    $query->where('merchant_id', DES3::encrypt($this->input));
                })->select(
                    MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->pluck('business_id', 'business_id')->toArray()
                );
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ShoplazzaStore(), function (Form $form) {
            $form->disableViewButton();
            $form->disableDeleteButton();

            $form->display('id');
            $form->display('store_id');
            $form->display('store_url');
            $form->display('refresh_token');
            $form->select('business_id')->options(
                MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->pluck('business_id', 'business_id')->toArray()
            )->placeholder('选择映射business_id')->required()->value(DES3::decrypt($form->model()->merchant_id));
            $form->text('sign_key')->placeholder('请输入16位随机字符串')->rules('required|between:16,16', [
                'required' => 'SignKey不能为空',
                'between'  => 'SignKey长度为16',
            ]);
            $form->switch('is_fingerprint_device');
            $form->text('remarks');
            $form->hidden('merchant_id');
        
            $form->display('created_at');
            $form->display('updated_at');
            
            $form->saving(function (Form $form) {
                $businessId = $form->business_id ?? '';
                if (!empty($businessId)) {
                    $form->merchant_id = DES3::encrypt($businessId);
                    // 删除
                    $form->deleteInput('business_id');  
                }
            });
            
            $form->saved(function (Form $form) {
                // 保存后清除缓存
                $key = md5('Shoplazza_Access_Token_' . $form->model()->store_id);
                if (Cache::has($key)) {
                    Cache::forget($key);
                }
            });
            
            Admin::Script(
                <<<JS
                    $('input[name="sign_key"]').parent().append('<div class="btn btn-primary btn-outline btn-sm sign_key" style="height:100%">&nbsp;生成SignKey</div>');
                    
                    $(document).on('click', '.sign_key', function () {
                        var t = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
                            a = t.length,
                            n = '';
                        for (i = 0; i < 16; i++) {
                            n += t.charAt(Math.floor(Math.random() * a));   
                        }
                        
                        $('input[name="sign_key"]').val(n);
                    });
                JS
            );
        });
    }
}
