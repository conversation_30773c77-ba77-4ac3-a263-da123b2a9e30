<?php

namespace App\Admin\Controllers\Risk\LocalChannelRouteScheme;

use App\Admin\Repositories\Risk\LocalChannelRouteScheme\Directchannel;
use App\Admin\Repositories\Risk\LocalChannelRouteScheme\Group;
use App\Classes\Supports\Arr;
use App\Models\Channel;
use App\Models\ChannelPid;
use App\Models\DirectoryLocalTransaction;
use App\Models\LocalChannelRouteScheme;
use Dcat\Admin\Form;
use Dcat\Admin\Form\NestedForm;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;

class DirectchannelController extends AdminController
{
    public function index(Content $content)
    {
        return $content->body($this->grid());
    }

    public function group($directchannel, Content $content)
    {
        return $content
            ->body($this->groupForm()->edit($directchannel));
    }

    public function groupUpdate($directchannel)
    {
        return $this->groupForm()->update($directchannel);
    }

    public function groupForm()
    {
        $channels = Channel::whereHas('channelPid', function ($query) {
            $query->where([
                'channel_type' => ChannelPid::CHANNEL_TYPE_LL,
                'access_type'  => 'directchannel',
            ]);
        })->where('status', '1')->get()->pluck('channel', 'channel')->toArray();

        return Form::make(new Group([request('scheme'), request('record'), request('directchannel'), request('transaction_type')]), function (Form $form) use ($channels) {
            $form->table('group', request('directchannel') . '组', function (NestedForm $table) use ($channels) {
                $table->html('<div class="btn btn-primary btn-outline btn-sm copy" style="margin-top: 10px">&nbsp;复制</div>');
                $table->select('channel', '账单标识')->options($channels)->required();
                $table->text('weight', '权重')->rules(['numeric'])->default(100, true)->required();
            });
            $form->action(admin_route('local.channel.route.scheme.record.directchannel.group.update', [request('scheme'), request('record'), request('directchannel'), request('transaction_type')]) . '?type=directchannel');

            if (!Admin::user()->can('risk/local_channel_route_schemes')) {
                $delete = true;
            } else {
                $delete = false;
            }

            Admin::Script(
                <<<JS
                    $(document).on("click", '.copy', function () {
                        let val = $(this).parents('td').next().find('select option:selected').val();
                        if (val == '') {
                            layer.msg('复制内容为空！');
                        } else {
                            var input = document.createElement('input'); // 创建input元素
                            // 把文字放进input中，供复制
                            input.value = val;
                            document.body.appendChild(input); // 向页面底部追加输入框
                            // 选中创建的input
                            input.select();
                            var copy_result = document.execCommand('copy'); // 执行复制命令
                            if (copy_result) {
                                layer.msg('已复制到粘贴板！');
                            } else {
                                layer.msg('复制失败！');
                            }
                            document.body.removeChild(input); //删除动态创建的节点
                        }
                    });
                    
                    if ("$delete") {
                        $(".layui-layer-btn-").remove();
                    }
                JS
            );
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        //交易类型
        $transactionTypes = DirectoryLocalTransaction::where('access_type', 'directchannel')->get()->pluck('transaction_type', 'transaction_type')->toArray();
        $transactionTypes = Arr::prepend($transactionTypes, '-', '-');

        return Form::make(new Directchannel([request('scheme'), request('record')]), function (Form $form) use ($transactionTypes) {
            $form->select('transaction_type', '交易类型')->options($transactionTypes)->required();
        });
    }

    protected function groupDelete(Form $form)
    {
        $schemeId = request('scheme');
        $urlId    = request('record');
        $t_type   = request('t_type');

        $channelRouteScheme = LocalChannelRouteScheme::find($schemeId)->toArray();

        if ($channelRouteScheme) {
            unset(
                $channelRouteScheme['extra_attributes'][$urlId]['directchannel'][$t_type],
                $channelRouteScheme['extra_attributes'][$urlId][$t_type]['directchannel']
            );

            if (isset($channelRouteScheme['extra_attributes'][$urlId][$t_type]) && count($channelRouteScheme['extra_attributes'][$urlId][$t_type]) === 0) {
                unset($channelRouteScheme['extra_attributes'][$urlId][$t_type]);
            }

            if (isset($channelRouteScheme['extra_attributes'][$urlId]['directchannel']) && count($channelRouteScheme['extra_attributes'][$urlId]['directchannel']) === 0) {
                unset($channelRouteScheme['extra_attributes'][$urlId]['directchannel']);
            }

            LocalChannelRouteScheme::where('id', $schemeId)->update(['extra_attributes' => json_encode($channelRouteScheme['extra_attributes'])]);

            return $form->response()->success('数据删除成功');
        }

        return $form->response()->error('数据删除失败');
    }
}
