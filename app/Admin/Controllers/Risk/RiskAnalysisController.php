<?php

namespace App\Admin\Controllers\Risk;

use App\Admin\Actions\Grid\Risk\Analysis\Synchronization;
use App\Admin\Repositories\Risk\RiskAnalysis;
use App\Models\Channel;
use App\Models\ChannelPid;
use App\Models\ChannelSupplier;
use App\Models\DirectoryCc;
use App\Models\DirectoryChannelMainBody;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\RiskMcc;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class RiskAnalysisController extends AdminController
{
    protected $title = '风险分析';
    protected $switchMonthType = ['qty1' => 'on', 'qty' => 'off'];
    protected $dishonourCaseType = ['on' => 'on', 'off' => 'off'];

    /**
     * @return Grid
     */
    protected function grid()
    {
        ini_set('memory_limit', '1024M');

        return Grid::make(new RiskAnalysis(request()->all()), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('date_stat_month', [
                    'start' => date('Ym', strtotime('-11 month')),
                    'end'   => date('Ym')
                ]);
            }

            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->disableBatchActions();
            $grid->disableCreateButton();
            $grid->disablePagination();
            //拒付调控开关（筛选：默认关闭）
            $dishonourCase = $grid->getRequestInput('dishonour_case_type') ?? 'off';
            //默认channel_id
            $channel_id = json_encode($grid->getRequestInput('channel_id') ?? []);

            $grid->column('date_stat_month', '日期')->sortable()->label('#586cb1');
            $grid->column('dishonour_qty_per', '拒付率')->display(function () use ($dishonourCase) {
                if ($dishonourCase == 'off') {
                    return self::perCalculate($this->dishonour_qty + $this->dishonour_case_qty, $this->transaction_qty);
                }
                return self::perCalculate($this->dishonour_qty, $this->transaction_qty);
            });
            $grid->column('fraud_qty_per', '拒付率（欺诈类）')->display(function () {
                return self::perCalculate($this->fraud_qty, $this->transaction_qty);
            });
            $grid->column('refund_qty_per', '退款率（笔数）')->display(function () {
                return self::perCalculate($this->refund_qty, $this->transaction_qty);
            });
            $grid->column('refund_amount_usd_per', '退款率（金额）')->display(function () {
                return self::perCalculate($this->refund_amount_usd, $this->transaction_amount_usd);
            });
            $grid->column('transaction_qty', '交易笔数');
            $grid->column('transaction_amount_usd', '交易金额');
            $grid->column('dishonour_qty', '拒付笔数')->display(function () use ($dishonourCase) {
                if ($dishonourCase == 'off') {
                    return $this->dishonour_qty + $this->dishonour_case_qty;
                }

                return $this->dishonour_qty;
            });
            $grid->column('refund_qty', '退款笔数');

            $grid->tools(Synchronization::make());

            $grid->filter(function ($filter) use ($channel_id) {
                $filter->panel();
                $filter->expand();
                $filter->equal('merchant_id', 'MID')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->load('business_id', '/businesses/get_businesses/' . MerchantBusiness::INTERNAL_STATUS_ENABLE)->width(3);
                $filter->equal('business_id', 'BID')->select(
                    MerchantBusiness::where('internal_status', MerchantBusiness::INTERNAL_STATUS_ENABLE)->pluck('business_id', 'business_id')
                        ->toArray()
                )->width(3);

                $filter->equal('channel_suppliers', '渠道')->select(ChannelSupplier::pluck('supplier_name', 'id')->toArray())->load('channel_pid', '/channel_pids/get_pid')->width(3);

                $filter->equal('channel_pid', 'PID')->select(ChannelPid::pluck('channel_pid', 'id')->toArray())->load('channel_id', '/channel_pids/channels/get_channels/channel_pid_id')->width(3);

				$filter->equal('main_bodys_id', '主体名称')->select(DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray())->width(3);

                $filter->in('channel_id', '账单标识')->multipleSelect(Channel::pluck('channel', 'id')->toArray())->width(3);
                Admin::script(
                    <<<JS
                        setTimeout(function () {
                            $(".channel_id").val({$channel_id}).trigger("change");
                        }, 1500);
JS
                );
                $filter->equal('switch_month_type', '还原')->select($this->switchMonthType)->default('qty')->ignore()->width(3);
                $filter->equal('dishonour_case_type', '拒付预警')->select($this->dishonourCaseType)->default('off')->ignore()->width(3);
                $filter->equal('card_country', '卡国家')->select(DirectoryCountry::pluck('name', 'name')->toArray())->width(3);
                $filter->equal('cc_type', '卡种')->select(DirectoryCc::isRiskControl()->pluck('cc_type', 'cc_type')->toArray())->width(3);
                $filter->equal('d_mcc_id', 'MCC')->select(RiskMcc::query()->where('overall_risk_rating', '!=', 'Prohibited')->pluck('mcc', 'id'))->width(3);
                $filter->equal('currency', '货币')->select(DirectoryCurrency::pluck('code', 'code')->toArray())->width(3);
                $filter->equal('url_id', '交易网站')->select(MerchantUrl::pluck('url_name', 'id')->toArray())->width(3);
                $filter->between('date_stat_month', '起始年月')->datetime(['format' => 'YYYYMM'])->width(3);
            });
        });
    }

    /**
     * 百分比计算
     * @param $molecular
     * @param $denominator
     * @param $prec
     * @return int|string
     */
    private static function perCalculate($molecular, $denominator, $prec = 4)
    {
        if (!$molecular || !floatval($denominator)) {
            $ret = '0.0000%';
        } else {
            $ret = number_format(round($molecular / $denominator * 100, $prec), $prec) . "%";
        }

        return $ret;
    }
}
