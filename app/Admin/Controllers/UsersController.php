<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Grid\User\ResetAccount;
use App\Models\OpenImUser;
use App\Services\OpenImService;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\UserController as BaseUserController;
use Dcat\Admin\Http\Repositories\Administrator;
use Dcat\Admin\Models\Administrator as AdministratorModel;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tree;
use Illuminate\Support\Facades\Hash;

class UsersController extends BaseUserController
{

    public function form()
    {
        Admin::script(
            <<<JS
            $(".feather.icon-eye").on('click', (eye) => {
                let password = $(eye.target).parent().parent().next();

                if(password.hasClass("on")) {
                    password.removeClass("on");
                    password.attr("type","password");
                } else {
                    password.addClass("on");
                    password.attr("type","text");
                }
            });
            $('#random_password').on('click', () => {
                $.ajax({
                    url: '/get_random_password',
                    type: 'GET',
                    success: function(res) {
                        $('.help-block').parent().find('.box-body').text(res);
                        $('input[name=password]').val(res);
                        $('input[name=password_confirmation]').val(res);
                    }
                });
            });
            JS
        );
        return Form::make(Administrator::with(['roles']), function (Form $form) {
            $userTable = config('admin.database.users_table');

            $connection = config('admin.database.connection');

            $id = $form->getKey();

            $form->display('id', 'ID');

            $form->text('username', trans('admin.username'))
                ->required()
                ->creationRules(['required', "unique:{$connection}.{$userTable}"])
                ->updateRules(['required', "unique:{$connection}.{$userTable},username,$id"]);
            $form->text('name', trans('admin.name'))->required();
            $form->image('avatar', trans('admin.avatar'))->autoUpload();
            $form->radio('is_notice', '是否开启告警通知')->options([0 => '关闭', 1 => '开启'])->default(0)->required();

            $form->display('random_password', '随机密码')->help('点击生成随机密码');
            $form->html('<span style="cursor: pointer" class="copy-group"><a href="javascript:;" id="random_password">生成随机密码</a></span>');

            if ($id) {
                $form->password('password', trans('admin.password'))
                    ->minLength(5)
                    ->maxLength(20)
                    ->customFormat(function () {
                        return '';
                    });
            } else {
                $form->password('password', trans('admin.password'))
                    ->required()
                    ->minLength(5)
                    ->maxLength(20);
            }

            $form->password('password_confirmation', trans('admin.password_confirmation'))->same('password');

            $form->hidden('password1');
            $form->hidden('password2');
            $form->hidden('password3');
            $form->hidden('password4');
            $form->hidden('password_valid_at');
            $form->ignore(['password_confirmation', 'random_password']);

            if (config('admin.permission.enable')) {
                $form->multipleSelect('roles', trans('admin.roles'))
                    ->options(function () {
                        $roleModel = config('admin.database.roles_model');

                        return $roleModel::all()->pluck('name', 'id');
                    })
                    ->customFormat(function ($v) {
                        return array_column($v, 'id');
                    });
            }

            $form->display('created_at', trans('admin.created_at'));
            $form->display('updated_at', trans('admin.updated_at'));

            if ($id == AdministratorModel::DEFAULT_ID) {
                $form->disableDeleteButton();
            }
        })->saving(function (Form $form) {
            $password = $form->password;

            if ($password) {
                //密码强度验证
                if (check_password_strength($password)) {
                    $arr = ['password', 'password1', 'password2', 'password3', 'password4'];
                    foreach ($arr as $val) {
                        if (Hash::check($password, $form->model()->{$val})) {
                            return $form->response()->error('新密码不能与最近5次修改重复');
                        }
                    }

                    $form->password4         = $form->model()->password3;
                    $form->password3         = $form->model()->password2;
                    $form->password2         = $form->model()->password1;
                    $form->password1         = $form->model()->password;
                    $form->password          = bcrypt($password);
                    $form->password_valid_at = now()->addMonths(3);
                } else {
                    return $form->response()->error('密码必须包括：字母/数字/标点符号至少两种并且长度为8-14位');
                }
            } else {
                $form->deleteInput('password');
            }
            return true;
        })->saved(function (Form $form, $result) {
            if ($form->isCreating()) {
                if (config('open_im.open_im')) {
                    // 注册openIm
                    $openImUser = [
                        'userId'          => $result,
                        'userType'        => OpenImUser::USER_TYPE_SYSTEM,
                        'nickname'        => $form->name,
                        'password'        => $form->password,
                        'addGroup'        => true,
                        'addFriend'       => true,
                        'createGroup'     => true,
                        'defaultAddGroup' => false,
                        'groupRoleLevel'  => OpenImUser::ROLE_LEVEL_MEMBER,
                        'userName'        => 'system',
                    ];

                    $openIm = new OpenImService;
                    $userId = $openIm->register($openImUser);
                    if ($userId == '') {
                        return $form->response()->error('openIm注册失败');
                    }
                }
            }

            return true;
        });
    }

    protected function grid()
    {
        return Grid::make(Administrator::with(['roles']), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->column('id', 'ID')->sortable();
            $grid->column('username');
            $grid->column('name');

            if (config('admin.permission.enable')) {
                $grid->column('roles')->pluck('name')->label('primary', 3);

                $permissionModel = config('admin.database.permissions_model');
                $roleModel       = config('admin.database.roles_model');
                $nodes           = (new $permissionModel())->allNodes();
                $grid->column('permissions')
                    ->if(function () {
                        return !$this->roles->isEmpty();
                    })
                    ->showTreeInDialog(function (Grid\Displayers\DialogTree $tree) use (&$nodes, $roleModel) {
                        $tree->nodes($nodes);

                        foreach (array_column($this->roles->toArray(), 'slug') as $slug) {
                            if ($roleModel::isAdministrator($slug)) {
                                $tree->checkAll();
                            }
                        }
                    })
                    ->else()
                    ->display('');
            }

            $grid->column('is_notice', '开启告警通知')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->quickSearch(['id', 'name', 'username']);

            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->showColumnSelector();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->getKey() == AdministratorModel::DEFAULT_ID) {
                    $actions->disableDelete();
                }

                if ($this->name != 'admin' || ($this->name == 'admin' && Admin::user()->username == 'admin')) {
                    $actions->append(new ResetAccount());
                }
            });
        });
    }

    protected function detail($id)
    {
        return Show::make($id, Administrator::with(['roles']), function (Show $show) {
            $show->field('id');
            $show->field('username');
            $show->field('name');

            $show->field('avatar', __('admin.avatar'))->image();
            $show->field('is_notice', '开启告警通知')->using([0 => '关闭', 1 => '开启']);

            if (config('admin.permission.enable')) {
                $show->field('roles')->as(function ($roles) {
                    if (!$roles) {
                        return;
                    }

                    return collect($roles)->pluck('name');
                })->label();

                $show->field('permissions')->unescape()->as(function () {
                    $roles = $this->roles->toArray();

                    $permissionModel = config('admin.database.permissions_model');
                    $roleModel       = config('admin.database.roles_model');
                    $permissionModel = new $permissionModel();
                    $nodes           = $permissionModel->allNodes();

                    $tree = Tree::make($nodes);

                    $isAdministrator = false;
                    foreach (array_column($roles, 'slug') as $slug) {
                        if ($roleModel::isAdministrator($slug)) {
                            $tree->checkAll();
                            $isAdministrator = true;
                        }
                    }

                    if (!$isAdministrator) {
                        $keyName = $permissionModel->getKeyName();
                        $tree->check(
                            $roleModel::getPermissionId(array_column($roles, $keyName))->flatten()
                        );
                    }

                    return $tree->render();
                });
            }

            $show->field('created_at');
            $show->field('updated_at');
        });
    }
}
