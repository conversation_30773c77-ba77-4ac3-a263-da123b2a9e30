<?php

namespace App\Admin\Controllers\Transaction;

use App\Admin\Actions\Tools\RefundErrorExportTool;
use App\Admin\Repositories\Refund;
use App\Models\PaymentRefund;
use App\Models\Refund as RefundModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Admin\Actions\Grid\Order\RefundError;
use App\Models\Refund as RefundErrorModel;
use Dcat\Admin\Widgets\Modal;

class RefundErrorController extends AdminController
{
    protected $title = '退款差错';

    protected $exportTitle = [
        'refund_id'                        => '退款订单号',
        'order_id'                         => '订单号',
        'payment_refund.payment_refund_id' => '退款渠道订单号',
        'order.channel'                    => '账单标识',
        'order.currency'                   => '原始订单币种',
        'order.amount'                     => '原始订单金额',
        'currency'                         => '币种',
        'amount'                           => '金额',
        'status'                           => '状态',
        'code'                             => '返回代码',
        'result'                           => '返回结果',
        'remark'                           => '备注',
        'completed_at'                     => '完成时间',
        'created_at'                       => '创建时间',
        'updated_at'                       => '更新时间',
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Refund(['paymentRefund', 'order']), function (Grid $grid) {
            $grid->model()
                ->whereIn('status', [RefundErrorModel::STATUS_PENDING, RefundErrorModel::STATUS_DECLINED, RefundErrorModel::STATUS_RECEIVED])
                ->orderBy('updated_at', 'desc')->orderBy('order_id', 'desc');
            $grid->refund_id->sortable();
            $grid->order_id;
            $grid->column('payment_refund.payment_refund_id', '退款渠道订单号');
            $grid->column('order.channel', '账单标识');
            $grid->column('order.currency', '原始订单币种');
            $grid->column('order.amount', '原始订单金额');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('status')->display(function ($value) {
                return RefundModel::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'success']);
            $grid->code;
            $grid->result;
            $grid->remark;
            $grid->completed_at;
            $grid->created_at;
            $grid->updated_at->sortable();
            $grid->fixColumns(1, -1);

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modal = Modal::make()
                    ->xl()
                    ->title('更 新')
                    ->body(RefundError::make()->payload(['refund_id' => $this->refund_id]))
                    ->button('更 新');
                // prepend一个操作
                $actions->prepend($modal . '&nbsp;');
            });

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $filter = $grid->model()->filter()->input();
                $tools->append(new RefundErrorExportTool($filter, $this->exportTitle));
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->inTextarea('refund_id')->setSeparator("\n");
                $filter->inTextarea('order_id')->setSeparator("\n");
                $filter->equal('paymentRefund.payment_refund_id', '退款渠道订单号');
                $filter->equal('status')->select(RefundModel::$statusMap);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Refund(['paymentRefund']), function (Show $show) {
            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->refund_id;
                $show->width(4)->order_id;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->status->using(\App\Models\Refund::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->remark;
                $show->width(4)->completed_at;
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
            });

            // 渠道信息
            $show->payment_info(function ($model) {
                return Show::make($model->paymentRefund['id'], new PaymentRefund(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->order_number('提交给渠道的订单号');
                        $show->width(4)->payment_refund_id('渠道返回的订单号');
                        $show->width(4)->currency;
                        $show->width(4)->amount;
                        $show->width(4)->status->using(\App\Models\PaymentRefund::$statusMap);
                        $show->width(4)->code;
                        $show->width(4)->result;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Refund(), function (Form $form) {
            $form->display('refund_id');
            $form->text('order_id');
            $form->text('currency');
            $form->text('amount');
            $form->text('code');
            $form->text('result');
            $form->text('remark');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

}
