<?php

namespace App\Admin\Controllers\Transaction\LocalPay;

use App\Admin\Actions\Grid\LocalPay\ExamineRefund;
use App\Admin\Actions\Grid\LocalPay\RefundQuery;
use App\Admin\Actions\Tools\LocalRefundsInfoExportTool;
use App\Admin\Repositories\LocalRefund;
use App\Models\ChannelSupplier;
use App\Models\LocalPaymentRefund;
use App\Models\LocalRefund as ModelsLocalRefund;
use App\Models\Merchant;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RefundController extends AdminController
{
    protected $title = '退款信息';
    
    protected $exportTitle = [
        'refund_id'                        => '退款订单号',
        'order_id'                         => '订单号',
        'payment_refund.payment_refund_id' => '退款渠道订单号',
        'order.channel'                    => '账单标识',
        'order.currency'                   => '原始订单币种',
        'order.amount'                     => '原始订单金额',
        'currency'                         => '币种',
        'amount'                           => '金额',
        'status'                           => '状态',
        'code'                             => '返回代码',
        'result'                           => '返回结果',
        'remark'                           => '备注',
        'completed_at'                     => '完成时间',
        'created_at'                       => '创建时间',
        'updated_at'                       => '更新时间',
    ];
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new LocalRefund(['paymentRefund', 'order']), function (Grid $grid) {
            $grid->model()->orderBy('updated_at', 'desc')->orderBy('order_id', 'desc');
            $grid->refund_id->sortable();
            $grid->order_id;
            $grid->column('order.order_number', '商户订单号');
            $grid->column('order.merchant_name', '商户名');
            $grid->column('payment_refund.payment_refund_id', '退款渠道订单号');
            $grid->column('order.channel', '账单标识');
            $grid->column('order.currency', '原始订单币种');
            $grid->column('order.amount', '原始订单金额');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('status')->display(function ($value) {
                return ModelsLocalRefund::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'success']);
            $grid->code;
            $grid->result;
            $grid->remark;
            $grid->completed_at;
            $grid->created_at;
            $grid->updated_at->sortable();
            $grid->fixColumns(1, -1);

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->status === 2 && $this->code === get_system_code('210')) {
                    $actions->disableView();
                    $actions->prepend(new ExamineRefund());
                }
            });

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $tools->append(new LocalRefundsInfoExportTool($this->exportTitle));
            });

            $grid->batchActions([new RefundQuery('查询')]);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('refund_id');
                $filter->equal('order_id');
                $filter->where('merchant_id',function ($query){
                    $query->whereHas('order', function ($query) {
                        $query->where('merchant_id', "{$this->input}");
                    });
                })->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('paymentRefund.payment_refund_id', '退款渠道订单号');
                $filter->where('order_number', function ($query) {
                    $query->whereHas('order', function ($query) {
                        $query->where('order_number', "{$this->input}");
                    });
                }, '商户订单号');
                $filter->equal('status')->select(ModelsLocalRefund::$statusMap);
                $filter->equal('order.channelObj.channel_supplier_id','渠道')->select(ChannelSupplier::get()->pluck('supplier_name', 'id')->toArray());
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new LocalRefund(['paymentRefund']), function (Show $show) {
            $show->html();
            $show->disableEditButton();
            $show->disableDeleteButton();

            $show->row(function (Show\Row $show) {
                $show->width(4)->refund_id;
                $show->width(4)->order_id;
                $show->width(4)->currency;
                $show->width(4)->amount;
                $show->width(4)->status->using(ModelsLocalRefund::$statusMap);
                $show->width(4)->code;
                $show->width(4)->result;
                $show->width(4)->remark;
                $show->width(4)->completed_at;
                $show->width(4)->created_at;
                $show->width(4)->updated_at;
            });

            // 渠道信息
            $show->payment_info(function ($model) {
                return Show::make($model->paymentRefund['id'], new LocalPaymentRefund(), function (Show $show) {
                    $show->row(function (Show\Row $show) {
                        $show->width(4)->order_number;
                        $show->width(4)->payment_refund_id;
                        $show->width(4)->currency;
                        $show->width(4)->amount;
                        $show->width(4)->status->using(LocalPaymentRefund::$statusMap);
                        $show->width(4)->code;
                        $show->width(4)->result;
                    });

                    $show->disableDeleteButton();
                    $show->disableEditButton();
                    $show->disableListButton();
                });
            });
        });
    }
}
