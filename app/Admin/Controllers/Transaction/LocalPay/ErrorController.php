<?php

namespace App\Admin\Controllers\Transaction\LocalPay;

use App\Admin\Actions\Grid\LocalPay\Error;
use App\Admin\Repositories\LocalOrder;
use App\Models\Channel;
use App\Models\LocalOrder as ModelsLocalOrder;
use App\Models\LocalOrderAddress;
use App\Models\LocalPaymentOrder;
use App\Models\Merchant;
use App\Models\MerchantUrl;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Database\Eloquent\Builder;

class ErrorController extends AdminController
{
    protected $title = '交易差错';
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new LocalOrder(['paymentOrder']), function (Grid $grid) {
            $grid->fixColumns(1, -1);

            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('completed_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }
            $grid->model()
                ->whereIn('status', [ModelsLocalOrder::STATUS_RECEIVED, ModelsLocalOrder::STATUS_PENDING, ModelsLocalOrder::STATUS_DECLINED, ModelsLocalOrder::STATUS_EXPIRED])
                ->orderBy('completed_at', 'desc')->orderBy('order_id', 'desc');

            $grid->column('order_id')->sortable();
            $grid->column('transaction_type');
            $grid->column('status')->display(function ($value) {
                return ModelsLocalOrder::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);
            $grid->column('merchant_id');
            $grid->column('business_id');
            $grid->column('merchant_name');
            $grid->column('url_name');
            $grid->column('channel');
            $grid->column('order_number');
            $grid->column('currency');
            $grid->column('amount');
            $grid->column('code');
            $grid->column('result');
            $grid->column('remark');
            $grid->column('payment_order.order_number', '渠道订单号');
            $grid->column('payment_order.payment_order_id', '渠道交易流水号');
            $grid->column('completed_at')->sortable();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();

            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableRowSelector();


            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $modale = Modal::make()
                    ->xl()
                    ->title('')
                    ->body(Error::make()->payload(['order_id' => $this->order_id]))
                    ->button('<button type="button" class="btn btn-outline-primary">更新</button>');
                // prepend一个操作
                $actions->prepend($modale . '&nbsp;');
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_id');
                $filter->equal('order_number');
                $filter->equal('merchant_id')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                );
                $filter->equal('url_id')->select(MerchantUrl::get()->pluck('url_name', 'id')->toArray());
                $filter->equal('channel_id')->select(
                    Channel::whereHas('channelPid', function (Builder $query) {
                        $query->where('channel_type', 1);
                    })->pluck('channel', 'id')->toArray()
                );
                $filter->equal('status')->select(ModelsLocalOrder::$statusMap);
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('completed_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new LocalOrder(['paymentOrder', 'address']), function (Show $show) {

	        $show->html();
	        $show->disableEditButton();
	        $show->disableDeleteButton();

	        $show->row(function (Show\Row $show) {
		        $show->width(4)->order_id;
		        $show->width(4)->order_number;
		        $show->width(4)->merchant_id;
		        $show->width(4)->business_id;
		        $show->width(4)->merchant_name;
		        $show->width(4)->url_name;
		        $show->width(4)->channel;
		        $show->width(4)->currency;
		        $show->width(4)->amount;
		        $show->width(4)->transaction_type;
		        $show->width(4)->status->using(ModelsLocalOrder::$statusMap);
		        $show->width(4)->code;
		        $show->width(4)->result;
		        $show->width(4)->completed_at;
		        $show->width(4)->created_at;
		        $show->width(4)->updated_at;
	        });

	        // 渠道信息
	        $show->payment_info(function ($model) {
		        return Show::make($model->paymentOrder['id'], new LocalPaymentOrder(), function (Show $show) {
			        $show->row(function (Show\Row $show) {
				        $show->width(4)->order_number;
				        $show->width(4)->payment_order_id;
				        $show->width(4)->currency;
				        $show->width(4)->amount;
				        $show->width(4)->transaction_type;
				        $show->width(4)->status->using(LocalPaymentOrder::$statusMap);
				        $show->width(4)->code;
				        $show->width(4)->result;
			        });

			        $show->disableDeleteButton();
			        $show->disableEditButton();
			        $show->disableListButton();
		        });
	        });

	        // 地址信息
	        $show->address_info(function ($model) {
		        return Show::make($model->address['id'], new LocalOrderAddress(), function (Show $show) {
			        $show->row(function (Show\Row $show) {
				        $show->width(4)->bill_name;
				        $show->width(4)->bill_email;
				        $show->width(4)->bill_address;
				        $show->width(4)->bill_city;
				        $show->width(4)->bill_state;
				        $show->width(4)->bill_postcode;
				        $show->width(4)->bill_country;
				        $show->width(4)->bill_country_isoa2;
				        $show->width(4)->bill_phone;
			        });

			        $show->row(function (Show\Row $show) {
				        $show->width(4)->ship_name;
				        $show->width(4)->ship_email;
				        $show->width(4)->ship_address;
				        $show->width(4)->ship_city;
				        $show->width(4)->ship_state;
				        $show->width(4)->ship_postcode;
				        $show->width(4)->ship_country;
				        $show->width(4)->ship_country_isoa2;
				        $show->width(4)->ship_phone;
			        });

			        $show->row(function (Show\Row $show) {
				        $show->width(4)->ip;
				        $show->width(4)->ip_country;
				        $show->width(4)->ip_country_isoa2;
				        $show->width(4)->ip_city;
				        $show->width(4)->ip_isp;
				        $show->width(4)->ip_postal_code;
			        });

			        $show->disableDeleteButton();
			        $show->disableEditButton();
			        $show->disableListButton();
		        });
	        });
        });
    }
}
