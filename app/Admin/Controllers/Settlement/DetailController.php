<?php

namespace App\Admin\Controllers\Settlement;

use App\Admin\Actions\Tools\SettlementDetailExportTool;
use App\Admin\Repositories\SettleDetail;
use App\Models\ChannelSupplier;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use Dcat\Admin\Grid;
use App\Models\SettleDetail as SettleDetailModel;
use Dcat\Admin\Http\Controllers\AdminController;

class DetailController extends AdminController
{
    protected $title = '结算明细';

    protected $exportTitle = [
        'business_id'         => 'BID',
        'merchant_id'         => 'MID',
        'merchant_name'       => '商户名称',
        'related_id'          => '关联ID',
        'order_number'        => '商户订单号',
        'channel_supplier_id' => '渠道',
        'settle_currency'     => '结算货币',
        'settle_amount'       => '结算金额',
        'currency'            => '订单货币',
        'amount'              => '订单金额',
        'rate'                => '结算金额/订单金额',
        'amount_type'         => '金额类型',
        'remarks'             => '备注',
        'order_complete_at'   => '交易完成时间',
        'settle_at'           => '结算日期'
    ];

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        ini_set('memory_limit', '512M');

        $channelSupplierList = ChannelSupplier::all()->pluck('supplier_name', 'id')->toArray();

        return Grid::make(new SettleDetail(), function (Grid $grid) use ($channelSupplierList) {
            if (count(request()->toArray()) <= 1) {
                request()->offsetSet('order_complete_at', [
                    'start' => date('Y-m-d 00:00:00', strtotime('-1 week')),
                    'end'   => date('Y-m-d H:i:s')
                ]);
            }
            $grid->model()
                ->where('settle_amount', '<>', '0')
                ->where('settle_at', '<=', date('Y-m-d'))
                ->orderBy('settle_at', 'DESC')
                ->orderBy('id', 'DESC');

            $grid->column('business_id');
            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('related_id');
            $grid->column('order_number');
            $grid->column('channel_supplier_id')->display(function ($value) use ($channelSupplierList) {
                return isset($channelSupplierList[$value]) ? $channelSupplierList[$value] : '-';
            });
            $grid->column('settle_amount', '结算金额')->display(function ($value) {
                return $value . '&nbsp;&nbsp;' . $this->settle_currency;
            });
            $grid->column('amount', '订单金额')->display(function ($value) {
                return $value . '&nbsp;&nbsp;' . $this->currency;
            });
            $grid->column('rate', '结算金额/订单金额');

            $grid->column('amount_type')->display(function ($value) {
                return SettleDetailModel::$amountTypeMap[$value];
            });

            $grid->column('remarks');
            $grid->column('order_complete_at');
            $grid->column('settle_at');

            $grid->fixColumns(0, -1);

            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableBatchDelete();

            $grid->tools(function (Grid\Tools $tools) use ($grid) {
                $filter = $grid->model()->filter()->input();
                $tools->append(new SettlementDetailExportTool($filter, $this->exportTitle));
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->load('business_id', '/businesses/get_businesses');
                $filter->equal('business_id')->select(
                    MerchantBusiness::pluck('business_id', 'business_id')->toArray()
                );
                $filter->equal('order_number');
                $filter->equal('related_id');
                $filter->equal('channel_supplier_id')->select(ChannelSupplier::all()->pluck('supplier_name', 'id')->toArray());
                $filter->equal('amount_type')->select(SettleDetailModel::$amountTypeMap);
                $filter->between('settle_at')->date();
                $filter->between('created_at')->datetime(['sideBySide'=>true]);
                $filter->between('order_complete_at')->datetime(['sideBySide'=>true]);
            });
        });
    }
}
