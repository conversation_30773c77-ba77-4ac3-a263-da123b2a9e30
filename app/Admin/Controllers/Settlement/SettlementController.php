<?php

namespace App\Admin\Controllers\Settlement;

use App\Admin\Actions\Grid\Settlement\SettleDetailTable;
use App\Admin\Actions\Tools\SettlementExportTool;
use App\Admin\Repositories\Settlement;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;

class SettlementController extends AdminController
{
    protected $title = '结算信息';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        Admin::style(
            <<<css
	.modal-xxl {max-width: 100%;}
css
        );

        return Grid::make(new Settlement(), function (Grid $grid) {
            $grid->model()->orderBy('settle_at', 'DESC')->orderBy('id', 'DESC');

            $grid->column('other', '操作')->display(function ($modal) {
                $modal = Modal::make()
                    ->title('结算详情')
                    ->size('xxl')
                    ->body(SettleDetailTable::make()->payload([
                        'business_id'     => $this->business_id,
                        'settle_currency' => $this->settle_currency,
                        'settle_at'       => $this->settle_at
                    ]))->button('<button class="btn btn-info">详情</button>');

                return $modal;
            });

            $grid->column('business_id');
            $grid->column('merchant_id');
            $grid->column('merchant_name');
            $grid->column('settle_currency');
            $grid->column('settle_amount');
            $grid->column('sale_amount');
            $grid->column('sale_rate_fee');
            $grid->column('sale_fee');
            $grid->column('sale_deposit_fee');
            $grid->column('sale_3d_fee');
            $grid->column('sale_risk_fee');
            $grid->column('auth_fee');
            $grid->column('auth_3d_fee');
            $grid->column('auth_risk_fee');
            $grid->column('capture_amount');
            $grid->column('capture_rate_fee');
            $grid->column('capture_deposit_fee');
            $grid->column('refund_amount');
            $grid->column('refund_fee');
            $grid->column('chargeback_amount');
            $grid->column('chargeback_fee');
            $grid->column('chargeback_reversal_amount');
            $grid->column('pre_chargeback_fee');
            $grid->column('chargeback_penalty');
            $grid->column('chargeback_penalty_special');
            $grid->column('other_amount');
            $grid->column('settle_at');

            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableBatchDelete();
            $grid->fixColumns(0, -1);

            $grid->tools(new SettlementExportTool());

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('merchant_id')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->load('business_id', '/businesses/get_businesses');
                $filter->equal('business_id')->select(
                    MerchantBusiness::pluck('business_id', 'business_id')->toArray()
                );
                $filter->between('settle_at')->date();

                // 设置默认时间
                if (count(request()->toArray()) <= 1) {
                    request()->offsetSet('settle_at.start', date('Y-m-d', strtotime('-1 week')));
                    request()->offsetSet('settle_at.end', date('Y-m-d'));
                }
            });
        });
    }
}
