<?php

namespace App\Admin\Controllers\RiskControl\Card;

use App\Admin\Actions\Tools\CardCipherWhitelistExportTool;
use App\Admin\Repositories\CardCipherWhitelist;
use App\Models\DirectoryCc;
use App\Models\Merchant;
use App\Models\OrderLoses;
use App\Models\OrderPost;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use DES3;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CardCipherWhitelistController extends AdminController
{
    protected $exportTitle = [
        'card_number'  => '卡密文',
        'card_mask'    => '卡掩码',
        'cc_type'      => '卡种',
        'merchant_ids' => '商户IDs',
        'created_at'   => '创建时间',
    ];
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CardCipherWhitelist(), function (Grid $grid) {
            $grid->model()->orderBy('created_at', 'desc');

            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->enableDialogCreate();
            $grid->setDialogFormDimensions('50%', '70%');
            $grid->showColumnSelector();
            $grid->hideColumns(['id', 'updated_at']);

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new CardCipherWhitelistExportTool($this->exportTitle));
            });

            $grid->column('id')->sortable();
            $grid->column('card_number');
            $grid->column('card_mask');
            $grid->column('cc_type');
            $grid->column('order_id');
            $grid->column('merchant_ids')->display(function($text) {
                    return '<span data-toggle="tooltip" data-placement="top" title="'.e($text).'">'.Str::limit($text, 50).'</span>';
            });
            $grid->column('created_at')->sortable();
            $grid->column('updated_at')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->equal('card_number')->width(2);
                $filter->equal('card_mask')->width(2);
                $cc = DirectoryCc::select('cc_type')->where('status', 1)->pluck('cc_type', 'cc_type')->toArray();
                $filter->equal('cc_type')->select($cc)->width(2);
                $filter->equal('order_id')->width(2);
                $merchants = Merchant::where('status', Merchant::STATUS_ENABLE)
                                     ->pluck('merchant_name', 'merchant_id')
                                     ->map(static function ($item, $key) {return $item . ':' . $key;})
                                     ->toArray();
                $filter->where('merchant_ids', function ($query) {
                    foreach ($this->input as $id) {
                        $query->orWhere('merchant_ids', 'LIKE', "%&{$id}&%")
                              ->orWhere('merchant_ids', 'LIKE', "{$id}&%")
                              ->orWhere('merchant_ids', 'LIKE', "%&{$id}")
                              ->orWhere('merchant_ids', $id);
                    }
                })->multipleSelect($merchants)->width(4);

                $filter->whereBetween('created_at', function ($query) {
                    $startDate = $this->input['start'] ?? '';
                    $endDate = $this->input['end'] ?? '';

                    $query->when(!empty($startDate), function ($q) use ($startDate) {
                        $q->where('created_at', '>=', $startDate);
                    })->when(!empty($endDate), function ($q) use ($endDate) {
                        $q->where('created_at', '<=', $endDate);
                    });
                })->datetime(['sideBySide' => true])->width(4);
            });

            Admin::script('$("[data-toggle=\"tooltip\"]").tooltip({ boundary: "window" });');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CardCipherWhitelist(), function (Show $show) {
            $show->field('id');
            $show->field('merchant_ids');
            $show->field('card_number');
            $show->field('card_mask');
            $show->field('cc_type');
            $show->field('order_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CardCipherWhitelist(), function (Form $form) {
            $form->display('id');
            $form->display('card_mask')->hideInDialog();

            $cc = DirectoryCc::select('cc_type')->where('status', 1)->pluck('cc_type', 'cc_type')->toArray();
            $form->text('order_id')->required();
            $form->text('card_number')->readOnly()->required();
            $form->text('card_mask')->readOnly()->required();
            $form->select('cc_type')->options($cc)->required();
            $form->multipleSelect('merchant_ids')->required()->options(
                Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
            )->customFormat(function ($value) {
                return empty($value) ? [] : explode('&', $value[0]);
            })->saving(function ($value) {
                return trim(implode('&', $value), '&');
            });

            Admin::script(
                <<<JS
                    $("input[name='order_id']").on('input',function (){
                            let orderId = $(this).val();
                            if (!orderId){
                                return;
                            }
                            
                            let form = $(this).closest('form');
                            
                            $.ajax({
                                url: '/admin/card_cipher_whitelist/get_card_info',
                                type: 'GET',
                                data: {
                                    order_id: orderId
                                },
                                beforeSend: function() {
                                    form.loading();
                                },
                                complete: function() {
                                    form.loading(false); 
                                },
                                success: function (data) {
                                    if (data.code === 0) {
                                        $('input[name="card_number"]').val(data.data.card_number);
                                        $('input[name="card_mask"]').val(data.data.card_mask);
                                        $('select[name="cc_type"]').val(data.data.cc_type).trigger('change');
                                        $('select[name="merchant_ids[]"]').val([data.data.merchant_id]).trigger('change');
                                    }else {
                                        $('input[name="card_number"]').val('');
                                        $('input[name="card_mask"]').val('');
                                        $('select[name="cc_type"]').val('').trigger('change');
                                        $('select[name="merchant_ids[]"]').val([]).trigger('change');
                                    }
                                }
                            })
                    });
                JS
            );

            $form->display('created_at');
            $form->display('updated_at');

            $form->submitted(function (Form $form) {
                // 如果card_number存在更新而非新增
                if ($form->card_number) {
                    $cardCipherWhitelist = \App\Models\CardCipherWhitelist::query()
                                                               ->where('card_number', $form->card_number)
                                                               ->first();

                    if ($cardCipherWhitelist) {
                        // 如果 mid 已经存在返回重复
                        $existingMerchants = explode('&', $cardCipherWhitelist->merchant_ids);
                        $newMerchants = array_filter((array)$form->merchant_ids); // 过滤空值

                        if ($duplicates = array_intersect($newMerchants, $existingMerchants)) {
                            return $form->response()->error('商户ID ['.implode(',', $duplicates).'] 已存在')->refresh();
                        }

                        // 拼接 merchant_ids
                        $form->merchant_ids = array_filter($form->merchant_ids);
                        $cardCipherWhitelist->merchant_ids = implode('&', array_unique(array_merge(explode('&', $cardCipherWhitelist->merchant_ids), $form->merchant_ids)));
                        $cardCipherWhitelist->save();

                        return $form->response()->success('更新成功')->refresh();
                    }
                }
            });
        });
    }

    /**
     * 获取卡密信息
     * @param Request $request
     * @return JsonResponse
     */
    public function getCardInfo(Request $request): JsonResponse
    {
        $orderId = $request->input('order_id');

        if (!$orderId) {
            return response()->json(['code' => 7, 'msg' => '订单号不能为空']);
        }

        try {
            // 优先查询 OrderPost
            $orderPost = OrderPost::query()
                                  ->where(function ($q) use ($orderId) {
                                      $q->where('order_id', $orderId)
                                        ->orWhere('order_number', $orderId);
                                  })
                                  ->first(['order_id', 'order_number', 'merchant_id', 'card']);

            $card = $orderPost ? $this->parseCardData($orderPost->card) : null;

            // 如果未找到，再查询 OrderLoses
            if (!$card) {
                $orderLose = OrderLoses::query()
                                       ->where('order_number', $orderId)
                                       ->first(['order_number', 'merchant_id', 'post_data']);

                $postData = $orderLose ? json_decode($orderLose->post_data,true) : null;
                $card = $postData['card'] ?? null;
            }

            if (!$card) {
                return response()->json(['code' => 1, 'msg' => '订单号不存在']);
            }

            return response()->json([
                                        'code' => 0,
                                        'data' => [
                                            'card_number' => DES3::encrypt($card['card_number']),
                                            'card_mask'   => get_markcard($card['card_number']),
                                            'cc_type'     => get_cc_type($card['card_number']),
                                            'merchant_id' => $orderPost ? $orderPost->merchant_id : ($orderLose->merchant_id ?? null)
                                        ]
                                    ]);

        } catch (\Exception $e) {
            return response()->json(['code' => 500, 'msg' => '服务器内部错误']);
        }
    }

    private function parseCardData($encryptedData)
    {
        try {
            return json_decode(DES3::decrypt($encryptedData), true, 512, JSON_THROW_ON_ERROR);
        } catch (\Exception $e) {
            return null;
        }
    }


}
