<?php

namespace App\Admin\Controllers\RiskControl\Financial;

use App\Admin\Repositories\RiskFinancial;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Show;

class RiskFinancialController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        app('admin.translator')->setPath('risk-financial');
        return Grid::make(new RiskFinancial(), function (Grid $grid) {
            $merchantId = request()->input('merchant_id');
            $grid->model()->where('merchant_id', $merchantId)->orderBy('created_at', 'desc');
            $grid->disableEditButton();
            $grid->disableBatchActions();
            $grid->disableRowSelector();
            $grid->setResource("risk_financial/$merchantId");
            $grid->enableDialogCreate();

            $grid->column('created_at')->sortable();
            $grid->column('file')->downloadable()->display(function ($value) {
                // 为支持的Office文件类型添加在线预览链接
                return add_office_preview_links($value);
            });
            $grid->column('remark');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableDelete();
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RiskFinancial(), function (Show $show) {
            $show->field('id');
            $show->field('file');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RiskFinancial(), function (Form $form) {
            $form->action('risk_financial');
            $form->display('id');
            $form->hidden('merchant_id')->default(request()->route('merchant_id',''));
            $form->hidden('risk_case_id')->default(0);
            $form->file('file')->downloadable()->autoUpload()->required();
            //模板下载
            $form->button('<span >' . admin_trans_field('download_template') . ' </span>')->on('click', "
                window.open('/download/financial.xlsx');
            ");
            $form->textarea('remark')->required();

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public function index(Content $content): Content
    {
        return $content->body($this->grid())->full();
    }
}
