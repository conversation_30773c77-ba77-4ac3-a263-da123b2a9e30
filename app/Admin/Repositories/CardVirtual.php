<?php

namespace App\Admin\Repositories;

use App\Models\CardVirtual as Model;
use Dcat\Admin\Repositories\EloquentRepository;

class CardVirtual extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public static $internalStatusMap = ['停用', '激活', '待提交渠道', '渠道待销', '已销卡', '待处理', '销卡处理中', '开卡进行中',];
    public static $internalDefaultMap = ['否', '是'];
    public static $internalTypetMap = ['常规卡', '共享卡'];
}
