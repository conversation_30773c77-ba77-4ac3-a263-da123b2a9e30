<?php

namespace App\Admin\Repositories\Risk\ChannelRouteScheme;

use App\Models\ChannelRouteScheme as Model;
use Dcat\Admin\Form;
use Dcat\Admin\Repositories\EloquentRepository;

class Scheme extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function store(Form $form)
    {
        return parent::store($form);
    }
}
