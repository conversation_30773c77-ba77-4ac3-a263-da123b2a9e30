<?php

namespace App\Admin\Repositories\Risk;

use App\Models\ChannelPid;
use Dcat\Admin\Repositories\EloquentRepository;
use Dcat\Admin\Grid;
use App\Models\Channel;
use App\Models\StatOrderChargeback as Model;
use Illuminate\Support\Facades\DB;

class RiskAnalysis extends EloquentRepository
{
    /**
     * Model.
     *
     * @var string
     */
    protected $eloquentClass = Model::class;

    public function get(Grid\Model $model)
    {
        $wheres     = $this->relations;
        $startMonth = $wheres['date_stat_month']['start'] ?? date('Ym', strtotime('-11 months'));
        $endMonth   = $wheres['date_stat_month']['end'] ?? date('Ym');
        $equalField = [
            'merchant_id', 'business_id', 'card_country', 'cc_type', 'd_mcc_id', 'currency', 'url_id'
        ];
		//拒付率（筛选：当月还是还原月）
		$monthTypeSuffix = $wheres['switch_month_type'] ?? 'qty';
		//默认退款金额
		$refundAmountUsd      = ($monthTypeSuffix == 'qty') ? 'refund_amount_usd' : 'refund_amount_usd1';
		$equalWhere           = [];
		$channelIds           = [];
		$channelIdsToMainBody = [];
		$isReturnEmpty        = false;
        foreach ($wheres as $field => $value) {
            if (!empty($value)) {
                switch ($field) {
                    case in_array($field, $equalField):
                        $equalWhere[$field] = $value;
                        break;
                    case 'channel_suppliers':
                        $channelIds = Channel::where('channel_supplier_id', $value)->pluck('id')->toArray();
                        break;
                    case 'channel_pid':
                        $channelIds = Channel::where('channel_pid_id', $value)->pluck('id')->toArray();
                        break;
                    case 'channel_id':
                        $channelIds = $value;
                        break;
					case 'main_bodys_id':
						$channelPidInfo = ChannelPid::with('Channel:id,channel_pid_id')
							->select('id', 'main_bodys_id')
							->where('main_bodys_id', $value)
							->get()->toArray();
						foreach ($channelPidInfo as $key => $channelData) {
							foreach ($channelData['channel'] as $channelId) {
								$channelIdsToMainBody[] = $channelId['id'];
							}
						}
						$isReturnEmpty = true;
						break;
				}
			}
		}

        $query = Model::select(
            'date_stat_month',
            DB::raw('SUM(dishonour_' . $monthTypeSuffix . ') as dishonour_qty'),
            DB::raw('SUM(fraud_' . $monthTypeSuffix . ') as fraud_qty'),
            DB::raw('SUM(refund_' . $monthTypeSuffix . ') as refund_qty'),
            DB::raw('SUM(dishonour_case_' . $monthTypeSuffix . ') as dishonour_case_qty'),
            DB::raw('SUM(transaction_qty) as transaction_qty'),
            DB::raw('SUM(' . $refundAmountUsd . ') as refund_amount_usd'),
            DB::raw('SUM(transaction_amount_usd) as transaction_amount_usd')
        );

        if (count($equalWhere)) {
            $query->where($equalWhere);
        }

        if (count($channelIds)) {
            $query->whereIn('channel_id', $channelIds);
        }

		if ($isReturnEmpty && empty($channelIdsToMainBody)) {
			return [];
		}

		if (count($channelIdsToMainBody)) {
			$query->whereIn('channel_id', $channelIdsToMainBody);
		}

        $statistics  = $query->whereBetween('date_stat_month', [$startMonth, $endMonth])
            ->groupBy('date_stat_month')
            ->orderBy('date_stat_month', 'DESC')
            ->get()->keyBy('date_stat_month')->toArray();
        $allMonthArr = $this->getAllMonthArr($startMonth, $endMonth);

        foreach ($allMonthArr as $month) {
            if (!isset($statistics[$month])) {
                $statistics[$month] = [
                    "date_stat_month"        => $month,
                    "dishonour_qty"          => "0",
                    "fraud_qty"              => "0",
                    "refund_qty"             => "0",
                    "dishonour_case_qty"     => "0",
                    "transaction_qty"        => "0",
                    "refund_amount_usd"      => "0",
                    "transaction_amount_usd" => "0"
                ];
            }
        }

        krsort($statistics);
        return $statistics;
    }

    //获取本次所查所有月份
    /**
     * 获取所有月份数组
     * @param [type] $startMonth
     * @param [type] $endMonth
     * @return array
     */
    private function getAllMonthArr($startMonth, $endMonth): array
    {
        $monthArr = [$endMonth];
        while ($startMonth < $endMonth) {
            $monthArr[]  = (string)$startMonth;
            $startMonth += 1;
            $year        = mb_substr($startMonth, 0, 4);
            $month       = mb_substr($startMonth, 4);

            if ($month + 1 > 13) {
                $year       = $year + 1;
                $month      = '01';
                $startMonth = $year . $month;
            }
        }

        rsort($monthArr);
        return $monthArr;
    }
}
