<?php

namespace App\Admin\Repositories;

use App\Models\ChannelRouteScheme;
use App\Models\MerchantUrl;
use Dcat\Admin\Grid;
use Dcat\Admin\Repositories\Repository;

class ChannelRouterClass extends Repository
{

    protected $channelName;

    protected $urlIds;


    public function __construct($channelName)
    {
        $this->urlIds      = [];
        $this->channelName = $channelName;
    }

    public function get(Grid\Model $model)
    {
        return $this->data($this->channelName);
    }

    public function data($channelName)
    {
        $data       = $urls = [];
        $attributes = ChannelRouteScheme::where('extra_attributes', 'like', '%' . $channelName . '%')
            ->with('merchant:merchant_id,merchant_name')->get()->toArray();

        foreach ($attributes as $attribute) {
            $completes = $this->division($attribute['extra_attributes']);

            if (count($completes)) {
                foreach ($completes as $complete) {
                    $groupRegion = explode('|', key($complete));
                    $data[]      = [
                        'merchantName' => $attribute['merchant']['merchant_name'],
                        'name'         => $attribute['name'],
                        'urlName'      => reset($complete),
                        'group'        => $groupRegion[0],
                        'region'       => $groupRegion[1],
                    ];
                }
            }
        }

        if (count($this->urlIds)) {
            $urls = MerchantUrl::select('id', 'url_name')->whereIn('id', $this->urlIds)->where('url_status', 1)->get()->keyBy('id')->toArray();
        }

        //替换urlName
        foreach ($data as $key => &$value) {
            if (isset($urls[$value['urlName']])) {
                $value['urlName'] = $urls[$value['urlName']]['url_name'];
            } else if ($value['urlName'] !== '-') {
                unset($data[$key]);
            }
        }

        return $data;
    }

    private function division(array $extraArray, string $prepend = ''): array
    {
        $results = [];

        foreach ($extraArray as $key => $value) {
            if (is_array($value)) {
                $results = array_merge($results, $this->division($value, $prepend . $key . '|'));
            } else {
                if ($key == $this->channelName) {
                    $data = explode('|', $prepend . $key);
                    $results[] = [$data[6] . '|' .$data[3] . '/' . $data[4] . '/' . $data[5] => $data[0]];
                    $this->urlIds[] = $data[0];
                }
            }
        }

        return $results;
    }
}
