<?php

namespace App\Admin\Exceptions;

use App\Classes\Supports\Log;
use App\Jobs\SendSlsLog;
use App\Models\CardHolder;
use App\Models\CardHolderChannelReports;
use App\Services\Virtual\VirtualServiceFacade;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use App\Models\CardVirtual as CardVirtualModel;

class CardHolderBind extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $virtualId    = $this->payload['virtual_id'] ?? '';
        $cardHolderId = $input['cardholder_id'] ?? '';

        if (empty($virtualId) || empty($cardHolderId)) {
            return $this->response()->error(admin_trans('card-batch.labels.commit_error'));
        }

        $cardVirtualData = CardVirtualModel::with('cardHolder')->with('cardBin.CardBinSupplier')->find($virtualId);
        if (empty($cardVirtualData)) {
            return $this->response()->error(admin_trans('card-holder.labels.虚拟卡数据不存在'));
        }

        if ($cardVirtualData->cardholder_id) {
            return $this->response()->error(admin_trans('card-holder.labels.该虚拟卡已绑定持卡人信息'));
        }

        if ($cardVirtualData->status != CardVirtualModel::ACTIVATION) {
            return $this->response()->error(admin_trans('card-virtual.fields.virtual_info.card_not_activated'));
        }

        if (CardHolder::where('id', $cardHolderId)->where('audit_status', '<>', CardHolder::CARD_HOLDER_AUDIT_STATUS_PASS)->count()) {
            return $this->response()->error(admin_trans('card-holder.labels.持卡人信息审核未通过'));
        }

        $cardVirtualData->cardholder_id = $cardHolderId;

        try {
            $result = VirtualServiceFacade::getService($cardVirtualData->cardBin->CardBinSupplier->file_name)
                ->setGatewayConfig($cardVirtualData->cardBin->config)
                ->bindCardHolder($cardVirtualData->toArray());
            if (!isset($result['code']) || $result['code'] != '200') {
                return $this->response()->error($result['message']);
            }

            $cardVirtualData->save();
            return $this->response()->success(admin_trans('card-holder.labels.绑定成功'))->refresh();
        } catch (\Exception $e) {
            Log::warning($e->getMessage());
            dispatch(new SendSlsLog(
                ['message' => "请求渠道绑定持卡人失败 bin_supplier_id: " . $cardVirtualData->cardBin->bin_supplier_id . $e->getMessage()],
                [],
                'warning',
            ));
        }

        return $this->response()->error(admin_trans('card-holder.labels.绑定失败'))->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->confirm(admin_trans('card-holder.labels.您确定要绑定该持卡人吗'));
        $this->select('cardholder_id', admin_trans('card-holder.fields.cardholder_id'))->options(
            CardHolder::where('status', CardHolder::CARD_HOLDER_STATUS_ENABLE)
                ->where('audit_status', CardHolder::CARD_HOLDER_AUDIT_STATUS_PASS)
                ->get()
                ->pluck('full_name', 'id')
                ->toArray()
        )->required();
    }
}