<?php

namespace App\Admin\Exceptions;

use App\Jobs\SendSlsLog;
use App\Models\CardBin;
use App\Services\Virtual\VirtualServiceFacade;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use App\Models\CardVirtual;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SyncSettlements extends Form implements LazyRenderable
{
    use LazyWidget;

    // 处理请求
    public function handle(array $input)
    {
        $ids = explode(',', $input['id'] ?? null);
        if (count($ids) > 20) {
            return $this->response()->error(admin_trans('card-batch.labels.limit_error') . '20');
        }

        $ids       = array_filter($ids);
        $startTime = $input['startTime'] ?? date('Y-m-d', strtotime('-1 day'));
        $endTime   = $input['endTime'] ?? date('Y-m-d');

        if ($startTime > $endTime) {
            return $this->response()->error(admin_trans('card-batch.labels.start_time_error'));
        }

        if (strtotime($endTime) - strtotime($startTime) > 24 * 60 * 60 * 7) {
            return $this->response()->error(admin_trans('card-batch.labels.interval_time_error'));
        }

        $errorMsg         = '';
        $statistics       = ['total' => count($ids), 'success' => 0, 'error' => 0];
        $cardVirtualInfos = CardVirtual::whereIn('virtual_id', $ids)->with('cardBin.CardBinSupplier')->get();
        foreach ($cardVirtualInfos as $virtualInfo) {
            if (!$virtualInfo->trans_id) {
                $statistics['error'] += 1;
                $errorMsg            .= $statistics['error'] . admin_trans('card-virtual.fields.virtual_info.card_not_exist');
                continue;
            }

            if ($virtualInfo->status != CardVirtual::ACTIVATION) {
                $statistics['error'] += 1;
                $errorMsg            .= $statistics['error'] . admin_trans('card-virtual.fields.virtual_info.card_not_activated');
                continue;
            }

            if (Auth::user()['merchant_id'] && $virtualInfo->merchant_id != Auth::user()['merchant_id']) {
                $statistics['error'] += 1;
                $errorMsg            .= $statistics['error'] . admin_trans('card-virtual.fields.virtual_info.illegal');
                continue;
            }

            $data = [
                'startTime' => $startTime,
                'endTime'   => $endTime,
                'trans_id'  => $virtualInfo->trans_id,
            ];
            try {
                VirtualServiceFacade::getService($virtualInfo->cardBin->CardBinSupplier->file_name)->setGatewayConfig($virtualInfo->cardBin->config)->inquirySettlement($data);
                $statistics['success'] += 1;
            } catch (\Exception $e) {
                $statistics['error'] += 1;
                $errorMsg            .= $statistics['error'] . ':error';
                Log::warning('SyncSettlements Error', ['error' => $e->getMessage(), 'params' => $data]);
                dispatch(new SendSlsLog(
                    ['message' => 'SyncSettlements Error'],
                    ['error' => $e->getMessage(), 'params' => $data],
                    'warning',
                ));
            }
        }

        $msg = admin_trans('card-virtual.fields.virtual_info.add_bulk_sync_settle_data') . ':' . $statistics['total'] . '。  success:' . $statistics['success'] . ' fail:' . $statistics['error'] . "\n" . $errorMsg;
        return $this->response()->success($msg)->refresh();
    }

    public function form()
    {
        $this->hidden('id')->attribute('id', 'batchsp-id'); //获取到卡id列
        $this->date('startTime', admin_trans('card-virtual.fields.start_date'))->default(date('Y-m-d', strtotime('-1 day')))->required();
        $this->date('endTime', admin_trans('card-virtual.fields.end_date'))->default(date('Y-m-d'))->required();
    }
}
