<?php

namespace App\Admin\Extensions\Table;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;


class SuccessRateMerchantSheet extends DefaultValueBinder implements WithTitle, WithCustomValueBinder, WithCustomStartCell, WithCustomChunkSize, WithStrictNullComparison, FromArray, WithColumnWidths, WithStyles
{
	use Exportable;

	protected $inputs;

	protected $data;

	protected $time;

	protected $ccType;

	public function __construct(array $inputs = [], array $data = [])
	{
		$this->inputs = $inputs;
		$this->data   = $data;
	}

	public function title(): string
	{
		return '商户成功率表格';
	}

	public function chunkSize(): int
	{
		return 500;
	}

	public function bindValue(Cell $cell, $value)
	{
		$cell->setValueExplicit($value, DataType::TYPE_STRING);

		return true;
	}

	// 导出数组
	public function array(): array
	{
		// 筛选参数
		$searchData   = $this->inputs;
		$this->ccType = $searchData['cc_type'] ?? '';
		$startTime    = $searchData['date_stat.start'] ?? date('Y-m-d', strtotime('yesterday'));
		$endTime      = $searchData['date_stat.end'] ?? date('Y-m-d', strtotime('yesterday'));
		$isSameDay    = $startTime === $endTime;
		$startMonth   = date('M', strtotime($startTime));
		$startDay     = date("d", strtotime($startTime));

		$endMonth = date('M', strtotime($endTime));
		$endDay   = date("d", strtotime($endTime));

		$timePeriod = $isSameDay ? $startDay . "—" . $startMonth : $startDay . "—" . $startMonth . ' To ' . $endDay . "—" . $endMonth;
		$this->time = $timePeriod;
		// 表格内容标题
		$title[] = [
			'', '', $timePeriod,
		];


		$finallyData = array_merge(
			$title,
			[['']], // 空行
			[['']]
		);

		return $finallyData;
	}


	// 从哪一行开始
	public function startCell(): string
	{
		return 'A1';
	}

	public function columnWidths(): array
	{
		return [
			'A' => 20,
			'B' => 30,
			'C' => 20,
			'D' => 30,
			'E' => 10,
			'F' => 10,
			'G' => 20,
			'H' => 40,
			'I' => 10
		];
	}

	public function styles(Worksheet $sheet)
	{
		// excel左边的数据
		$tableData = array_map(function ($item) {
			return array_slice($item, 0, 6, true);
		}, $this->data);

		// 生成饼图 所需的数据
		$pieData = array_map(function ($item) {
			return array_slice($item, 6, null, true);
		}, $this->data);

		// 初始化起始行和列
		$startTableRow = $startPieRow =3;
		// H 列对应的索引为 8
		$startColumn   = 8;
		// 遍历每个键值对生成相应的单元格数据
		foreach ($tableData as $merchant => $info) {
			$startTableRow += 3; // 写完一个商户多三行
			$startPieRow    = $startTableRow;
			// 写入每个商户的数据
			foreach ($info as $key => $value) {
				$sheet->setCellValue('A' . $startTableRow, ucwords(str_replace("_", " ", $key)));
				$sheet->setCellValue('B' . $startTableRow, $value);
				$startTableRow += 2; // 间隔一行写入数据
			}

			arsort($pieData[$merchant]);
			foreach ($pieData[$merchant] as $failReasonKey => $failQty) {
				// 写入数据
				$sheet->setCellValueByColumnAndRow($startColumn, $startPieRow, $failReasonKey);
				$sheet->setCellValueByColumnAndRow($startColumn + 1, $startPieRow, $failQty);
				$startPieRow++;
			}
			
			$startTableRow = max([$startTableRow, $startPieRow]);
		}
	}
}
