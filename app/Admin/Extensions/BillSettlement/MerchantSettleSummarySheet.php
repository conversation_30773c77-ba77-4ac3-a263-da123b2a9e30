<?php

namespace App\Admin\Extensions\BillSettlement;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use App\Exports\Traits\SettleSummary;

class MerchantSettleSummarySheet extends DefaultValueBinder implements WithTitle, WithCustomValueBinder, WithCustomStartCell, WithCustomChunkSize, WithStrictNullComparison, FromArray, WithColumnWidths, WithStyles
{
    use Exportable;
    use SettleSummary;
}
