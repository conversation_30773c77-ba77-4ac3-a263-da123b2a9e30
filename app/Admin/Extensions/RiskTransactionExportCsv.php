<?php

namespace App\Admin\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class RiskTransactionExportCsv implements WithHeadings, FromArray
{
    use Exportable;

    protected $refund;

    public function __construct(array $refund = [])
    {
        $this->refund = $refund;
    }

    public function array(): array
    {
        return $this->refund;
    }

    public function headings(): array
    {
        return ['订单号','商户订单号','BID','商户名','交易网站','账单标识','卡号','货币','支付金额','是否3d','持卡人姓名','持卡人邮箱','IP','是否退款','是否拒付','状态','完成时间','创建时间'];
    }
}
