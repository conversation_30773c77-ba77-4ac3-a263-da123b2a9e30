<?php

namespace App\Admin\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TrackTracksExportCsv implements WithHeadings, FromArray
{
    use Exportable;

    protected $tracks;

    public function __construct(array $tracks = [])
    {
        $this->tracks = $tracks;
    }

    public function array(): array
    {
        return $this->tracks;
    }

    public function headings(): array
    {
        return ['商户订单号', '运单类型', '运单号', '生效状态', '妥投状态', '更新时间', '订单号', 'MID', '商户名称', 'BID', '账单标识', '是否退款', '是否拒付', '是否结算', '审核状态', 'API返回状态', '邮编', '国家', '地区', '城市', '收件人', '备注', '添加时间', '审核人', '审核时间', '妥投日期'];
    }
}
