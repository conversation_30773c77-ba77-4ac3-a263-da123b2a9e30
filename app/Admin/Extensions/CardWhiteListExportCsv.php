<?php

namespace App\Admin\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class CardWhiteListExportCsv implements WithHeadings, FromArray
{
    use Exportable;

    protected $detail;

    public function __construct(array $detail = [])
    {
        $this->detail = $detail;
    }

    public function array(): array
    {
        return $this->detail;
    }

    public function headings(): array
    {
        return ['卡号'];
    }
}
