<?php

namespace App\Admin\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class LocalRefundErrorsExportCsv implements WithHeadings, FromArray
{
    use Exportable;

    protected $refund;

    public function __construct(array $refund = [])
    {
        $this->refund = $refund;
    }

    public function array(): array
    {
        return $this->refund;
    }

    public function headings(): array
    {
        return ['退款订单号', '订单号', '退款渠道订单号', '账单标识', '原始订单币种', '原始订单金额', '币种', '金额', '状态', '返回代码', '返回结果', '备注', '完成时间', '创建时间', '更新时间'];
    }
}
