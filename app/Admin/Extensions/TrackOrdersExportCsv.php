<?php

namespace App\Admin\Extensions;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TrackOrdersExportCsv implements WithHeadings, FromArray
{
    use Exportable;

    protected $tracks;

    public function __construct(array $tracks = [])
    {
        $this->tracks = $tracks;
    }

    public function array(): array
    {
        return $this->tracks;
    }

    public function headings(): array
    {
        return ['账单标识', 'MID', 'BID', '商户名称', '交易订单号', '商户订单号', '订单货币', '订单金额', '授权货币', '授权金额', '交易类型', '交易状态', '支付时间'];
    }
}
