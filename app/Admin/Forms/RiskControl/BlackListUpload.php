<?php

namespace App\Admin\Forms\RiskControl;

use App\Models\MerchantBlacklist;
use App\Models\UploadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Validator;
use SplFileObject;

class BlackListUpload extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        // 获取外部传递参数
        $files     = $input['excelFile'];
        $fileNames = [];
        
        foreach ($files as $file) {
            $fileUrl    = public_path('/data/blackList') . '/' . $file;
            $fileName   = pathinfo($fileUrl)['basename'];
            $spl_object = new SplFileObject($fileUrl, 'rb');
            $spl_object->seek(filesize($fileUrl));
            $count = $spl_object->key();

            if ($count > 50000) {
                return $this->response()->error($fileName . '文件行数超过限制');  
            }

            if (($handle = fopen($fileUrl, "r")) !== FALSE) {
                $i = 0;
                while (($fileData = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $i++;
                    
                    if (empty($fileData[0]) || empty($fileData[1])) {
                        return $this->response()->error($fileName . '文件第' . $i . '行内容为空'); 
                    }
                    
                    // 处理中文
                    $fileData[0] = mb_convert_encoding($fileData[0], "UTF-8", "GBK");
                    $fileData[1] = trim($fileData[1]);
                    
                    if (!in_array($fileData[0], MerchantBlacklist::$typesMap)) {
                        return $this->response()->error($fileName . '文件第格式不正确');
                    }
                    
                    $rule = 'required';
                    switch ($fileData[0]) {
                        case MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_IP]:
                            $rule .= '|ip';
                            break;
                        case MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARD_NUMBER]:
                            $rule .= '|between:14,19|not_regex:/^[+-]?[0-9]+([.][0-9]*)?[Ee][+-]?[0-9]+$/';
                            break;
                        case MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARDHOLDER_NAME]:
                            if (count(explode(' ', $fileData[1])) != 2) {
                                return $this->response()->error($fileName . '文件第' . $i . '行格式错误，请检查');
                            }
                            break;
                        case MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_MAILBOX]:
                            $rule .= '|email:rfc,dns';
                            break;
                        case MerchantBlacklist::$typesMap[MerchantBlacklist::TYPE_CARD_BIN]:
                            $rule .= '|size:6|not_regex:/^[+-]?[0-9]+([.][0-9]*)?[Ee][+-]?[0-9]+$/';
                            break;
                    }
                    
                    $messages = [
                        'not_regex' => 'The format is scientific notation',
                    ];

                    $value     = ['limit_value' => $fileData[1]];
                    $validator = Validator::make($value, [
                        'limit_value' => $rule,
                        $messages,
                    ]);
                    
                    if ($validator->fails()) {
                        return $this->response()->error($fileName . '文件第' . $i . '行' . $validator->errors()->messages()['limit_value'][0]);
                    }
                }
                 
                fclose($handle);
            } else {
                return $this->response()->error($fileName . '文件错误'); 
            }

            $fileNames[] = $fileName; // 获取文件名
        }

        $user   = Admin::user();
        $center = UploadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $fileNames,
            'import_identity' => 'blackListImport',
            'url'             => $files,
        ]);

        if ($center) {
            return $this->response()->success('导入任务已添加，请到上传中心查看任务进度')->refresh();
        }

        return $this->response()->error('添加导入任务失败');
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $url = config('app.url') . '/download/blackList.csv';
        $this->multipleFile('excelFile')->accept('csv')->autoUpload()->required()->maxSize(102400)->disk('blackList')->help('最多导入10个文件；单个文件限制5w行。')->name(function ($file) {
            // 防止文件名重复情况，原文件名加上时间
            return time() . '-' . str_replace(' ', '', $file->getClientOriginalName());
        })->limit(10);
        $this->button('<a href="' . $url . '" download="blackList.csv" style="color: #ffffff" target="_blank">示例文件下载</a>');
    }
}
