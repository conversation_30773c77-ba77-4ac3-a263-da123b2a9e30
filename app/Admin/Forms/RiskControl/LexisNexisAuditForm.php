<?php

namespace App\Admin\Forms\RiskControl;

use App\Models\LexisNexisScanDetailRelation;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class LexisNexisAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        if (!$this->auditHandelValidate($this->payload, $input)) {
            return $this->response()->error('无法获取当前要审核的LN信息');
        }

        $id = $this->payload['id'] ?? explode(',', $input['id']);

        if ($input['audit_result'] == LexisNexisScanDetailRelation::LEXIS_NEXIS_RESULT_FALSE_MATCH &&
            ($input['reject_reason'] == LexisNexisScanDetailRelation::LEXIS_NEXIS_REJECT_REASON_OTHER_REASON ||
            $input['reject_reason'] == LexisNexisScanDetailRelation::LEXIS_NEXIS_REJECT_REASON_MATCH_NOT_RELATED)) {
            $input['audit_remark'] = $input['reject_audit_remark'];
        }

        $auditResult = LexisNexisScanDetailRelation::lexisNexisStatusMap[$input['audit_result']];
        // TODO 当前只有初审，所以初审完成可以直接更新最终审核结果，后续如果增加了升级审核逻辑需要修改
        $adminUser = Admin::user();
        $builder = LexisNexisScanDetailRelation::query();
        $builder->whereIn('id', $id)->
            where('status', LexisNexisScanDetailRelation::LEXIS_NEXIS_STATUS_PENDING)->
            update([
                'preliminary_approval_result' => $input['audit_result'],
                'final_approval_result' => $auditResult,
                'preliminary_approval_note' => $input['audit_remark'],
                'preliminary_manager_id' => $adminUser['id'],
                'status' => $auditResult,
            ]);

        return $this->response()->success('审核成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->disableResetButton();
        $this->hidden('id')->attribute('id', 'lexis-nexis-audit-form-id');
        $this->select('audit_result', admin_trans('lexis-nexis-audit-form.fields.audit_result'))
            ->when(2, function (Form $form) {
                $form->select('reject_reason', admin_trans('lexis-nexis-audit-form.fields.reject_reason'))
                    ->when('>=', 5, function (Form $form) {
                        $form->textarea('reject_audit_remark', admin_trans('lexis-nexis-audit-form.fields.reject_audit_remark'))
                            ->rules('required_if:reject_reason,5,6', [
                                'required_if' => admin_trans('lexis-nexis-audit-form.labels.reject_reason_required_if')
                            ]);
            })->options(admin_trans('lexis-nexis-audit-form.options.reject_reason'))
                    ->default(0)->disableClearButton()->required();
        })->when('!=', 2, function (Form $form) {
            $form->textarea('audit_remark', admin_trans('lexis-nexis-audit-form.fields.audit_remark'))
                ->rules('required_if:audit_result,0,1', [
                    'required_if' => admin_trans('lexis-nexis-audit-form.labels.audit_remark_required_if')
                    ]);
        })->options(admin_trans('lexis-nexis-audit-form.options.audit_result'))
            ->default(0)->disableClearButton()->required()->rules('required|in:0,1,2', [
                'in' => admin_trans('lexis-nexis-audit-form.labels.audit_result_in'),
            ]);
        $this->confirm(admin_trans('lexis-nexis-audit-form.labels.confirm'));
    }

    private function auditHandelValidate(array $payload, array $input): bool
    {
        if (empty($payload)) {
            return false;
        }

        if (!isset($payload['id']) && !isset($input['id'])) {
            return false;
        }

        return true;
    }
}
