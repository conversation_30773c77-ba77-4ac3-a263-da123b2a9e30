<?php

namespace App\Admin\Forms\Dispute;

use App\Admin\Imports\ChargebackImport;
use Dcat\Admin\Widgets\Form;

class ChargebackUpload extends Form
{
    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        set_time_limit(0);
        ini_set('memory_limit', '512M');

        // 获取外部传递参数
		$file = $input['file'];
        
        $chargebackImport = new ChargebackImport();
        $msg = $chargebackImport->import($file);

        if ($msg['success']) {
            return $this->response()->success($msg['msg'])->refresh();
        }

        return $this->response()->error($msg['msg']);
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $url = config('app.url') . '/download/chargeback.xlsx';
        $this->file('file')->accept('xlsx')->autoUpload()->required()->maxSize(2048);
        $this->button('<a href="' . $url . '" download="chargeback.xlsx" style="color: #ffffff" target="_blank">示例文件下载</a>');
    }
}
