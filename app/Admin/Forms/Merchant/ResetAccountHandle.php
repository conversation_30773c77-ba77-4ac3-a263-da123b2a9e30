<?php

namespace App\Admin\Forms\Merchant;

use App\Classes\Supports\Arr;
use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Traits\LazyWidget;

class ResetAccountHandle extends Form implements LazyRenderable
{
	use LazyWidget;

	/**
	 * Handle the form request.
	 *
	 * @param array $input
	 *
	 * @return mixed
	 */
	public function handle(array $input)
	{
		$merchantId   = $input['merchant_id'] ?? '';
		$merchantName = $input['name'] ?? '';

		if (empty($merchantId) || empty($merchantName)) {
			return $this->response()->error('商户不存在,请检查重试')->refresh();
		}

		if ($merchantName == 'all_user') {
			$updateRet = User::where('merchant_id', $merchantId)->update(['google2fa_secret' => Null]);
		} else {
			$updateRet = User::where('merchant_id', $merchantId)
				->where('name', $merchantName)->update(['google2fa_secret' => Null]);
		}

		if (empty($updateRet)) {
			return $this->response()->error('账号重置失败')->refresh();
		}

		return $this->response()->success('账号重置成功')->refresh();
	}

	/**
	 * Build a form here.
	 */
	public function form()
	{
        $this->disableResetButton();
		$merchantArr = User::where('merchant_id', $this->payload['merchant_id'])
			->where('status', User::STATUS_ENABLE)
			->pluck('type', 'name')
			->map(static function ($item, $key) {
				$show = User::$typesMap[$item].'：';

				return $show . $key;
			})
			->toArray();

        if (count($merchantArr)) {
            $merchantArr = Arr::prepend($merchantArr, '全部', 'all_user');
        }

		$this->select('name', '名称')->options($merchantArr)->required();

		// 设置隐藏表单，传递id
		$this->hidden('merchant_id')->value($this->payload['merchant_id']);
	}
}
