<?php

namespace App\Admin\Forms\Merchant;

use App\Models\MerchantBusiness;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Dcat\Admin\Contracts\LazyRenderable;

class Businesses extends Form implements LazyRenderable
{
    use LazyWidget; // 使用异步加载功

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        try {
            $merchan_business = MerchantBusiness::find($this->payload['id']);

            $merchan_business->order_uprate    = $input['order_uprate'];
            $merchan_business->parities_uprate = $input['parities_uprate'];

            $merchan_business->save();

            return $this->response()->success('配置成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $merchan_business = MerchantBusiness::find($this->payload['id']);

        $order_uprate    = admin_trans('business.fields.order_uprate');
        $parities_uprate = admin_trans('business.fields.parities_uprate');

        $this->rate('order_uprate', $order_uprate)->value($merchan_business->order_uprate)
            ->rules('required|numeric|between:0,1000', [
                'required' => $order_uprate . '不能为空',
                'numeric'  => $order_uprate . '必须为数字',
                'between'  => $order_uprate . '数据不符合',
            ]);
        $this->rate('parities_uprate', $parities_uprate)->value($merchan_business->parities_uprate)
            ->rules('required|numeric|between:0,1000', [
                'required' => $parities_uprate . '不能为空',
                'numeric'  => $parities_uprate . '必须为数字',
                'between'  => $parities_uprate . '数据不符合',
            ]);
    }
}
