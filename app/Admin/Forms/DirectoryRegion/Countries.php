<?php

namespace App\Admin\Forms\DirectoryRegion;

use App\Models\DirectoryCountry;
use App\Models\DirectoryRegion;
use Dcat\Admin\Widgets\Form;
use Symfony\Component\HttpFoundation\Response;

class Countries extends Form
{
    // 增加一个自定义属性保存用户ID
    protected $id;

    // 构造方法的参数必须设置默认值
    public function __construct($id = null)
    {
        $this->id = $id;

        parent::__construct();
    }

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return Response
     */
    public function handle(array $input)
    {
        $countryIds = $input['countryIds'] ? array_filter($input['countryIds']) : null;
        $regionId   = $input['id'] ?? null;

        if (!$regionId) {
            return $this->response()->error('参数错误');
        }

        $region = DirectoryRegion::find($regionId);

        if (!$region) {
            return $this->response()->error('地区不存在');
        }

        $region->country($countryIds);

        return $this->response()->success('更新完成')->redirect('/directory_regions');
    }

    /**
     * Build a form here.
     */
    public function form()
    {
    	$this->hidden('countryIds');

        if (!empty($this->id)) {
            $countries = DirectoryRegion::find($this->id)->countries()->get(['country_id'])->pluck('country_id')->toArray();

            $this->checkbox('countryIds', '国家列表')->options(DirectoryCountry::getAllData())
                ->default($countries)
                ->saving(function ($value) {
                    // 转化成json字符串保存到数据库
                    return json_encode($value);
                });

            // 设置隐藏表单，传递地区id
            $this->hidden('id')->value($this->id);
        } else {
	        $this->hidden('id');
        }
    }
}
