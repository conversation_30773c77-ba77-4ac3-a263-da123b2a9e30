<?php

namespace App\Admin\Metrics\Dashboard;

use App\Admin\Metrics\DeviceWorth;
use Dcat\Admin\Widgets\Metrics\Card;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Laravel\Horizon\WaitTimeCalculator;

class Wait extends Card
{
    /**
     * 卡片底部内容.
     *
     * @var string|Renderable|\Closure
     */
    protected $footer;

    /**
     * 初始化卡片.
     */
    protected function init()
    {
        parent::init();

        $this->title('任务最长等待时间');
    }

    /**
     * 处理请求.
     *
     * @param Request $request
     *
     * @return void
     */
    public function handle(Request $request)
    {
        $waitData = collect(app(WaitTimeCalculator::class)->calculate())->take(1)->toArray();

        if ($waitData) {
            $maxWaitTime  = array_values($waitData)[0];
            $maxWaitQueue = explode(':', array_keys($waitData)[0])[1];
            $this->footer = $maxWaitQueue;
        }

        $this->content(!empty($maxWaitTime) ? $maxWaitTime : '-');
    }

    /**
     * 渲染卡片内容.
     *
     * @return string
     */
    public function renderContent()
    {
        $content = parent::renderContent();

        return <<<HTML
<div class="d-flex justify-content-between align-items-center mt-1" style="margin-bottom: 2px">
    <h2 class="ml-1 font-lg-1">{$content}</h2>
</div>
<div class="ml-1 mt-1 font-weight-bold text-80">
    {$this->renderFooter()}
</div>
HTML;
    }

    /**
     * 渲染卡片底部内容.
     *
     * @return string
     */
    public function renderFooter()
    {
        return $this->toString($this->footer);
    }
}
