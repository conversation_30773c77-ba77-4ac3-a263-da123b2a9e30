<?php

namespace App\Admin\Metrics\DataStatistics;

use App\Models\Channel;
use App\Models\DirectoryCc;
use App\Models\DirectoryChannelMainBody;
use App\Models\DirectoryCountry;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use App\Models\RiskMcc;
use Dcat\Admin\Admin;
use Dcat\Admin\Widgets\Box;
use Illuminate\Support\Arr;

class TransactionAnalysis
{

    public function handle()
    {
        $midList = Merchant::pluck('merchant_name', 'merchant_id')
            ->map(static function ($item, $key) {
                return $item . ':' . $key;
            })->toArray();
        $midList = Arr::prepend($midList, '全部', '全部');
        $midList = Arr::prepend($midList, '搜索', '搜索');

        $mid = [
            "id"      => "mid",
            "options" => $midList
        ];

        $midMenu = TranDropdown::make($mid)->button('merchant_id')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-mid" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-mid' data-tranmid='{$k}'>{$v}</a>";
            });

        $bidList = MerchantBusiness::pluck('business_id')->toArray();
        $bidList = Arr::prepend($bidList, '全部');
        $bidList = Arr::prepend($bidList, '搜索');

        $bid = [
            "id"      => "bid",
            "options" => $bidList
        ];

        $bidMenu = TranDropdown::make($bid)->button('business_id')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-bid" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }

                return "<a class='tran-switch-bar-bid' data-tranbid='{$k}'>{$v}</a>";
            });

        $merUrlList = MerchantUrl::pluck('url_name', 'id')->toArray();
        $merUrlList = [0 => '搜索', -1 => '全部'] + $merUrlList;

        $mer = [
            "id"      => "url",
            "options" => $merUrlList
        ];

        $merUrlMenu = TranDropdown::make($mer)->button('url_name')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-url" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }

                return "<a class='tran-switch-bar-url' data-tranurl='{$k}'>{$v}</a>";
            });

        $dirList = RiskMcc::query()->where('overall_risk_rating', '!=', 'Prohibited')->pluck('mcc', 'id')->toArray();
        $dirList = ['全部'] + $dirList;

        $dir = [
            "id"      => "mcc",
            "options" => $dirList
        ];

        $dirMenu = TranDropdown::make($dir)->button('MCC')->click()
            ->map(function ($v, $k) {
                return "<a class='tran-switch-bar-mcc' data-tranmcc='{$k}'>{$v}</a>";
            });

        $dirCurrList = DirectoryCurrency::pluck('code')->toArray();
        $dirCurrList = Arr::prepend($dirCurrList, '全部');
        $dirCurrList = Arr::prepend($dirCurrList, '搜索');

        $dirCurr = [
            "id"      => "curr",
            "options" => $dirCurrList
        ];

        $dirCurrMenu = TranDropdown::make($dirCurr)->button('currency')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-curr" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-curr' data-trancurr='{$k}'>{$v}</a>";
            });

        $dirCcsList = DirectoryCc::isRiskControl()->pluck('cc_type')->toArray();
        $dirCcsList = Arr::prepend($dirCcsList, '全部');

        $dirCcs = [
            "id"      => "ccs",
            "options" => $dirCcsList
        ];

        $dirCcsMenu = TranDropdown::make($dirCcs)->button('cc_type')->click()
            ->map(function ($v, $k) {
                return "<a class='tran-switch-bar-ccs' data-tranccs='{$k}'>{$v}</a>";
            });

        $CountryList = DirectoryCountry::pluck('name')->toArray();
        $CountryList = Arr::prepend($CountryList, '全部');
        $CountryList = Arr::prepend($CountryList, '搜索');

        $Country = [
            "id"      => "country",
            "options" => $CountryList
        ];

        $countryMenu = TranDropdown::make($Country)->button('card_country')->click()
            ->map(function ($v, $k) {
                if ($k == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-country" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                return "<a class='tran-switch-bar-country' data-trancountry='{$k}'>{$v}</a>";
            });

        $channelModel= Channel::query()->select('id', 'channel', 'channel_pid_id', 'channel_supplier_id')->with(['channelSupplier:id,supplier_name', 'channelPid:id,channel_pid'])->get();
        $ChannelList = $channelModel->pluck('id', 'channel')->toArray();
        $ChannelList = Arr::prepend($ChannelList, '全部');
        $ChannelList = Arr::prepend($ChannelList, '搜索');
        $ChannelList = array_unique($ChannelList);

        $Channel = [
            "id"      => "channel",
            "options" => $ChannelList
        ];

        $ChannelMenu = TranDropdown::make($Channel)->button('channel')->click()
            ->map(function ($v, $k) {
                if ($v == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-channel" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                if ($v == '全部') {
                    return "<a class='tran-switch-bar-channel' data-trancountry='{$v}'>全部</a>";
                }
                return "<a class='tran-switch-bar-channel' data-trancountry='{$v}'>{$k}</a>";
            });

        $SupplierList = $channelModel->pluck('channelSupplier.supplier_name', 'channelSupplier.id')->toArray();
        $SupplierList = Arr::prepend($SupplierList, '全部', '全部');
        $SupplierList = Arr::prepend($SupplierList, '搜索', '搜索');

        $Channel = [
            "id"      => "supplier",
            "options" => array_unique($SupplierList)
        ];

        $SupplierMenu = TranDropdown::make($Channel)->button('supplier')->click()
            ->map(function ($v, $k) {
                if ($v == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-supplier" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                if ($v == '全部') {
                    return "<a class='tran-switch-bar-supplier' data-trancountry='{$v}'>全部</a>";
                }
                return "<a class='tran-switch-bar-supplier' data-trancountry='{$k}'>{$v}</a>";
            });

        $pidList = $channelModel->pluck('channelPid.channel_pid', 'channelPid.id')->toArray();
        $pidList = Arr::prepend($pidList, '全部', '全部');
        $pidList = Arr::prepend($pidList, '搜索', '搜索');

        $Pid = [
            "id"      => "pid",
            "options" => array_unique($pidList)
        ];

        $PidMenu = TranDropdown::make($Pid)->button('pid')->click()
            ->map(function ($v, $k) {
                if ($v == '搜索') {
                    return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-pid" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
                }
                if ($v == '全部') {
                    return "<a class='tran-switch-bar-pid' data-trancountry='{$v}'>全部</a>";
                }
                return "<a class='tran-switch-bar-pid' data-trancountry='{$k}'>{$v}</a>";
            });

		$channelMainBodyList = DirectoryChannelMainBody::get()->pluck('full_name', 'id')->toArray();
		$channelMainBodyList = Arr::prepend($channelMainBodyList, '全部', '全部');
		$channelMainBodyList = Arr::prepend($channelMainBodyList, '搜索', '搜索');

		$mainBodyId = [
			"id"      => "main_bodys_id", // 下拉框的id
			"options" => array_unique($channelMainBodyList) // 下拉框的选项
		];

		// 按钮的文本
		$mainBodyIdMenu = TranDropdown::make($mainBodyId)->button('main_body')->click()
			->map(function ($v, $k) {
				if ($k == '搜索') {
					// 生成筛选组件input的id => 下面进行组件的筛选时需要对应id
					return '<div style="padding: .35rem 1.38rem;"><input id="transaction-drop-search-main_body_id" style="border: 1px solid #586cb1;border-radius: .25rem !important;width: 100%;"></div>';
				}
				// 生成下拉框的组件switch的id => 下面的点击事件需要用到
				return "<a class='tran-switch-bar-main_body_id' data-tranbid='{$k}'>{$v}</a>";
			});


		$transactionBox = TransactionBox::make()
            ->title("交易分析图表")
            ->fetching('$("#tran-box").loading()')
            ->fetched('$("#tran-box").loading(false)')
            ->fetched(self::JS())
			->click(['.tran-switch-bar-mid', '.tran-switch-bar-bid', '.date-submit', '.tran-switch-bar-url', '.tran-switch-bar-ccs', '.tran-switch-bar-mcc', '.tran-switch-bar-curr', '.tran-switch-bar-country', '.tran-switch-bar-channel', '.tran-switch-bar-supplier', '.tran-switch-bar-pid', '.tran-switch-bar-main_body_id'])
            ->id("tran-chart-box");

        $box             = Box::make()->title("交易分析")->id("tran-box");
        $firstendDate    = date("Y/m/d");
        $firstStartDate  = date("Y/m/d", strtotime(date("Y-m-d") . " -6 day"));
        $secondendDate   = date("Y/m/d", strtotime(date("Y-m-d") . " -7 day"));
        $secondStartDate = date("Y/m/d", strtotime(date("Y-m-d") . " -13 day"));

        Admin::js('vendor/dcat-admin/dcat/plugins/moment/moment-with-locales.min.js');
        Admin::js('vendor/dcat-admin/dcat/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.js');
        Admin::css('vendor/dcat-admin/dcat/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.css');
        Admin::script(
            '$("#firstDate1").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#firstDate2").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#secondDate1").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });
             $("#secondDate2").datetimepicker({
             format:"YYYY/MM/DD",
             locale: moment.locale("zh-CN")
            });'
        );

        $box->content(
            <<<HTML
<div style="margin: 3px;">
{$midMenu->render()}

{$bidMenu->render()}
 &nbsp;
{$merUrlMenu->render()}
&nbsp;
{$dirMenu->render()}
&nbsp;
{$dirCurrMenu->render()}
&nbsp;
{$dirCcsMenu->render()}
&nbsp;
{$countryMenu->render()}

{$SupplierMenu->render()}

{$PidMenu->render()}

<!--数据渲染-->
{$mainBodyIdMenu->render()}

{$ChannelMenu->render()}
</div>

<div style="margin: 15px;">
    <form action="/dcat-api/value" method="post" class="fist-date" id="tran-date">
        <div class="row">
            <input id="firstDate1" class="firstDate1 form-control col-lg-1"  type="text" value="{$firstStartDate}">
            <div style="padding: 8px">-</div>
            <input id="firstDate2" class="firstDate2 form-control col-lg-1"  type="text" value="{$firstendDate}">
            <div style="padding: 9px">VS</div>
            <input id="secondDate1" class="secondDate1 form-control col-lg-1"  type="text" value="{$secondStartDate}">
            <div style="padding: 8px">-</div>
            <input id="secondDate2" class="secondDate2 form-control col-lg-1"  type="text" value="{$secondendDate}">
            <div class="col-lg-4">
				<button class="date-submit btn btn-primary btn-outline" type="button">提交</button>
				&nbsp;&nbsp;<button class="btn btn-primary btn-outline" id="switchAmount" type="button">授权金额</button>
            </div>

        </div>
    </form>
</div>
{$transactionBox->render()}
HTML
        );

        return $box;
    }

    private static function JS()
    {
        //获取BID
        $business      = MerchantBusiness::selectRaw('business_id,merchant_id')->get()->toArray();
        $business_data = [];

        foreach ($business as $vo) {
            $business_data[$vo['merchant_id']][] = $vo['business_id'];
        }

        $business_data = json_encode($business_data);

        //获取账单标识
        $channel             = Channel::with(['channelSupplier', 'channelPid'])->get()->toArray();
        $channelData         = [];
        $pidData             = [];
        $supplierChannelData = [];
        $supplierPidData     = [];
        $pidChannelData      = [];

        foreach ($channel as $vo) {
            $channelData[]                                                   = $vo['channel'];
            $supplierChannelData[$vo['channel_supplier']['supplier_name']][] = $vo['channel'];

            if (!empty($vo['channel_pid']['id'])) {
                $pidChannelData[$vo['channel_pid']['channel_pid']][]                                 = $vo['channel'];
                $pidData[$vo['channel_pid']['id']]                                                   = $vo['channel_pid']['channel_pid'];
                $supplierPidData[$vo['channel_supplier']['supplier_name']][$vo['channel_pid']['id']] = $vo['channel_pid']['channel_pid'];
            }
        }

        $channelData         = json_encode($channelData);
        $pidData             = json_encode($pidData);
        $supplierChannelData = json_encode($supplierChannelData);
        $pidChannelData      = json_encode($pidChannelData);
        $supplierPidData     = json_encode($supplierPidData);


        $JS =  <<<JS

        //BID联动
        $('#bid').on('click', function(){
            let value        = $('#mid stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            let businessData = {$business_data};

            if (value != 'merchant_id' && value != '全部') {
                value        = value.split(":");
                let business = businessData[value[1]];
                let html     = '<li class="dropdown-item" style="display: block;"><a class="tran-switch-bar-bid tran-switch-screen-bid linkage-bid" data-tranbid="全部">全部</a></li>';
                let ul       = $('#bid').next('');

                for(let i = 0; i < business.length; i++) {
                    html += '<li class="dropdown-item" style="display: block;"><a class="tran-switch-bar-bid tran-switch-screen-bid linkage-bid" data-tranbid="'+ (i + 2) +'">'+ business[i] +'</a></li>'
                }

                ul.children().remove('li:gt(0)');
                ul.append(html);
            }
        })

        //PID联动
        $('#pid').on('click', function() {
            let supplierTag     = $('#supplier stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            let supplierPidData = {$supplierPidData};
            let pidData         = {$pidData};
            let pid, pidTag;

            if ($('#pid stub').length != 0) {
                pidTag = $('#pid stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            } else {
                pidTag = $('#pid').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            }

            if (supplierTag != 'supplier' && supplierTag != '全部') {
                pid  = supplierPidData[supplierTag];
            }

            if ((pidTag == 'pid' || pidTag == '全部') && (supplierTag == 'supplier' || supplierTag == '全部')) {
                pid  = pidData;
            }

            if (pid) {
                let html = '<li class="dropdown-item"><a class="tran-switch-bar-pid tran-switch-screen-pid  linkage-pid" data-tranbid="全部">全部</a></li>';
                let ul   = $('#pid').next('');

                for(let i in pid){
                    html += '<li class="dropdown-item"><a class="tran-switch-bar-pid tran-switch-screen-pid linkage-pid" data-tranbid="' + i + '">'+ pid[i] +'</a></li>'
                }

                ul.children().remove('li:gt(0)');
                ul.append(html);
            }
        })

        //账单标识联动
        $('#channel').on('click', function(){
            let supplierTag = $('#supplier stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            let pidTag, channelTag;

            if ($('#pid stub').length != 0) {
                pidTag = $('#pid stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            } else {
                pidTag = $('#pid').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            }

            if ($('#channel stub').length != 0) {
                channelTag = $('#channel stub').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            } else {
                channelTag = $('#channel').html().replace(/&nbsp;/ig,'').replace(/\s/g, "");
            }
            //增加一个pid筛选
            let channelData         = {$channelData};
            let supplierChannelData = {$supplierChannelData};
            let pidChannelData      = {$pidChannelData};
            let channel;

            if (supplierTag != 'supplier' && supplierTag != '全部') {
                channel = supplierChannelData[supplierTag];
            }

            if (pidTag != 'pid' && pidTag != '全部') {
                channel = pidChannelData[pidTag];
            }

            if ((supplierTag == 'supplier' || supplierTag == '全部') && (pidTag == 'pid' || pidTag == '全部')) {
                channel = channelData;
            }

            if (channel) {
                let html = '<li class="dropdown-item"><a class="tran-switch-bar-channel tran-switch-screen-channel linkage-channel" data-tranbid="全部">全部</a></li>';
                let ul   = $('#channel').next('');

                for(let i = 0; i < channel.length; i++) {
                    html += '<li class="dropdown-item"><a class="tran-switch-bar-channel tran-switch-screen-channel linkage-channel" data-tranbid="'+ (i + 2) +'">'+ channel[i] +'</a></li>'
                }

                ul.children().remove('li:gt(0)');
                ul.append(html);
            }
        })

        $('#switchAmount').off();
        $('#switchAmount').on('click', function () {
            let text = $(this).text();
            // 切换隐藏和显示
            if (text === '授权金额') {
                $('.apex-chart-amount-tr').addClass('amount-hidden')
                $('.apex-chart-amount-tr-payment').removeClass('amount-hidden')
                $(this).text('成功金额');
            } else if (text === '成功金额') {
                $('.apex-chart-amount-tr').removeClass('amount-hidden')
                $('.apex-chart-amount-tr-payment').addClass('amount-hidden')
                $(this).text('授权金额');
            }
            return false;
        })

        $(".dropdown-menu").css({"height" : "350px", "overflow-y" : "auto"})
        let aClass  = ['tran-switch-bar-curr', 'tran-switch-bar-country', 'tran-switch-bar-mid', 'tran-switch-bar-bid', 'tran-switch-bar-url', 'tran-switch-bar-channel', 'tran-switch-bar-supplier', 'tran-switch-bar-pid', 'tran-switch-bar-main_body_id'];
        let aId     = ['curr', 'country', 'mid', 'bid', 'url', 'channel', 'supplier', 'pid','main_bodys_id']
        let aSearch = ['transaction-drop-search-curr', 'transaction-drop-search-country', 'transaction-drop-search-mid', 'transaction-drop-search-bid', 'transaction-drop-search-url', 'transaction-drop-search-channel', 'transaction-drop-search-supplier', 'transaction-drop-search-pid', 'transaction-drop-search-main_body_id']
        for(let i = 0; i < aId.length; i++) {
            let child = $("#" + aId[i]).next().find('.' + aClass[i])
            child.parent().css("display", "block");
        }
        for(let i = 0; i < aSearch.length; i++) {
            $("#"+aSearch[i]).click(function () {
                event.stopPropagation();
            }).on("input propertychange", function() {
                let child = $("#" + aId[i]).next().find('.' + aClass[i])
                child.parent().css("display", "block");

                for(let j = 0; j < child.length; j++) {
                    if (child[j].text.toLowerCase().indexOf($(this).val().toLowerCase()) > -1) {
                        child[j].parentNode.style.display = "block";
                    } else {
                        child[j].parentNode.style.display = "none";
                    }

                    if (aSearch[i].indexOf("transaction-drop-search-mid") > -1) {
                        if (child[j].getAttribute("data-tranmid").toLowerCase().indexOf($(this).val().toLowerCase()) > -1) {
                           if (isNaN(Number($(this).val()))) {
                               child[j].parentNode.style.display = "block";
                           }
                        }
                    }
                }
             }).val("").parent().click(function() {
                 event.stopPropagation();
             }).parent().css("padding",'0').removeAttr("href");
        }
JS;
        return $JS;
    }
}
