<?php

namespace App\Admin\Metrics\DataStatistics;

use App\Models\StatOrderChargeback;
use Dcat\Admin\Widgets\ApexCharts\Chart;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Channel;
use App\Models\MerchantUrl;

class  RiskChart extends Chart
{

    protected $menus;
    protected $merUrl;
    protected $bid;
    protected $ccs;
    protected $curr;
    protected $mcc;
    protected $country;
    protected $value = [];

    public function __construct($menus = [], $options = [])
    {
        parent::__construct();
        $this->menus = $menus;
    }


    public function handle(Request $request)
    {
        $menus = $request->get("menus", []);
        $value = [];
        $data  = [];

        if ($request->get("mouthContent", -1) > 0) {
            $value['month'] = $request->get("mouthContent") ?? 0;
        }

        $data['month'] = $value['month'] ?? 6;

        if ($request->get("midContent", -1) >= 0 && $request->get("midContent") != "merchant_id" && $request->get("midContent") != "全部") {
            $data['mid'] = explode(':', $request->get("midContent"))[1] ?? '';
        }

        if (!empty($request->get("urlContent"))) {
            $data['url'] = MerchantUrl::where('url_name', $request->get("urlContent"))->value('id');
        }

        $data['bid']     = $request->get("bidContent");
        $data['mcc']     = $request->get("mccContent");
        $data['ccs']     = $request->get("ccsContent");
        $data['curr']    = $request->get("currContent");
        $data['country'] = $request->get("countryContent");

        if ($request->get("channelContent", -1) >= 0 && $request->get("channelContent") != 'channel' && $request->get("channelContent") != "全部") {
            $data['channel'] = [Channel::where('channel', $request->get("channelContent"))->value('id')];
        }

        if (count($menus) > 0) {
            if ($request->get("supplierContent", -1) >= 0 && $request->get("supplierContent") != 'supplier' && $request->get("supplierContent") != "全部") {
                $data['channel'] = array_merge($data['channel'] ?? [], array_keys($menus['supplier'], $request->get("supplierContent")));
            }
        }

        $this->setUpOptions($data);
    }

    public function parameters(): array
    {
        return [
            'menus' => $this->menus,
        ];
    }

    private function setUpOptions($data)
    {
        $m       = date("Ym", strtotime(date("Y-m-d") . "-" . $data['month'] . "month"));
        $mid     = $data['mid'] ?? "";
        $bid     = $data['bid'] ?? "";
        $url     = $data['url'] ?? "";
        $mcc     = $data['mcc'] ?? "";
        $curr    = $data['curr'] ?? "";
        $cctype  = $data['ccs'] ?? "";
        $country = $data['country'] ?? "";
        $channel = $data['channel'] ?? "";

        $where = [];

        $where[] = ["date_stat_month", ">", $m];

        if ($mid != "") {
            $where[] = ["merchant_id", "=", (string)$mid];
        }

        if ($bid != "business_id" && $bid != "全部") {
            $where[] = ["business_id", "=", (string)$bid];
        }

        if ($url != "url_name" && !in_array($url, [0, -1])) {
            $where[] = ["url_id", "=", $url];
        }

        if ($mcc != "MCC" && $mcc != 0) {
            $where[] = ["d_mcc_id", "=", $mcc];
        }

        if ($curr != "currency" && $curr != "全部") {
            $where[] = ["currency", "=", $curr];
        }

        if ($cctype != "cc_type" && $cctype != "全部") {
            $where[] = ["cc_type", "=", $cctype];
        }

        if ($country != "card_country" && $country != "全部") {
            $where[] = ["card_country", "=", $country];
        }

        $orderChargebacks = StatOrderChargeback::select([
            'date_stat_month',
            DB::raw('SUM(dishonour_qty) as dishonour_qty'),
            DB::raw('SUM(dishonour_qty1) as dishonour_qty1'),
            DB::raw('SUM(refund_qty) as refund_qty'),
            DB::raw('SUM(refund_qty1) as refund_qty1'),
            DB::raw('SUM(fraud_qty) as fraud_qty'),
            DB::raw('SUM(fraud_qty1) as fraud_qty1'),
            DB::raw('SUM(transaction_qty) as transaction_qty'),
            DB::raw('SUM(3d_transaction_amount_usd) as 3d_transaction_amount_usd'),
        ])
            ->where($where)
            ->groupBy("date_stat_month")
            ->orderBy("date_stat_month", "asc");

        if ($channel != "") {
            $orderChargebacks->whereIn('channel_id', $channel);
        }
        $orderChargebacks = $orderChargebacks->get()->toArray();

        $result = [];
        foreach ($orderChargebacks as $orderChargeback) {
            $transactionQty                     = $orderChargeback['transaction_qty'];
            $result["dishonour_qty_percent"][]  = $this->divisionRisk($orderChargeback['dishonour_qty'], $transactionQty);
            $result["dishonour_qty1_percent"][] = $this->divisionRisk($orderChargeback['dishonour_qty1'], $transactionQty);
            $result["fraud_qty_percent"][]      = $this->divisionRisk($orderChargeback['fraud_qty'], $transactionQty);
            $result["fraud_qty1_percent"][]     = $this->divisionRisk($orderChargeback['fraud_qty1'], $transactionQty);
            $result["refund_percent"][]         = $this->divisionRisk($orderChargeback['refund_qty'], $transactionQty);
            $result["refund1_percent"][]        = $this->divisionRisk($orderChargeback['refund_qty1'], $transactionQty);
            $result["dishonour_percent"][]      = $orderChargeback['3d_transaction_amount_usd'];
        }

        $labels = array_column($orderChargebacks, "date_stat_month", "");

        $optionsInit = [
            'colors' => ['rgb(0, 143, 251)', 'rgb(0, 227, 150)', 'rgb(254, 176, 25)', 'rgb(255, 69, 96)', 'rgb(119, 93, 208)', 'rgb(153,50,204)'],
            'chart'  => [
                'type'    => 'line',
                'height'  => 380,
                'toolbar' => [
                    'show' => false,
                ]
            ],
            'stroke' => [
                'width' => 3.5,
            ],
            'series' => [
                [
                    'name' => "当月拒付率/%",
                    'data' => $result["dishonour_qty_percent"] ?? []
                ],
                [
                    'name' => "还原拒付率/%",
                    'data' => $result["dishonour_qty1_percent"] ?? []
                ],
                [
                    'name' => "当月欺诈率/%",
                    'data' => $result["fraud_qty_percent"] ?? []
                ],
                [
                    'name' => "还原月欺诈率/%",
                    'data' => $result["fraud_qty1_percent"] ?? []
                ],
                [
                    'name' => "退款率/%",
                    'data' => $result["refund_percent"] ?? []
                ],
                [
                    'name' => "还原月退款率/%",
                    'data' => $result["refund1_percent"] ?? []
                ],
                [
                    'name' => "3D交易金额",
                    'data' => $result["dishonour_percent"] ?? []
                ],
            ],
            'labels' => $labels,
            'yaxis'  => [
                [
                    'seriesName' => "当月拒付率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "还原拒付率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "当月欺诈率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "还原月欺诈率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "退款率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "还原月退款率/%",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
                [
                    'seriesName' => "3D交易金额",
                    'labels'     => [
                        'show' => false,
                    ],
                ],
            ],
            'title'  => [
                'margin' => 0
            ]
        ];
        $this->options($optionsInit);
    }


    public function divisionRisk($qty, $transactionQty)
    {
        if (!$qty) {
            return 0;
        }

        if (!$transactionQty) {
            return 100;
        }

        return round($qty * 100 / $transactionQty,4);
    }

    public function buildRequestScript()
    {
        if (!$this->allowBuildRequest()) {
            return;
        }

        $fetching = implode(';', $this->requestScripts['fetching']);
        $fetched  = implode(';', $this->requestScripts['fetched']);

        return <<<JS
(function () {
    var loading;
    function request(data) {
        if (loading) {
            return;
        }
        loading = 1;

        data = $.extend({$this->formatRequestData()}, data || {});

        data.mouthContent = $("#mouth")[0].text.trim().slice(1).slice(0, -2);
        data.midContent = $("#rickMid")[0].text.trim();
        data.bidContent = $("#rickBid")[0].text.trim();
        data.urlContent = $("#rickUrl")[0].text.trim();
        data.mccContent = $("#rickMcc")[0].text.trim();
        data.countryContent = $("#rickCountry")[0].text.trim();
        data.supplierContent = $("#rickSupplier")[0].text.trim();
        data.channelContent = $("#rickChannel")[0].text.trim();
        data.ccsContent = $("#rickCcs")[0].text.trim();
        data.currContent = $("#rickCurr")[0].text.trim();


        {$fetching};

        $.ajax({
          url: '{$this->getRequestUrl()}',
          dataType: 'json',
          method: '{$this->method}',
          data: data,
          success: function (response) {
            loading = 0;
            {$fetched};
          },
          error: function (a, b, c) {
              loading = 0;
              Dcat.handleAjaxError(a, b, c)
          },
        });
    }
    //点击bid发送请求
    $(document).on("click", '.switch-bar-bid', function () {
        let text = $(this).text();
        $("#rickBid").text(text);
        request();
    });
    //点击账单标识发送请求
    $(document).on("click", '.switch-bar-channel', function () {
        let text = $(this).text();
        $("#rickChannel").text(text);
        request();
    });
    request();

    {$this->buildBindingScript()}
})();
JS;
    }

    /**
     * @return string
     */
    private function formatRequestData()
    {
        $data = [
            '_key' => $this->getUriKey(),
        ];

        return json_encode(
            array_merge($this->parameters(), $data)
        );
    }

    /**
     * @return string
     */
    private function buildBindingScript()
    {
        $script = '';

        foreach ($this->requestSelectors as $v) {
            $script .= <<<JS
$('{$v}').on('click', function () {
    request($(this).data())
});
JS;
        }

        return $script;
    }
}
