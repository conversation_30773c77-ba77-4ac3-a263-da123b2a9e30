<?php

namespace App\Admin\Actions\Grid\Merchant;

use Dcat\Admin\Widgets\Modal;
use Dcat\Admin\Grid\RowAction;

class CardBalance extends RowAction
{
    protected $title = '<a href="#"> 余额 </a>';

    public function render()
    {
        return Modal::make()
            ->xl()
            ->title('当前商户余额')
            ->body(CardBalanceShow::make()->payload(['merchant_id' => $this->row->merchant_id]))
            ->button($this->title);
    }
}
