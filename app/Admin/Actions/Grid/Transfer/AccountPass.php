<?php

namespace App\Admin\Actions\Grid\Transfer;

use App\Models\TransferAccount;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AccountPass extends RowAction
{
    /**
     * @return string
     */
    protected $title = "&nbsp<button class='btn btn-info'>审 核</button>";

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        $account = TransferAccount::find($this->getKey());

        if (empty($account) || $account->status != TransferAccount::STATUS_EXAMINE) {
            return $this->response()->error('非法操作')->refresh();
        }

        // 更新
        $account->status = TransferAccount::STATUS_ADOPT;

        DB::beginTransaction();

        try {
            $account->save();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response()->error('审核操作失败')->refresh();
        }

        DB::commit();

        return $this->response()->success('审核操作成功')->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['您确认审核成功吗?'];
    }
}
