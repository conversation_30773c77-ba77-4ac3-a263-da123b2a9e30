<?php

namespace App\Admin\Actions\Grid\Dispute\ChargebackCases;

use App\Models\OrderRelation;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use App\Models\Order as OrderModel;
use App\Models\Merchant;
use App\Admin\Repositories\Order;

class ApplyChargebackCase extends LazyRenderable
{
    public function grid(): Grid
    {
        admin_style('.modal-xl {max-width: 1520px;}');

        return Grid::make(new Order(['card:id,card_mask', 'chargebackCase:order_id', 'parentOrder:order_id,amount,currency', 'relation:order_id,is_chargeback']), function (Grid $grid) {
            if (count(request()->toArray()) <= 2) {
                request()->offsetSet('app-admin-actions-grid-dispute-chargebackcases-applychargebackcase_completed_at', date('Y-m-d'));
            }

            $grid->model()->doesntHave('chargebackCase');
            $grid->model()->select('order_id', 'order_number', 'type', 'amount', 'currency', 'status', 'result', 'remark', 'parent_order_id');
            $grid->model()->where('status', OrderModel::STATUS_APPROVED);
            $grid->model()->orderBy('completed_at', 'desc')->orderBy('order_id', 'desc');

            $grid->content('选项')->display(function () {
                return self::build($this->order_id);
            });

            $grid->column('order_id', '订单号');
            $grid->column('order_number', '商户订单号');
            $grid->column('type', '交易类型')->display(function ($value) {
                return OrderModel::$typesMap[$value] ?? '未知';
            });
            $grid->column('currency/amount', '支付金额/授权金额')->display(function () {
                return $this->amount . ' ' . $this->currency . '<br/>' . ($this->paymentOrder->amount ?? '') . ' ' . ($this->paymentOrder->currency ?? '');
            });
            $grid->column('status', '支付状态')->display(function ($value) {
                return OrderModel::$statusMap[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success', '2' => 'primary', '3' => 'primary', '4' => 'danger']);
            $grid->column('relation.is_chargeback', '是否拒付')->display(function ($value) {
                return OrderRelation::$is_chargeback[$value] ?? '未知';
            })->dot(['0' => 'danger', '1' => 'success'], 'danger');
            $grid->column('result', '返回结果');
            $grid->column('remark', '备注');
            $grid->column('parent_order_id', '原始订单号');

            $grid->enableDialogCreate();
            $grid->disableRefreshButton();
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->showFilter();
            $grid->showFilterButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('card.card_mask', '卡号')->width(4);
                $filter->equal('order_id', '订单号')->width(4);
                $filter->equal('order_number', '商户订单号')->width(4);
                $filter->equal('merchant_id', 'MID')->select(
                    Merchant::where('status', Merchant::STATUS_ENABLE)
                        ->pluck('merchant_name', 'merchant_id')
                        ->map(static function ($item, $key) {
                            return $item . ':' . $key;
                        })
                        ->toArray()
                )->width(4);
				$filter->where('completed_at', function ($query) {
					$startTime = date('Y-m-d 00:00:00', strtotime($this->input));
					$endTime   = date('Y-m-d 23:59:59', strtotime($this->input));
					$query->whereRaw("completed_at >= '{$startTime}' AND completed_at <= '{$endTime}'");
				}, '交易日期')->date()->width(4);
            });
        });
    }

    /**
     * show form button by dialog
     *
     * @param [int] $orderId
     * @return string
     */
    static function build($orderId)
    {
        Form::dialog('<b>拒付预警</b>')
            ->click('.chargeback-case-query-forms')
            ->width('70%')
            ->height('95%')
            ->success('Dcat.reload()');
        $url = '/admin/dcat-api/render?_current_=/admin/alert_chargeback?&order_id=' . $orderId . '&renderable=App_Admin_Actions_Grid_Dispute_ChargebackCases_ChargebackCase';
        return "<span class='btn btn-info chargeback-case-query-forms' data-url='{$url}'>拒付预警</span>";
    }
}
