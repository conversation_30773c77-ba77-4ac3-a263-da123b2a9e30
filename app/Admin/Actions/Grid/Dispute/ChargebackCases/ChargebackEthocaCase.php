<?php

namespace App\Admin\Actions\Grid\Dispute\ChargebackCases;

use App\Classes\Pay\Contracts\Support;
use App\Classes\Supports\Traits\HasHttpRequest;
use App\Http\Controllers\Traits\PaymentController;
use App\Jobs\SendSlsLog;
use App\Models\ChargebackCase as ChargebackCaseModel;
use App\Models\DirectoryChargebackCode;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderRelation;
use App\Models\PaymentRefund;
use App\Models\Refund;
use App\Services\TransactionService;
use Dcat\Admin\Admin;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Validation\Rule;

class ChargebackEthocaCase extends Form implements LazyRenderable
{
    use LazyWidget;
    use PaymentController;
    use HasHttpRequest;

    public function handle(array $input)
    {
        $orderId = $this->payload['order_id'];

        // 数据简单验证
        if (empty($orderId)) {
            return $this->response()->error('非法请求!');
        }

        if (
            empty($input['case_id']) || !isset($input['alert_type'])
            || empty($input['reason_code']) || !isset($input['chargeback_from']) || !isset($input['alert_from'])
        ) {
            return $this->response()->error('参数错误, 请仔细核对再提交!');
        }

        $order        = Order::with(['settlements', 'paymentOrder', 'refund', 'relation'])->find($orderId);
        $paymentOrder = $order->paymentOrder;

        if (!$order || empty($order->relation) || !$paymentOrder) {
            return $this->response()->error('订单数据缺失, 请重试！');
        }

        // 判断是否重复预警
        if ($order->relation->is_chargeback_warn) {
            return $this->response()->error('该订单为已拒付预警订单, 请重试！');
        }

        // 预警数据初始化
        $addData = [
            'case_id'             => $input['case_id'],
            'business_history_id' => $order->settlements->business_history_id,
            'order_id'            => $order->order_id,
            'order_number'        => $order->order_number,
            'merchant_id'         => $order->merchant_id,
            'merchant_name'       => $order->merchant_name,
            'business_id'         => $order->business_id,
            'channel_id'          => $order->channel_id,
            'channel'             => $order->channel,
            'alert_type'          => $input['alert_type'],
            'reason_code'         => $input['reason_code'],
            'result'              => '0',
            'dishonour_warn_info' => '',
            'remarks'             => '',
            'warn_currency'       => $paymentOrder->currency,
            'warn_amount'         => $paymentOrder->amount,
            'is_meddle'           => '0',
            'by_alert_id'         => (Admin::user())->id,
            'by_alert'            => (Admin::user())->username,
            'chargeback_from'     => $input['chargeback_from'],
            'alert_from'          => $input['alert_from'],
            'date_complete'       => now(),
            'date_settle'         => '1970-01-01',
            'mod_times'           => 0,
            'is_manual'           => 1, // 通过拒付预警申请添加的，默认为人工添加
        ];

        $availableAmount = 0.00;

        // Ethoca 原始交易是否退款、拒付
        if ($order->relation->is_refund == OrderRelation::IS_REFUND_FULL || $order->relation->is_chargeback) {
            $addData['result']  = 1;
            $addData['remarks'] = '已退款或已拒付';
        } else {
            $addData['result'] = 1;

            // 获取可退款最高金额
            $availableAmount = TransactionService::getAvailableRefundAmount($order);

            // 添加退款任务
            if ($availableAmount <= 0.00) {
                $addData['remarks'] = '可退款金额为0, 请核对！';
            }
        }

        if ($order->type == Order::TYPES_AUTH) {
            $addData['remarks'] = 'Auth交易预警';
        }

        // 拒付预警收费
        $addData['date_settle'] = get_settle_date();
        $result                 = '';

        // 保存拒付预警数据
        $chargebackCase = ChargebackCaseModel::create($addData);
        if ($chargebackCase) {
            // 更新关联表拒付预警字段
            $order->relation->update(['is_chargeback_warn' => 1]);

            $result = $this->_crerateRefundSuccessData($order, $availableAmount);

            if ($result) {
                ChargebackCaseModel::where('case_id', $input['case_id'])->update(['remarks' => $result]);
            }
        }

        return $this->response()->success('操作成功! ' . $result)->redirect('/alert_chargeback');
    }

    public function form()
    {
        $this->disableSubmitButton();
        $this->disableResetButton();

        $orderId            = $this->payload['order_id'];
        $orderResult        = Order::with(['paymentOrder', 'card'])->find($orderId);
        $paymentOrderResult = $orderResult->paymentOrder;

        if (!$orderResult || empty($orderResult->card) || !$paymentOrderResult) {
            return ['error' => true, 'msg' => '该订单号的订单数据缺失'];
        }

        $chargebackCodeList = DirectoryChargebackCode::select(['code'])->where('cc_type', $orderResult->card->cc_type)->get()->pluck('code', 'code')->toArray();

        $this->select('alert_from', '预警来源')->default(0)->options(ChargebackCaseModel::$alertFromMap)->required();
        $this->select('chargeback_from', '预警服务')->default(0)->options([ChargebackCaseModel::FROM_ETHOCA => 'Ethoca'])->default(ChargebackCaseModel::FROM_ETHOCA)->required();
        $this->text('case_id', 'case_id')->default('1' . substr(microsecond(), -8))->required()->creationRules([Rule::unique('chargeback_cases')]);
        $this->select('alert_type', '类型')->options(ChargebackCaseModel::$typeMap)->default(0)->required();
        $this->select('reason_code', '原因码')->options($chargebackCodeList)->required();

        $this->fieldset('原始订单信息', function (Form $form) use ($orderResult, $paymentOrderResult) {
            $form->text('order_id', '订单号')->default($orderResult->order_id)->readOnly();
            $form->text('order_number', '商户订单号')->default($orderResult->order_number)->readOnly();
            $form->text('merchant_id', 'MID')->default($orderResult->merchant_id)->readOnly();
            $form->text('merchant_name', '商户名')->default($orderResult->merchant_name)->readOnly();
            $form->text('business_id', 'BID')->default($orderResult->business_id)->readOnly();
            $form->text('channel', '账单标识')->default($orderResult->channel)->readOnly();
            $form->text('url_name', '交易网站')->default($orderResult->url_name)->readOnly();
            $form->text('type', '交易类型')->default(Order::$typesMap[$orderResult->type])->readOnly();
            $form->text('status', '交易状态')->default(Order::$statusMap[$orderResult->status])->readOnly();
            $form->text('card_mask', '卡号')->default($orderResult->card->card_mask)->readOnly();
            $form->text('cc_type', '卡类型')->default($orderResult->card->cc_type)->readOnly();
            $form->text('currency', '授权币种')->default($paymentOrderResult->currency)->readOnly();
            $form->text('amount', '授权金额')->default($paymentOrderResult->amount)->readOnly();
            $form->text('date_complete', '完成时间')->default($orderResult->completed_at)->readOnly();
        });
    }

    protected function _crerateRefundSuccessData($order, $availableAmount)
    {
        $refund = new Refund();
        $refund->fill([
            'refund_id'    => Order::findAvailableNo(),
            'order_id'     => $order->order_id,
            'currency'     => $order->currency,
            'amount'       => $availableAmount,
            'status'       => Refund::STATUS_RECEIVED,
            'code'         => '',
            'result'       => '',
            'remark'       => '',
            'completed_at' => now(),
        ]);

        $refund->save();

        // 计算提交渠道退款金额
        if ($refund->amount == $order->amount) { // 全额退款
            $refundAmount = $order->paymentOrder->amount;
        } else { // 部分退款
            if ($order->currency == $order->paymentOrder->currency) {
                if ($refund->amount == $availableAmount) { // 同币种 剩余金额全额退款
                    // 已退款渠道交易金额
                    $refundIds                  = $order->refund->pluck('refund_id', 'refund_id');
                    $channelRefundedArr         = PaymentRefund::whereIn('refund_id', $refundIds)->get()->pluck('amount', 'id')->toArray();
                    $channelRefundedTotalAmount = array_sum($channelRefundedArr);
                    $refundAmount               = $order->paymentOrder->amount - $channelRefundedTotalAmount;
                } else { // 同币种 部分退款
                    $refundAmount = $refund->amount;
                }
            } else { // 不同币种
                if ($refund->amount == $availableAmount) { // 不同币种 剩余金额全额退款
                    // 已退款渠道交易金额
                    $refundIds                  = $order->refund->pluck('refund_id', 'refund_id');
                    $channelRefundedArr         = PaymentRefund::whereIn('refund_id', $refundIds)->get()->pluck('amount', 'id')->toArray();
                    $channelRefundedTotalAmount = array_sum($channelRefundedArr);
                    $refundAmount               = $order->paymentOrder->amount - $channelRefundedTotalAmount;
                } else { // 不同币种 部分退款
                    $directoryCurrencyArr = DirectoryCurrency::whereIn('code', [$order->currency, $order->paymentOrder->currency])->get()->pluck('rate', 'code');
                    $refundAmount         = Support::amount_format($refund->amount * ($directoryCurrencyArr[$order->paymentOrder->currency] / $directoryCurrencyArr[$order->currency]));
                }
            }
        }

        // 根据币种对金额进行向上取整
        $refundAmount = $this->_roundUpAmountByCurrency($order->paymentOrder->currency, $refundAmount);

        // 保存网关订单信息
        $refund->paymentRefund()->create([
            'order_number' => $refund->refund_id,
            'currency'     => $order->paymentOrder->currency,
            'amount'       => $refundAmount,
        ]);

        // 组装成功退款状态
        $refundResult['refund']         = [
            'status' => Refund::STATUS_APPROVED,
            'code'   => get_system_code('000'),
            'result' => 'Transaction is approved',
            'remark' => '交易成功',
        ];
        $refundResult['payment_refund'] = [
            'payment_refund_id' => '0',
            'code'              => '0000',
            'result'            => 'Transaction is approved',
            'remark'            => '',
            'status'            => PaymentRefund::STATUS_APPROVED
        ];

        $res    = $this->updateRefund($refund, $refundResult);
        $result = '';

        if (!empty($res)) {
            if (isset($res['status']) && $res['status'] == 1) {
                $result = '创建退款成功数据成功！';
            }
        }

        return $result;
    }
}
