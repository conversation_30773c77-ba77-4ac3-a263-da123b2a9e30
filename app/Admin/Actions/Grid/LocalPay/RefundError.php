<?php

namespace App\Admin\Actions\Grid\LocalPay;

use App\Classes\Supports\Collection;
use App\Http\Controllers\Traits\LocalPaymentController;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Widgets\Form;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;
use Dcat\Admin\Traits\LazyWidget;
use App\Models\LocalRefund as RefundErrorModel;
use App\Classes\Pay\Contracts\Support;
use App\Models\LocalOrder;
use App\Services\LocalTransactionService;

class RefundError extends Form implements LazyRenderable
{
    use LazyWidget;
    use LocalPaymentController;

    protected $statusList = [
        RefundErrorModel::STATUS_DECLINED => 'Declined',
        RefundErrorModel::STATUS_REVIEW   => 'Review',
    ];

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return Response
     */
    public function handle(array $input)
    {
        // 获取订单信息
        $refundError = RefundErrorModel::find($this->payload['refund_id']);

        if (empty($refundError)) {
            return $this->response()->error('获取订单信息失败!');
        }

        if (!in_array($refundError->status, [RefundErrorModel::STATUS_PENDING, RefundErrorModel::STATUS_DECLINED, RefundErrorModel::STATUS_RECEIVED])) {
            return $this->response()->error('订单信息原始状态错误!');
        }

        if (!isset($this->statusList[$input['status']])) {
            return $this->response()->error('订单更新状态错误!');
        }

        // 判断是否可退款
        $originOrder           = LocalOrder::with(['refund'])->find($refundError->order_id);
        $availableRefundAmount = LocalTransactionService::getAvailableRefundAmount($originOrder);

        if (($refundError->status == RefundErrorModel::STATUS_PENDING && Support::amount_format($availableRefundAmount) < 0)
            || ($refundError->status == RefundErrorModel::STATUS_DECLINED && Support::amount_format($refundError->amount) > Support::amount_format($availableRefundAmount))) {
            return $this->response()->error('超出退款金额范围!');
        }

        switch ($input['status']) {
            case RefundErrorModel::STATUS_DECLINED:
                $code   = get_system_code('054');
                $result = 'Transaction is declined';
                break;
            case RefundErrorModel::STATUS_REVIEW:
                $code   = get_system_code('000');
                $result = 'Transaction is approved';
                break;
            default:
                return $this->response()->error('更新状态失败');
        }

        $refundResult = [
            'status' => $input['status'],
            'code'   => $code,
            'result' => '(*) ' . $result,
            'remark' => '交易差错',
        ];
        $paymentRefundResult     = [
            'payment_refund_id' => $input['payment_refund_id'],
            'code'              => $code,
            'result'            => '(*) ' . $result,
            'remark'            => '',
            'status'            => $input['status']
        ];
        $collection = new Collection();
        $collection->set('refund', $refundResult);
        $collection->set('payment_refund', $paymentRefundResult);
        $result = $this->updateRefund($refundError, $collection, false);

        if (!empty($result)) {
            if (isset($result['status']) && $result['status'] == $input['status']) {
                return $this->response()->success('更新成功')->refresh();
            } else {
                return $this->response()->error('更新状态失败');
            }
        } else {
            return $this->response()->error('更新失败,返回为空', null, '404');
        }
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        // 获取交易信息
        $refundError = RefundErrorModel::with(['paymentRefund'])->find($this->payload['refund_id']);
        // 卡种
        $this->text('payment_refund_id', '退款渠道订单号')->default($refundError->paymentRefund->payment_refund_id)->required();
        $this->select('status', '交易状态')->options($this->statusList)->rules(['required', Rule::in(RefundErrorModel::$status)]);
    }
}
