<?php

namespace App\Admin\Actions\Grid\User;

use Dcat\Admin\Models\Administrator as AdministratorModel;
use Dcat\Admin\Grid\RowAction;

class ResetAccount extends RowAction
{
    protected $title = '<span>重置绑定</span>';

    public function handle()
    {
        $userRow = AdministratorModel::find($this->getKey());

        if (!$userRow) {
            return $this->response()->error('找不到该管理员信息，请核对！')->refresh();
        }

        $updateRet = AdministratorModel::where('id', $userRow->id)->update(['google2fa_secret' => Null]);

        if (empty($updateRet)) {
            return $this->response()->error('重置绑定失败')->refresh();
        }

        return $this->response()->success('重置绑定成功')->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['您确认要重置该账号的谷歌认证绑定吗?'];
    }
}
