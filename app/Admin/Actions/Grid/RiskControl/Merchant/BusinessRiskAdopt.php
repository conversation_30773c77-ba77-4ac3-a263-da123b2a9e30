<?php

namespace App\Admin\Actions\Grid\RiskControl\Merchant;

use App\Models\MerchantBusiness as MerchantBusinessModel;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\RowAction;

class BusinessRiskAdopt extends RowAction
{
	protected $id;
	protected $updatedAt;
	protected $status;
	protected $type;

	public function __construct($id = 0, $updatedAt = '', $type = '', $status = '')
	{
		$this->id 		 = $id;
		$this->updatedAt = $updatedAt;
		$this->type 	 = $type;
		$this->status 	 = $status;
		Admin::css('/css/element/businessRiskSwitchCss.css');
	}

	public function title()
	{
		return $this->status == 'on' ?
			"<span class='switchery switchery-small' style='background-color: rgb(88, 108, 177); border-color: rgb(88, 108, 177); box-shadow: rgb(88, 108, 177) 0px 0px 0px 11px inset; transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s, background-color 1.2s ease 0s;'>
<small style='left: 13px; background-color: rgb(255, 255, 255); transition: background-color 0.4s ease 0s, left 0.2s ease 0s;'></small></span>"
			: "<span class='switchery switchery-small' style='box-shadow: rgb(223, 223, 223) 0px 0px 0px 0px inset; border-color: rgb(223, 223, 223); background-color: rgb(255, 255, 255); transition: border 0.4s ease 0s, box-shadow 0.4s ease 0s;'>
<small style='left: 0px; transition: background-color 0.4s ease 0s, left 0.2s ease 0s;'></small></span>";

	}

	public function handle()
	{
		$businessId = request()->input('id');
		$updatedAt  = request()->input('updated_at');
		$type 		= request()->input('type');
		$status 	= request()->input('status');

		if (empty($businessId) || empty($updatedAt) || empty($type) || empty($status)) {
			return $this->response()->error('修改失败,请检查重试')->refresh();
		}

		$updateStatus = $status == 'on' ? MerchantBusinessModel::SWITCH_OFF : MerchantBusinessModel::SWITCH_ON;
		$updateRet    = MerchantBusinessModel::where('business_id', $businessId)->update([$type => $updateStatus, 'updated_at' => $updatedAt]);

		if (!$updateRet) {
			return $this->response()->error('修改失败')->refresh();
		}
		return $this->response()->success('修改成功')->refresh();
	}

	public function confirm()
	{
		$switch = $this->status == 'on' ? '关闭' : '开启';
		switch ($this->type) {
			case 'send_mail':
				$msg = '是否发送邮件按钮';
				break;
			case 'is_group_b_pay':
				$msg = '非白名单卡号能否在B组账单交易';
				break;
			case 'open_blacklist':
				$msg = '是否黑名单拦截';
				break;
			case 'is_not_delivery':
				$msg = '是否开启无运单结算';
				break;
		}
		return ["$switch $this->id <br> $msg ?"];
	}

	public function parameters()
	{
		return ['id' => $this->id, 'updated_at' => $this->updatedAt, 'type' => $this->type, 'status' => $this->status];
	}
}
