<?php

namespace App\Admin\Actions\Grid\RiskControl\Merchant;

use App\Models\MerchantBusiness;
use App\Models\MerchantKyc;
use App\Models\MerchantUrl;
use App\Models\RiskCase;
use App\Models\RiskCreditAssessment;
use App\Services\RiskCasesService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Database\Query\Builder;

class CreditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $merchantId = $this->payload['merchant_id'];
        $businessId = $input['bid'];

        // 排除掉还有待审核/审核中case 的bid
        $businessId = MerchantBusiness::query()
                                      ->where('merchant_id', $merchantId)
                                      ->where('business_id', $businessId)
                                      ->whereNotIn('business_id', function (Builder $query) use ($merchantId) {
                                          $query->select('risk_cases_bid.business_id')
                                                ->from('risk_cases')
                                                ->join('risk_cases_bid', 'risk_cases.id', '=', 'risk_cases_bid.risk_case_id')
                                                ->where('merchant_id', $merchantId)
                                                ->where('case_status', '<>', RiskCase::CASE_STATUS_COMPLETE)
                                                ->where('case_type', RiskCase::CASE_TYPE_CRA);
                                      })
                                      ->pluck('business_id')
                                      ->toArray();

        if (!$businessId){
            return $this->response()->error(admin_trans('credit-assessment.labels.bid_fail'));
        }

        $urlList = MerchantUrl::query()->with(['mcc'])
                              ->where('merchant_id', $merchantId)
                              ->whereIn('business_id', $businessId)
                              ->get();

        $urlList = $urlList->groupBy('business_id')->all();

        $kyc = MerchantKyc::query()
                          ->where('merchant_id', $merchantId)
                          ->where('audit_result', MerchantKyc::STATUS_UP)
                          ->orderBy('created_at')
                          ->first();

        foreach ($businessId as $bid) {
            $urlList    = $urlList[$bid] ?? [];
            $mccTable   = [];
            $ndxDetails = RiskCreditAssessment::NDX_DETAIL_REQUIRED_TEMPLATE_HEADER;
            $mccIds     = [];
            foreach ($urlList as $url) {
                $mccTable[]   = [
                    'id'                 => $url->d_mcc_id,
                    'mcc'                => $url->mcc->mcc ?? '',
                    'mcc_description'    => $url->mcc->description ?? '',
                    'credit_risk_rating' => $url->mcc->overall_risk_rating ?? '',
                    'delivery_days'      => null,
                    'percentage_of_apv'  => null,
                    'merchant_website'   => $url->url_name,
                ];
                $ndxDetails[] = [
                    'title'  => $url->mcc->mcc ?? '',
                    'days'   => null,
                    'rate'   => null,
                    'amount' => null,
                ];

                if ($url->mcc) {
                    $mccIds[] = $url->d_mcc_id;
                }
            }

            $ndxDetails = array_merge($ndxDetails, RiskCreditAssessment::NDX_DETAIL_REQUIRED_TEMPLATE_FOOTER);

            // 对mccId去重
            $mccIds = array_unique($mccIds);

            // 创建对应的风险案列和风险信用评级报告记录
            $caseService = new RiskCasesService();
            $case        = $caseService->addCases(['merchant_id'   => $merchantId,
                                                   'merchant_name' => $kyc->company_name,
                                                   'case_type'     => RiskCase::CASE_TYPE_CRA,
                                                   'country'       => $kyc->type ?? -1], $bid);

            $creditAssessment = new RiskCreditAssessment();
            $creditAssessment->case()->associate($case);
            $creditAssessment->mcc_table        = json_encode($mccTable);
            $creditAssessment->ndx_details      = json_encode($ndxDetails);
            $creditAssessment->company_name     = $kyc->company_name;
            $creditAssessment->merchant_id      = $merchantId;
            $creditAssessment->mcc_id           = implode(',', $mccIds);
            $creditAssessment->reserve_currency = 'USD';
            $creditAssessment->save();
        }
        return $this->response()->success(admin_trans('credit-assessment.labels.create_success'))->refresh();
    }

    public function form()
    {
        $bids = MerchantBusiness::query()
                                ->where('merchant_id', $this->payload['merchant_id'])
                                ->where('internal_status', '!=', MerchantBusiness::INTERNAL_STATUS_DISABLE)
                                ->pluck('business_id', 'business_id')->toArray();
        $this->multipleSelect('bid')->width(8, 3)->options($bids)->required();
        $this->confirm(admin_trans_field('confirm.title'));
    }
}
