<?php

namespace App\Admin\Actions\Grid\CardVirtual;

use App\Classes\Supports\Log;
use App\Jobs\SendSlsLog;
use App\Models\CardBin;
use App\Models\CardHolder;
use App\Models\CardHolderChannelReports;
use App\Services\Virtual\VirtualServiceFacade;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use DES3;

class CardHolderAudit extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input)
    {
        $id       = $this->payload['id'] ?? '';
        $remark   = $input['audit_remark'] ?? '';
        $status   = $input['audit_status'] ?? '';
        $userId   = Admin::user()->id;
        $userName = Admin::user()->username;

        if (empty($id)) {
            return $this->response()->error('参数错误');
        }

        $cardHolderData = CardHolder::with('reports')->find($id);
        if (empty($cardHolderData)) {
            return $this->response()->error('持卡人信息数据不存在');
        }

        if ($cardHolderData->audit_status != CardHolder::CARD_HOLDER_AUDIT_STATUS_PENDING) {
            return $this->response()->error('该持卡人信息已审核');
        }

        $cardHolderData->audit_status    = $status;
        $cardHolderData->audit_remark    = $remark;
        $cardHolderData->audit_user_id   = $userId;
        $cardHolderData->audit_user_name = $userName;

        if ($status == CardHolder::CARD_HOLDER_AUDIT_STATUS_FAIL) {
            $cardHolderData->save();
            return $this->response()->success('审核成功')->refresh();
        }

        $cardBinInfo = CardBin::selectRaw('bin_supplier_id, MAX(id) as id, ANY_VALUE(config) as config')
            ->where('status', '=', CardBin::CARD_BIN_STATUS_ENABLE)  // 确保查询条件匹配
            ->with('CardBinSupplier')
            ->groupBy('bin_supplier_id')
            ->get();

        foreach ($cardBinInfo as $cardBin) {
            try {
                $result = VirtualServiceFacade::getService($cardBin->CardBinSupplier->file_name)->setGatewayConfig($cardBin->config)->createHolder($cardHolderData->toArray());
                if ($result['code'] == '000') {
                    continue;
                }

                if (!isset($result['code']) || $result['code'] != '200') {
                    return $this->response()->error($result['message']);
                }

                // 持卡人渠道报备记录
                CardHolderChannelReports::insert([
                    'supplier_id'        => $cardBin->CardBinSupplier->id,
                    'supplier_name'      => $cardBin->CardBinSupplier->supplier_name,
                    'supplier_type'      => $result['data']['supplier_type'],
                    'payer_id'           => $result['data']['payer_id'] ?? '',
                    'cardholder_id'      => $cardHolderData->id ?? '',
                    'report_supplier_id' => $result['data']['report_supplier_id'] ?? '',
                    'report_result'      => $result['data']['report_result'],
                    'created_at'         => date_create(),
                    'updated_at'         => date_create()
                ]);
            } catch (\Exception $e) {
                Log::warning($e->getMessage());
                dispatch(new SendSlsLog(
                    ['message' => "请求渠道创建持卡人信息失败 bin_supplier_id: " . $cardBin->bin_supplier_id . ' ' . $e->getMessage()],
                    [],
                    'warning',
                ));
                continue;
            }
        }

        $cardHolderData->save();
        return $this->response()->success('审核成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->disableResetButton();
        $url            = config('filesystems.disks.merchant.url');
        $cardHolderData = CardHolder::find($this->payload['id']);
        $this->confirm('您确定要进行审核？');
        $this->text('merchant_id', 'MID')->value($cardHolderData->merchant_id)->readonly();
        $this->text('full_name', '姓名')->value($cardHolderData->full_name)->readonly();
        $this->text('calling_prefix', '手机号码前缀')->value($cardHolderData->calling_prefix)->readonly();
        $this->text('phone', '手机号码')->value($cardHolderData->phone)->readonly();
        $this->text('email', '邮箱')->value($cardHolderData->email)->readonly();
        $this->text('country', '国家地区')->value($cardHolderData->country)->readonly();
        $this->text('city', '城市')->value($cardHolderData->city)->readonly();
        $this->text('province', '省份')->value($cardHolderData->province)->readonly();
        $this->text('address', '详细地址')->value($cardHolderData->address)->readonly();
        $this->text('id_type', '证件类型')->value(CardHolder::$idTypeMap[$cardHolderData->id_type])->readonly();
        $this->text('id_number', '证件号码')->value(DES3::decrypt($cardHolderData->id_number))->readonly();
        $this->text('birth_date', '出生日期')->value(date('Y-m-d', strtotime($cardHolderData->birth_date)))->readonly();
        $this->text('nationality', '国籍')->value($cardHolderData->nationality)->readonly();
        $this->image('photo_front', '证件正面照片')->default($url . '/' . $cardHolderData->photo_front)->disable();
        $this->image('photo_back', '证件反面照片')->default($url . '/' . $cardHolderData->photo_back)->disable();
        $this->text('company_position', '公司职位')->value(CardHolder::$companyPositionMap[$cardHolderData->company_position])->readonly();
        $this->text('subject_type', '主体类型')->value(CardHolder::$subjectTypeMap[$cardHolderData->subject_type])->readonly();
        $this->text('holder_type', '持卡人身份')->value(CardHolder::$holderTypeMap[$cardHolderData->holder_type])->readonly();
        $this->select('audit_status', '审核状态')->options([
            CardHolder::CARD_HOLDER_AUDIT_STATUS_PASS => '审核通过',
            CardHolder::CARD_HOLDER_AUDIT_STATUS_FAIL => '审核不通过'
        ])->required();
        $this->text('audit_remark', '审核备注');
    }
}