<?php

namespace App\Admin\Actions\Grid\Api;

use App\Classes\Supports\Traits\HasHttpRequest;
use App\Models\MerchantApiNoticeTask;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use DES3;
use App\Jobs\SendSlsLog;

class ApiTask extends RowAction
{
    use HasHttpRequest;

    /**
     * @return string
     */
    protected $title = "<sapn class='btn btn-danger'>通 知</span>";

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle()
    {
        $taskId = $this->getKey();
        // 获取待通知
        $apiNoticeTask = MerchantApiNoticeTask::find($taskId);
        $noticeData    = json_decode($apiNoticeTask->content, true);

        dispatch(new SendSlsLog(
            ['message' => 'apiNoticeTask callback request'],
            ['notify_url' => $apiNoticeTask->notify_url, 'notice_data' => $noticeData],
            'info',
            'api_notice_task',
        ));
        if (isset($noticeData['result'])) {
            // 解密数据
            $noticeData['result'] = json_decode(DES3::decrypt($noticeData['result']), true);
        }

        $msg = '已通知';
        try {
            $headers = [
                'headers' => [
                    'Message-Type' => MerchantApiNoticeTask::$internalMessageTypeEnMap[$apiNoticeTask->message_type],
                ]
            ];
            $result = trim($this->post($apiNoticeTask->notify_url, $noticeData, $headers));
        } catch (\Exception $e) {
            $result = $e->getMessage();
        }

        dispatch(new SendSlsLog(
            ['message' => 'apiNoticeTask callback result'],
            ['result' => $result],
            'info',
            'api_notice_task',
        ));

        if ($result == 'ok') {
            $apiNoticeTask->status = '1'; // status=1 表示通知成功
            $msg = '通知成功';
        }

        $apiNoticeTask->result = $result;
        $apiNoticeTask->cnt++;
        $apiNoticeTask->save();

        return $this->response()->success($msg)->refresh();
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['您确认要发送异步通知?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
