<?php

namespace App\Admin\Actions\Tools;

use App\Classes\Supports\Str;
use App\Models\DownloadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class LocalRefundErrorExportTool extends AbstractTool
{
    protected $title = '<i class="fa fa-download"></i> 导出';
    protected $style = 'btn btn-primary grid-refresh btn-mini btn-outline pull-right';
    protected $param = [];
    protected $exportTitle = [];

    public function __construct($param = null, $exportTitle = null)
    {
        $this->param       = $param;
        $this->exportTitle = $exportTitle;

        parent::__construct($this->title);
    }

    public function handle(Request $request)
    {
        // 判断是否有筛选条件
        $tempInput = $request->input('inputs');
        unset($tempInput['_pjax']);
        if (empty($request->input('filter')) && empty($tempInput)) {
            return $this->response()->error('无筛选条件不允许导出');
        }

        $user             = Admin::user();
        $downloadFileName = 'refundError-' . Str::random(10);
        $center           = DownloadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $downloadFileName,
            'export_identity' => 'localRefundError',
            'query_criteria'  => $request->toArray(),
            'titles'          => $request->input('export_title')
        ]);

        if ($center) {
            return $this->response()->success('添加导出任务成功');
        }

        return $this->response()->error('添加导出任务失败');
    }

    public function confirm(): array
    {
        return ['', '您确定导出退款差错吗?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [
            'param'        => $this->param,
            'export_title' => $this->exportTitle,
            'filter'       => $this->parent->filter()->getConditions(),
            'inputs'       => $this->parent->filter()->inputs()
        ];
    }
}
