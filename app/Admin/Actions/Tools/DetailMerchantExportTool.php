<?php

namespace App\Admin\Actions\Tools;

use App\Classes\Supports\Str;
use App\Events\ExportTask;
use App\Models\DownloadCenter;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;

class DetailMerchantExportTool extends AbstractTool
{
    /**
     * @return string
     */
    protected $title = '<i class="feather icon-download"></i> 导出MID结算明细';

    protected $style = 'btn btn-primary pull-right';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        // 判断是否有筛选条件
        $tempInput = $request->input('inputs');
        unset($tempInput['_pjax']);
        if (empty($request->input('filter')) && empty($tempInput)) {
            return $this->response()->error('无筛选条件不允许导出');
        }

        $user          = Admin::user();
        $queryCriteria = $request->toArray();

        $downloadFileName = 'settleDetailMerchant-' . Str::random(10);
        $center           = DownloadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $downloadFileName,
            'export_identity' => 'settleDetailMerchant',
            'query_criteria'  => $queryCriteria,
        ]);

        if ($center) {
            return $this->response()->success('添加导出任务成功');
        }

        return $this->response()->error('添加导出任务失败');
    }

    /**
     * @return string|void
     */
    protected function href()
    {
        // return admin_url('auth/users');
    }

    /**
     * @return string|array|void
     */
    public function confirm()
    {
        return ['', '您确定导出MID结算明细吗?'];
    }

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [
            'filter' => $this->parent->filter()->getConditions(),
            'inputs' => $this->parent->filter()->inputs()
        ];
    }
}
