<?php

namespace App\Admin\Actions\Tools;


use App\Models\DownloadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;

class ChannelSuccessRateExport extends AbstractTool
{
	protected $title = '渠道成功率类表格导出';
	protected $style = 'btn btn-primary grid-refresh btn-mini btn-outline pull-right';
	protected $param = [];
	protected $exportTitle = [];

	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * @throws \Exception
	 */
	public function handle(Request $request): \Dcat\Admin\Actions\Response
	{
		// 判断是否有筛选条件
		$tempInput = $request->input('inputs');
		unset($tempInput['_pjax']);
		if (empty($request->input('filter')) && empty($tempInput)) {
			return $this->response()->error('无筛选条件不允许导出');
		}
		// 渠道必须进行选择 才能进行商户成功率的导出
		if (isset($tempInput['merchant_id.0'])) {
			return $this->response()->error('筛选条件与导出类型不一致');
		}

		foreach ($tempInput as $key => $value) {
			switch ($key) {
				case (strpos($key, 'channel_suppliers_id.') !== false):
					$tempInput['channel_supplier_id'][] = $value;
					unset($tempInput[$key]);
					break;
				case(strpos($key, 'cc_type.') !== false):
					$tempInput['cc_type'][] = $value;
					unset($tempInput[$key]);
					break;
			}
		}
		$tempInput['date_stat.start'] = $tempInput['date_stat.start'] ?? date('Y-m-d', strtotime('-1 months'));
		$tempInput['date_stat.end']   = $tempInput['date_stat.end'] ?? date('Y-m-d', strtotime('-1 days'));


		$user                    = Admin::user();
		$queryCriteria['filter'] = $request->input('filter');
		$queryCriteria['inputs'] = $tempInput;
		$downloadFileName        = env('APP_NAME', 'Laravel') . '渠道成功率' . date('mdH') . time();
		$center                  = DownloadCenter::create([
			'admin_user_id'   => $user['id'],
			'file_name'       => $downloadFileName,
			'export_identity' => 'successRate',
			'query_criteria'  => $queryCriteria,
			'titles'          => 'channel'
		]);

		if ($center) {
			return $this->response()->success('添加导出任务成功');
		}

		return $this->response()->error('添加导出任务失败');
	}

	public function confirm(): array
	{
		return ['', '您确定导出渠道成功率类表格吗?'];
	}

	public function parameters(): array
	{
		return [
			'filter' => $this->parent->filter()->getConditions(),
			'inputs' => $this->parent->filter()->inputs(),
		];
	}

}
