<?php

namespace App\Admin\Actions\Tools;

use App\Models\DownloadCenter;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;

class ChargebackPenaltyExportTool extends AbstractTool
{
    protected $title = '<i class="fa fa-download"></i> 导出';
    protected $style = 'btn btn-primary grid-refresh btn-mini btn-outline pull-right';
    protected $exportTitle = [];

    public function __construct($exportTitle = null)
    {
        $this->exportTitle = $exportTitle;

        parent::__construct($this->title);
    }

    public function handle(Request $request): \Dcat\Admin\Actions\Response
    {
        // 判断是否有筛选条件
        $tempInput = $request->input('inputs');
        unset($tempInput['_pjax']);
        if (empty($request->input('filter')) && empty($tempInput)) {
            return $this->response()->error('无筛选条件不允许导出');
        }

        $user                    = Admin::user();
        $queryCriteria['filter'] = $request->input('filter');
        $queryCriteria['inputs'] = $request->input('inputs');
        $fileName                = 'chargebackPenalty' . date('YmdHms') . random_int(100, 9999);
        $titles                  = $request->input('export_title');
        $center                  = DownloadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $fileName,
            'export_identity' => 'chargebackPenalty',
            'query_criteria'  => $queryCriteria,
            'titles'          => $titles,
        ]);

        if ($center) {
            return $this->response()->success('添加导出任务成功');
        }

        return $this->response()->error('添加导出任务失败');
    }

    public function confirm(): array
    {
        return ['', '您确定导出拒付罚金吗?'];
    }

    public function parameters(): array
    {
        return [
            'export_title' => $this->exportTitle,
            'filter'       => $this->parent->filter()->getConditions(),
            'inputs'       => $this->parent->filter()->inputs()
        ];
    }
}
