<?php

namespace App\Admin\Actions\Tools;

use App\Models\DownloadCenter;
use App\Services\TableService;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Illuminate\Http\Request;

class TransactionStatExportTool extends AbstractTool
{
    protected $title = '商户交易占比统计表格导出';
    protected $style = 'btn btn-primary grid-refresh btn-mini btn-outline pull-right';
    protected $exportTitle = [];

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(Request $request): \Dcat\Admin\Actions\Response
    {
        // 判断是否有筛选条件
        $tempInput = $request->input('inputs');
        unset($tempInput['_pjax']);
        if (empty($request->input('filter')) && empty($tempInput)) {
            return $this->response()->error('无筛选条件不允许导出');
        }

        // 多选条件处理
        foreach ($tempInput as $key => $value) {
            switch ($key) {
                case (strpos($key, 'merchant_id') !== false):
                    $tempInput['merchant_id'][] = $value;
                    unset($tempInput[$key]);
                    break;
            
                case (strpos($key, 'channel_suppliers') !== false):
                    $tempInput['channel_suppliers'][] = $value;
                    unset($tempInput[$key]);
                    break;
            }
        }
        
        if (empty($tempInput['merchant_id'])) {
            // 导出没有MID获取固定条件
            $system                   = env('APP_NAME', 'Laravel');
            $tempInput['merchant_id'] = TableService::$TransactionStatData[$system]['merchantTransactionStat']['merchant_id'] ?? [];
        }

        $tempInput['date_stat.start'] = $tempInput['date_stat.start']  ?? date('Y-m-d', strtotime('-1 months'));
        $tempInput['date_stat.end']   = $tempInput['date_stat.end']  ?? date('Y-m-d', strtotime('-1 day'));

        $user                    = Admin::user();
        $queryCriteria['filter'] = $request->input('filter');
        $queryCriteria['inputs'] = $tempInput;
        $fileName                = 'transaction-stat' . date('YmdHms') . random_int(100, 9999);
        $center                  = DownloadCenter::create([
            'admin_user_id'   => $user['id'],
            'file_name'       => $fileName,
            'export_identity' => 'transactionStat',
            'query_criteria'  => $queryCriteria,
            'titles'          => 'merchant',
        ]);

        if ($center) {
            return $this->response()->success('添加导出任务成功');
        }

        return $this->response()->error('添加导出任务失败');
    }

    public function confirm(): array
    {
        return ['', '您确定导出商户交易占比统计表格吗?'];
    }

    public function parameters(): array
    {
        return [
            'filter' => $this->parent->filter()->getConditions(),
            'inputs' => $this->parent->filter()->inputs()
        ];
    }
}
