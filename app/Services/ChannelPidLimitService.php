<?php
namespace App\Services;

use App\Models\Channel;
use App\Models\Order;
use App\Models\OrderSettlement;
use App\Models\PaymentOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class ChannelPidLimitService
{
    /**
     * 更新账单标识和PID限额缓存
     *
     * @param array $orderSettlementData 日\月交易数据
     * @param string $timezone
     * @param array $pidList PID数据
     * @return array
     */
    public static function updateCache(array $orderSettlementData,string $timezone, array $pidList = []): array
    {
        // 获取账单标识对应的PID数据
        if (!count($pidList)) {
            $pidList = Channel::with('channelPid:id,channel_pid')->selectRaw('channel_pid_id,channel')->get()->pluck('channelPid.channel_pid', 'channel');
        }

        $time            = Carbon::now($timezone);
        $typeData        = ['pid', 'channel'];
        $transactionData = [];
        foreach ($orderSettlementData as $dateKey => $dateData) {
            if ($dateKey == 'day') {
                $amountField = 'day_amount';
                $qtyField    = 'day_qty';
            } else {
                $amountField = 'month_amount';
                $qtyField    = 'month_qty';
            }
            
            if (empty($dateData)) {
                continue;
            }
            
            foreach ($dateData as $dateRow) {
                foreach ($typeData as $type) {
                    $keyName = $type == 'channel' ? $dateRow->channel : $pidList[$dateRow->channel];
                    // 金额汇总
                    if (!isset($transactionData[$type][$keyName][$amountField]['*'])) {
                        $transactionData[$type][$keyName][$amountField]['*'] = 0;
                    }
                    
                    if (!isset($transactionData[$type][$keyName][$amountField][$dateRow->cc_type])) {
                        $transactionData[$type][$keyName][$amountField][$dateRow->cc_type] = 0;
                    }
                    
                    $transactionData[$type][$keyName][$amountField]['*']               += $dateRow->amount ?? 0;
                    $transactionData[$type][$keyName][$amountField][$dateRow->cc_type] += $dateRow->amount ?? 0;
                    
                    if ($type == 'channel') {
                        // 笔数汇总
                        if (!isset($transactionData[$type][$keyName][$qtyField]['*'])) {
                            $transactionData[$type][$keyName][$qtyField]['*'] = 0;
                        }
                        
                        if (!isset($transactionData[$type][$keyName][$qtyField][$dateRow->cc_type])) {
                            $transactionData[$type][$keyName][$qtyField][$dateRow->cc_type] = 0;
                        }
    
                        $transactionData[$type][$keyName][$qtyField]['*']               += $dateRow->qty ?? 0;
                        $transactionData[$type][$keyName][$qtyField][$dateRow->cc_type] += $dateRow->qty ?? 0;
                    }
                      
                }
            }
        }
        
        $transactionChannelList = $transactionData['channel'] ?? [];
        $transactionPidList     = $transactionData['pid'] ?? [];

        // 账单标识统计数据缓存
        foreach ($typeData as $type) {
            $transactionData = $type == 'channel' ?  $transactionChannelList : $transactionPidList;
            if (empty($transactionData)) {
                continue;
            }
            
            foreach ($transactionData as $keyName => $transaction) {
                foreach ($transaction as $typeFile => &$amounts) {
                    if ($typeFile == 'day_amount' || $typeFile == 'month_amount') {
                        foreach ($amounts as $ccType => $amount) {
                            $amounts[$ccType] = amount_format($amount);
                        }
                    }
                }

                if ($type == 'channel') {
                    $cacheKey     = 'Transaction_' . MD5($keyName) . '_Qty_And_Amount_' . $time->format('Ym');
                    $cacheKeyLock = $cacheKey . '_Lock';
                    Cache::lock($cacheKeyLock, 10)->block(5, function () use ($cacheKey, $transaction) {
                        Cache::put($cacheKey, $transaction, 31 * 24 * 60 * 60);
                    });
                } else {
                    $cacheKey     = 'Transaction_' . $keyName . '_Amount_' . $time->format('Ym');
                    $cacheKeyLock = $cacheKey . '_Lock';
                    Cache::lock($cacheKeyLock, 10)->block(5, function () use ($cacheKey, $transaction) {
                        Cache::put($cacheKey, $transaction, 31 * 24 * 60 * 60);
                    });
                }
            }
        }
        
        return ['transactionChannelList' => $transactionChannelList, 'transactionPidList' => $transactionPidList];
    }

    /**
     * 获取账单标识的日月交易数据
     *
     * @param string $timezone
     * @param string $sysTimezone
     * @param array $tempChannelArr
     * @return array
     */
    public static function getLimitData(string $timezone,string $sysTimezone,array $tempChannelArr = []): array
    {
        //日交易数据
        $startTimeDay = Carbon::now($timezone)->startOfDay()->setTimezone($sysTimezone)->toDateTimeString();
        $endTimeDay   = Carbon::now($timezone)->endOfDay()->setTimezone($sysTimezone)->toDateTimeString();
        $dayQuery     = OrderSettlement::WhereBetween('created_at', [$startTimeDay, $endTimeDay])
            ->where('status', Order::STATUS_APPROVED)
            ->whereIn('type', [PaymentOrder::TYPES_SALE, PaymentOrder::TYPES_CAPTURE])
            ->selectRaw('channel, cc_type, count(order_id) as qty, ifnull(sum(payment_amount_usd),0.00) as amount')
            ->groupBy('channel', 'cc_type');

        if (!empty($tempChannelArr)) $dayQuery->whereIn('channel', $tempChannelArr);
        $dayData = $dayQuery->get();

        // 月交易数据
        $startTimeMonth = Carbon::now($timezone)->startOfMonth()->setTimezone($sysTimezone)->toDateTimeString();
        $endTimeMonth   = Carbon::now($timezone)->endOfMonth()->setTimezone($sysTimezone)->toDateTimeString();
        $monthQuery     = OrderSettlement::WhereBetween('created_at', [$startTimeMonth, $endTimeMonth])
            ->where('status', Order::STATUS_APPROVED)
            ->whereIn('type', [PaymentOrder::TYPES_SALE, PaymentOrder::TYPES_CAPTURE])
            ->selectRaw('channel, cc_type, count(order_id) as qty, ifnull(sum(payment_amount_usd),0.00) as amount')
            ->groupBy('channel', 'cc_type');

        if (!empty($tempChannelArr)) $monthQuery->whereIn('channel', $tempChannelArr);

        $monthData = $monthQuery->get();

        return ['day' => $dayData, 'month' => $monthData];
    }
}