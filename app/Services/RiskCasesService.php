<?php

namespace App\Services;

use App\Models\RiskCase;
use App\Models\MerchantBusiness;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class RiskCasesService
{
    public function addCases($data, $bid = null)
    {
        return DB::transaction(function () use ($data, $bid) {
            // 新建一个 case
            $case = RiskCase::create([
                'merchant_id'   => $data['merchant_id'],
                'merchant_name' => $data['merchant_name'],
                'case_number'   => $this->generate(),
                'case_type'     => $data['case_type'],
                'country'       => $data['country'],
            ]);

            // 处理 case 和 bid 之间的关联关系
            if (!empty($bid)) {
                $this->attachBidsToCase($case, $bid);
            }

            return $case;
        });
    }

    /**
     * 将 bid 关联到 case
     *
     * @param RiskCase $case
     * @param mixed $bid 可以是单个 bid 模型实例、多个 bid 模型的数组/集合，或者 business_id 字符串/数组
     */
    private function attachBidsToCase(RiskCase $case, $bid)
    {
        $businessIds = $this->extractBusinessIds($bid);

        if (!empty($businessIds)) {
            // 使用多对多关系的 attach 方法
            $case->bids()->attach($businessIds);
        }
    }

    /**
     * 从不同类型的 bid 参数中提取 business_id
     *
     * @param mixed $bid
     * @return array
     */
    private function extractBusinessIds($bid)
    {
        $businessIds = [];

        if (is_string($bid)) {
            // 如果是字符串，直接作为 business_id
            $businessIds[] = $bid;
        } elseif (is_array($bid) || $bid instanceof \Illuminate\Support\Collection) {
            // 如果是数组或集合
            foreach ($bid as $item) {
                if (is_string($item)) {
                    // 字符串类型的 business_id
                    $businessIds[] = $item;
                } elseif ($item instanceof MerchantBusiness) {
                    // MerchantBusiness 模型实例
                    $businessIds[] = $item->business_id;
                } elseif (is_object($item) && isset($item->business_id)) {
                    // 其他包含 business_id 属性的对象
                    $businessIds[] = $item->business_id;
                }
            }
        } elseif ($bid instanceof MerchantBusiness) {
            // 单个 MerchantBusiness 模型实例
            $businessIds[] = $bid->business_id;
        } elseif (is_object($bid) && isset($bid->business_id)) {
            // 其他包含 business_id 属性的对象
            $businessIds[] = $bid->business_id;
        }

        // 去重并过滤空值
        return array_unique(array_filter($businessIds));
    }

    public function generate(): string
    {
        // 格式化当天日期
        $date = Carbon::now()->format('Ymd');
        $key  = "case_seq:{$date}";

        // 如果没有缓存，则初始化为 0，并设置过期时间到明天零点
        if (!Cache::has($key)) {
            $expiresAt = Carbon::tomorrow();
            Cache::add($key, 0, $expiresAt);
        }

        $seq = Cache::increment($key);

        // 序号左侧补零
        $seqStr = str_pad((string) $seq, 7, '0', STR_PAD_LEFT);

        // 拼接前缀 + 日期 + 序号，16 位固定长度
        return 'C' . $date . $seqStr;
    }
}