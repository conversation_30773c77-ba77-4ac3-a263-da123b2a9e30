<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Models\Administrator;

class LocalChannelRouteSchemeHistory extends Model
{
    use HasDateTimeFormatter;
    protected $table = 'local_channel_route_scheme_history';

    protected $casts = [
        'extra_attributes' => 'array'
    ];

    protected $fillable = [
        'scheme_id', 'url_id', 'transaction_type', 'group_type', 'scheme_type', 'extra_attributes', 'operator_id'
    ];

    
	const GROUP_A  = 0;

	public static $groupMap = [
		self::GROUP_A  => 'A组',
	];

	const SCHEME_S2S            = 0;
	const SCHEME_DIRECT_CHANNEL = 1;
    
	public static $schemeMap = [
		self::SCHEME_S2S            => '两方',
		self::SCHEME_DIRECT_CHANNEL => '三方',
	];

	public function operator()
    {
        return $this->belongsTo(Administrator::class, 'operator_id', 'id');
    }
}
