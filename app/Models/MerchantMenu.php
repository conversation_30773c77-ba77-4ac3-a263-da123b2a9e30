<?php

namespace App\Models;

use Dcat\Admin\Models\MenuCache;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Dcat\Admin\Traits\ModelTree;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class MerchantMenu extends Model
{
    use HasDateTimeFormatter,
        MenuCache,
        ModelTree {
        allNodes as treeAllNodes;
        ModelTree::boot as treeBoot;
    }

    /**
     * @var array
     */
    protected $sortable = [
        'sort_when_creating' => true,
    ];

    protected $table = 'merchant_menus';

    protected $fillable = [
        'parent_id', 'order', 'title', 'icon', 'uri'
    ];

    /**
     * A Menu belongs to many roles.
     *
     * @return BelongsToMany
     */
    public function roles(): BelongsToMany
    {
        $pivotTable   = config('merchant.database.role_menu_table');
        $relatedModel = config('merchant.database.roles_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'menu_id', 'role_id');
    }

    public function permissions(): BelongsToMany
    {
        $pivotTable   = config('merchant.database.permission_menu_table');
        $relatedModel = config('merchant.database.permissions_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'menu_id', 'permission_id');
    }

    /**
     * Fetch all elements.
     *
     * @return object
     */
    public function fetchAll()
    {
        $all = $this->withQuery(function ($query) {
            if (static::withPermission()) {
                $query = $query->with('permissions');
            }

            return $query->with('roles');
        })->treeAllNodes();

        $user = auth()->user();
        //非系统后台
        if (!(empty($user) || $user->getTable() == 'admin_users')) {
            if ($user->merchant->status == Merchant::STATUS_CHECK) {
                return collect();
            }

            if (!$user->merchant->is_credit) {
                $all = $all->whereNotIn('title', ['BID管理', '运单管理', '网址管理', '争议管理', '交易管理', '提现管理', '固定保证金管理', '结算信息', '商户结算报表', '结算调整']);
            }

            if (!$user->merchant->is_virtual) {
                $all = $all->whereNotIn('title', ['CID管理', '虚拟卡管理', '虚拟卡交易管理']);
            }
        }

        return $all;
    }

    /**
     * Get all elements.
     *
     * @param bool $force
     *
     * @return object
     */
    public function allNodes(bool $force = false)
    {
        if ($force || $this->queryCallbacks) {
            return $this->fetchAll();
        }

        return $this->remember(function () {
            return $this->fetchAll();
        });
    }

    /**
     * Determine if enable menu bind permission.
     *
     * @return bool
     */
    public static function withPermission()
    {
        return config('merchant.menu.bind_permission') && config('merchant.permission.enable');
    }

    /**
     * Determine if enable menu bind role.
     *
     * @return bool
     */
    public static function withRole()
    {
        return (bool)config('merchant.permission.enable');
    }

    /**
     * Detach models from the relationship.
     *
     * @return void
     */
    protected static function boot()
    {
        static::treeBoot();

        static::deleting(function ($model) {
            $model->roles()->detach();
            $model->permissions()->detach();

            $model->flushCache();
        });

        static::saved(function ($model) {
            $model->flushCache();
        });
    }
}
