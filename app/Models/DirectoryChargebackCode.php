<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class DirectoryChargebackCode extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'directory_chargeback_codes';

    const TYPE_FRAUD                         = 0;
    const TYPE_PRODUCT_NOT_RECEIVED          = 1;
    const TYPE_PRODUCT_NOT_AS_DESCRIBED      = 2;
    const TYPE_REFUND_NOT_RECEIVED           = 3;
    const TYPE_PROCESSING_ERRORS             = 4;
    const TYPE_TRANSACTION_NOT_RECOGNISE     = 5;
    const TYPE_CANCELLED_RECURRING           = 6;
    const TYPE_DUPLICATE_PROCESSING          = 7;
    const TYPE_INCORRECT_AMOUNT              = 8;
    const TYPE_CANCELLED_MERCHANDISE_SERVICE = 9;
    const TYPE_ALTERED_AMOUNT                = 10;
    
    public static $typesMap = [
        self::TYPE_FRAUD                         => 'Fraud',
        self::TYPE_PRODUCT_NOT_RECEIVED          => 'Product Not Received',
        self::TYPE_PRODUCT_NOT_AS_DESCRIBED      => 'Product Not As Described',
        self::TYPE_REFUND_NOT_RECEIVED           => 'Refund Not Received',
        self::TYPE_PROCESSING_ERRORS             => 'Processing Errors',
        self::TYPE_TRANSACTION_NOT_RECOGNISE     => 'Transaction not Recognise',
        self::TYPE_CANCELLED_RECURRING           => 'Cancelled Recurring',
        self::TYPE_DUPLICATE_PROCESSING          => 'Duplicate processing',
        self::TYPE_INCORRECT_AMOUNT              => 'Incorrect Amount',
        self::TYPE_CANCELLED_MERCHANDISE_SERVICE => 'Cancelled Merchandise/Service',
        self::TYPE_ALTERED_AMOUNT                => 'Altered Amount',
    ];

    public static $type = [
        self::TYPE_FRAUD,
        self::TYPE_PRODUCT_NOT_RECEIVED,
        self::TYPE_PRODUCT_NOT_AS_DESCRIBED,
        self::TYPE_REFUND_NOT_RECEIVED,
        self::TYPE_PROCESSING_ERRORS,
        self::TYPE_TRANSACTION_NOT_RECOGNISE,
        self::TYPE_CANCELLED_RECURRING,
        self::TYPE_DUPLICATE_PROCESSING,
        self::TYPE_INCORRECT_AMOUNT,
        self::TYPE_CANCELLED_MERCHANDISE_SERVICE,
        self::TYPE_ALTERED_AMOUNT,
    ];

    public function chargebackHistory()
    {
        return $this->belongsTo(ChargebackHistory::class, 'code', 'chargeback_code');
    }
}
