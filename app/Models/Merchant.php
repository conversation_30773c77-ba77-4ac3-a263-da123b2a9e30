<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Ramsey\Uuid\Uuid;

class Merchant extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'merchants';

    protected $primaryKey = 'merchant_id';

    protected $keyType = 'int';

    protected $guarded = [];

    public $incrementing = false;

    protected $casts = [
        'transfer'              => 'array',
        'special_auth_channels' => 'json'
    ];

    // 请求特殊处理MID
    public static $specialMerchant = ['160396110255821'];

    // 状态
    const STATUS_DISABLE = 0;
    const STATUS_ENABLE = 1;
    const STATUS_CHECK = 2;

    public static $statusMap = [
        self::STATUS_DISABLE => '禁用',
        self::STATUS_ENABLE  => '启用',
        self::STATUS_CHECK   => '待激活',
    ];

    const INNER_BUCKLE    = 0;
    const EXTERNAL_BUCKLE = 1;

    public static $deductionMap = [
        self::INNER_BUCKLE    => '内扣',
        self::EXTERNAL_BUCKLE => '外扣',
    ];

    const FIXED_FEE = 0;
    const RATIO_FEE = 1;

    public static $transferMap = [
        self::FIXED_FEE => '固定提现手续费',
        self::RATIO_FEE => '比例提现手续费',
    ];

    const TRANSFER_ACCOUNT_TYPE_TRADER = 0;
    const TRANSFER_ACCOUNT_TYPE_BILLING = 1;

    public static $transferAccountTypeMap = [
        self::TRANSFER_ACCOUNT_TYPE_TRADER  => '商户提现',
        self::TRANSFER_ACCOUNT_TYPE_BILLING => '账单提现',
    ];

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->{$this->getAuthIdentifierName()};
    }

    /**
     * Get the name of the unique identifier for the user.
     *
     * @return string
     */
    public function getAuthIdentifierName()
    {
        return $this->getKeyName();
    }

    public function businesses()
    {
        return $this->hasMany(MerchantBusiness::class, 'merchant_id');
    }

    public function users()
    {
        return $this->hasMany(User::class, 'merchant_id');
    }

    public function merchantApi()
    {
        return $this->belongsTo(MerchantApi::class, 'merchant_id', 'merchant_id');
    }

    public static function getApiToken()
    {
        do {
            $no = Uuid::uuid4()->getHex();
        } while (self::query()->where('api_token', $no)->exists());

        return $no;
    }

    public static function createMerchantId()
    {
        $merchantId = time();
        $time       = explode(' ', microtime());

        return $merchantId . str_pad(intval($time[0] * 100000), 5, '0', STR_PAD_LEFT);
    }

    public function merchantCard(){
        return $this->hasMany(MerchantCard::class, 'merchant_id', 'merchant_id');
    }

    public function merchantKyc(){
        return $this->hasOne(MerchantKyc::class, 'merchant_id', 'merchant_id');
    }
}
