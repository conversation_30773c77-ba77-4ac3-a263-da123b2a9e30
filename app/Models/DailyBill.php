<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;

use Illuminate\Database\Eloquent\Model;

class DailyBill extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'daily_bill';

    protected $guarded = [];

    // 状态
    const EXPORT_STATUS_NOT     = 0;
    const EXPORT_STATUS_ON      = 1;
    const EXPORT_STATUS_SUCCESS = 2;
    const EXPORT_STATUS_ERROR   = 3;

    public static $exportStatusMap = [
        self::EXPORT_STATUS_NOT     => '未开始',
        self::EXPORT_STATUS_ON      => '进行中',
        self::EXPORT_STATUS_SUCCESS => '导出成功',
        self::EXPORT_STATUS_ERROR   => '导出失败',
    ];

    public static $exportIdentityMap = [
    ];
}
