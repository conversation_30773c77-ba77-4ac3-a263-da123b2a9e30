<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class OrderPost extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'order_posts';
    protected $casts = [
        'address'  => 'json',
        'product'  => 'json',
        'customer' => 'json',
    ];
    protected $fillable = [
        'merchant_id', 'business_id', 'url_name', 'transaction_type', 'order_number', 'currency',
        'amount', 'card', 'address', 'product', 'customer', 'notify_url', 'return_url', 'attach', 'reserved', 'timeout'
    ];

    public function order()
    {
        return $this->hasOne(Order::class, 'order_id', 'order_id');
    }
}
