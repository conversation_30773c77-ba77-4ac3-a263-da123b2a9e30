<?php

namespace App\Scopes;

use Dcat\Admin\Admin;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Bid implements Scope
{
    public function apply(Builder $builder, Model $model)
    {
        $user = Admin::user();
        
        if (empty($user) || $user->getTable() == 'admin_users') {
            return;
        }

        if (!$user->isMainAccount()) {
            $bid = $user->bid->pluck('business_id')->toArray();
            $builder->whereIn('business_id', $bid);
        }
    }

}
