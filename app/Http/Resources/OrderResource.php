<?php

namespace App\Http\Resources;

class OrderResource extends Resource
{
    protected $showCardAllFields = false;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $data = parent::toArray($request);

        unset($data['url_id']);
        unset($data['address_id']);
        unset($data['card_id']);

        if (!$this->showCardAllFields) {
            $data['card'] = new OrderCardResource($this->whenLoaded('card'));
        } else {
            $data['card'] = (new OrderCardResource($this->whenLoaded('card')))->showSensitiveFields();
        }

	    $data['address'] = new OrderAddressResource($this->whenLoaded('address'));

        return $data;
    }

    public function showCardAllFields()
    {
        $this->showCardAllFields = true;

        return $this;
    }
}
