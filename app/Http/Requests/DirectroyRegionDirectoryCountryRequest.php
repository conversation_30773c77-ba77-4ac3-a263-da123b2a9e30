<?php

namespace App\Http\Requests;

use Illuminate\Validation\Rule;

class DirectroyRegionDirectoryCountryRequest extends Request
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
//            'countryIds.*' => 'required|numeric'
        ];
    }

    public function attributes()
    {
        return [
            'countryIds' => '国家',
        ];
    }
}
