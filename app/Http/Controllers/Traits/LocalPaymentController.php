<?php

namespace App\Http\Controllers\Traits;

use App\Classes\LocalPay\Contracts\Support;
use App\Models\Channel;
use App\Models\ChannelOrderUprate;
use App\Models\DirectoryCurrency;
use App\Models\Merchant;
use App\Models\MerchantBusiness;
use App\Models\MerchantUrl;
use Auth;
use App\Classes\LocalPay\Pay;
use App\Events\LocalOrderPaid;
use App\Events\LocalRefundPaid;
use App\Http\Requests\Api\LocalOrderRequest;
use App\Http\Requests\Api\LocalRefundRequest;
use App\Http\Resources\LocalOrderResource;
use App\Jobs\SendSlsLog;
use App\Models\LocalChannelRouteScheme;
use App\Models\LocalOrder;
use App\Models\LocalOrderAddress;
use App\Models\LocalOrderPost;
use App\Models\LocalOrderProduct;
use App\Models\LocalPaymentRefund;
use App\Models\LocalRefund;
use App\Models\LocalRefundPost;
use App\Services\LocalTransactionService;
use App\Services\UrlService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

trait LocalPaymentController
{
    public function pay(LocalOrderRequest $request, LocalOrder $order, LocalOrderAddress $address, LocalOrderProduct $product)
    {
        $orderId = LocalOrder::findAvailableNo();
        $request->request->set('amount', Support::amount_format($request->amount));
        $request->request->set('currency', strtoupper($request->currency));

        // 验证 business_id internal_status=1 表示激活状态
        $merchantBusiness = MerchantBusiness::where(['business_id' => $request->business_id, 'internal_status' => 1])->first();

        if (empty($merchantBusiness)) {
            return response()->json([
                'message' => 'Business Id Is Invalid',
                'errors'  => ['businessId' => ['Business Id Status Is Invalid Or Not Find']],
            ]);
        }

        // 保存商户提交数据
        $orderPost = new LocalOrderPost();
        $orderPost->fill($request->all());
        $orderPost->order_id = $orderId;
        $orderPost->save();

        // 数据验证
        // 商户订单号重复处理
        $infoData = $this->_repeateOrderNumberDeal($request);

        if (!empty($infoData)) {
            return response()->json([
                'message' => $infoData['msg'],
                'errors'  => [$infoData['type'] => [$infoData['msg']]],
            ]);
        }

        // 验证网址状态
        $mainUrlName = UrlService::_getMainUrlName($request->url_name);
        $merchantUrl = MerchantUrl::where(['business_id' => $request->business_id, 'main_url_name' => $mainUrlName, 'url_status' => 1])->first();

        if (empty($merchantUrl)) {
            return response()->json([
                'message' => 'Url Name Is Invalid',
                'errors'  => ['urlName' => ['Url Name Status Is Invalid Or Not Find']],
            ]);
        }

        DB::beginTransaction();

        // 地址信息
        $address->fill($request->address);


        // 保存订单地址信息
        try {
            $address->save();
        } catch (\Exception $e) {
            // 记录日志
            logger()->channel('intercept')->warning(
                'Order Address Error',
                ['order_number' => $request->order_number, 'error' => $e->getMessage()]
            );
            dispatch(new SendSlsLog(
                ['message' => 'Order Address Error'],
                ['order_number' => $request->order_number, 'error' => $e->getMessage()],
                'warning',
                'intercept'
            ));
            
            DB::rollBack();

            return response()->json([
                'message' => 'System Error',
                'errors'  => ['system' => ['Invalid Address Info']],
            ]);
        }

        // 接入的方式
        $accessType = 'directchannel';

        // 订单信息
        $order->fill($request->all());
        $order->order_id      = $orderId;
        $order->merchant_name = Auth::user()->merchant_name;
        $order->url_id        = $merchantUrl->id;
        $order->url_name      = $merchantUrl->url_name;
        $order->access_type   = $accessType;
        $order->status        = LocalOrder::STATUS_RECEIVED;
        $order->d_mcc_id      = $merchantUrl->d_mcc_id;
        $order->address()->associate($address->id);

        // 获取路由信息
        $channelList = $this->getChannelList($merchantUrl);

        if (empty($channelList)
            || (!isset($channelList[$merchantUrl->id][$accessType])
                && !isset($channelList['-'][$accessType]))
        ) {
            DB::rollBack();

            return response()->json([
                'message' => 'System Error',
                'errors'  => ['system' => ['No Available Transaction ChannelList']],
            ]);
        }

        $finalData = [
            'merchantUrl'      => $merchantUrl,
            'accessType'       => $accessType,
            'channelList'      => $channelList,
            'transactionType' => $request->transaction_type,
        ];

        $finalChannelData = $this->finalChannel($finalData, $order);

        if (empty($finalChannelData)) {
            DB::rollBack();

            return response()->json([
                'message' => 'System Error',
                'errors'  => ['system' => ['No Available Transaction Channel']],
            ]);
        }

        // 保存产品信息
        try {
            $product->adds($request->product, $order->order_id);
        } catch (\Exception $e) {
            // 记录日志
            logger()->channel('intercept')->warning('Order Product Error', ['order_number' => $request->order_number, 'error' => $e->getMessage()]);
            dispatch(new SendSlsLog(
                ['message' => 'Order Product Erro'],
                ['order_number' => $request->order_number, 'error' => $e->getMessage()],
                'warning',
                'intercept'
            ));
            
            DB::rollBack();

            return [
                'message' => 'System Error',
                'errors'  => ['system' => ['Invalid Product Info']],
            ];
        }

        // 保存订单信息
        $order->channelObj()->associate($finalChannelData['channel_data']['id']) ?? '0';
        $order->channel      = $finalChannelData['channel_data']['channel'] ?? '';

        $expiresTime         = !empty($request->timeout) ? $request->timeout : 3 * 24 * 60;  //1
        $order->expired_at   = date("Y-m-d H:i:s", strtotime(now()) + $expiresTime * 60);    // 分钟转秒//1
        $order->completed_at = now();

        try {
            $order->save();
        } catch (\Exception $e) {
            // 记录日志
            logger()->channel('intercept')->warning('Order Error', ['order_number' => $request->order_number, 'error' => $e->getMessage()]);
            dispatch(new SendSlsLog(
                ['message' => 'Order Error'],
                ['order_number' => $request->order_number, 'error' => $e->getMessage()],
                'warning',
                'intercept'
            ));
            
            DB::rollBack();

            return [
                'message' => 'System Error',
                'errors'  => ['system' => ['Invalid Order Info']],
            ];
        }

        $paymentData = [
            'order_number'     => $order->order_id,
            'currency'         => $finalChannelData['currency'],
            'amount'           => $finalChannelData['amount'],
            'transaction_type' => $order->transaction_type,
        ];

        if ($order->status == LocalOrder::STATUS_DECLINED) {
            $paymentData['status'] = $order->status;
        }

        try {
            // 保存网关订单信息
            $order->paymentOrder()->create($paymentData);
        } catch (\Exception $e) {
            // 记录日志
            logger()->channel('intercept')->warning('Gateway Error', ['order_number' => $request->order_number, 'error' => $e->getMessage()]);
            dispatch(new SendSlsLog(
                ['message' => 'Gateway Error'],
                ['order_number' => $request->order_number, 'error' => $e->getMessage()],
                'warning',
                'intercept'
            ));
            
            DB::rollBack();

            return [
                'message' => 'System Error',
                'errors'  => ['system' => ['Invalid Gateway Info']],
            ];
        }

        DB::commit();

        $res                        = [];
        $channelSupplierName        = strtolower($finalChannelData['channelSupplierName']);
        $config                     = $finalChannelData['config'];
        $config['channel']          = $finalChannelData['channel_data']['channel'];
        $config['channel_supplier'] = $channelSupplierName;

        $channelSupplier = Pay::$channelSupplierName($config);

        $order->load('paymentOrder', 'address', 'products');

        $paymentResultUp = [
            'order'         => [
                'transaction_type' => $order->transaction_type,
                'status'           => LocalOrder::STATUS_RECEIVED,
                'channel_id'       => $finalChannelData['channel_data']['id'],
                'channel'          => $finalChannelData['channel_data']['channel'] ?? '',
            ],
            'payment_order' => [
                'currency'         => $finalChannelData['currency'],
                'amount'           => $finalChannelData['amount'],
                'transaction_type' => $order->transaction_type,
                'payment_order_id' => '0',
                'status'           => LocalOrder::STATUS_RECEIVED,
            ]
        ];

        if ($order->throw_cnt == 0 && $order->status == LocalOrder::STATUS_DECLINED) {
            $paymentResult = [];
        } else {
            $order->paymentOrder->currency = $finalChannelData['currency'];
            $order->paymentOrder->amount   = $finalChannelData['amount'];

            $payload             = (new LocalOrderResource($order))->objectToArray();
            $payload['customer'] = $request->customer;

            $paymentResult                  = $channelSupplier->auth($payload);
            $paymentResult['attach']        = $request->attach;
            $paymentResult                  = $paymentResult->toarray();
            $paymentResult['order']         = array_merge($paymentResultUp['order'], $paymentResult['order']);
            $paymentResult['payment_order'] = array_merge($paymentResultUp['payment_order'], $paymentResult['payment_order']);
        }

        $res = $this->updateOrder($order, $paymentResult);

        return $res;
    }

    /**
     * 组装渠道所需数据
     *
     * @param $finalData
     * @param $order
     * @return array
     */
    public function finalChannel($finalData, $order): array
    {
        $merchantUrl     = $finalData['merchantUrl'];
        $accessType      = $finalData['accessType'];
        $channelList     = $finalData['channelList'];
        $transactionType = $finalData['transactionType'];
        $tempChannelArr  = $channelList[$merchantUrl->id][$accessType] ?? $channelList['-'][$accessType];

        // 路由组
        $aChannelArr  = $tempChannelArr[$transactionType]['a'] ?? $tempChannelArr['-']['a'] ?? [];
        
        if (empty($aChannelArr)) {
            return [];
        }
        
        $allChannelArr = array_keys($aChannelArr);

        // 获取可用账单标识
        $channels = Channel::where('status', Channel::STATUS_ENABLE)->select('channel', 'id', 'card_limits')
            ->whereIn('channel', $allChannelArr)
            ->get();

        $availableChannel = $channels->pluck('channel', 'id')->toArray();

        // 获取路由
        $channelGroup = [];
        // 根据权重选择一个账单标识
        $channel = $this->_getChannel($aChannelArr, $availableChannel, $channelGroup);
        
        return $this->getChannelData($channel, $order);
    }

    public function getChannelData($channel, $order): array
    {
        // 获取账单标识信息
        $channelData = Channel::with(['channelSupplier', 'channelPid'])->where('channel', $channel)->first();

        // 获取渠道名
        $channelSupplierName = strtolower($channelData->channelSupplier->file_name);

        // 获取账单标识配置信息
        $config = $this->_getConfig($channelData->config, $channelSupplierName);

        // 强制转换渠道授权货币
        $currency = $channelData->channelPid->payment_currency ?? $order->currency;
        $amount   = $order->amount;

        if ($currency != $order->currency) {
            // 获取汇率计算提交渠道金额
            $directoryCurrencyArr = DirectoryCurrency::whereIn('code', [$currency, $order->currency])->get()->pluck('rate', 'code');
            $tempAmount           = Support::amount_format($order->amount * ($directoryCurrencyArr[$currency] / $directoryCurrencyArr[$order->currency]));
            $amount               = $tempAmount;
        }

        $channelUprateList           = ChannelOrderUprate::where('channel_supplier_id', $channelData->channel_supplier_id)->pluck('uprate', 'currency');
        $merchantBusiness            = MerchantBusiness::find($order->business_id);
        $channelUprate               = $channelUprateList[$currency] ?? $channelUprateList['*'] ?? 0;
        $merchantBusinessOrderUprate = $merchantBusiness->order_uprate;

        $tempAmount = Support::amount_format($amount * (1 + $channelUprate * $merchantBusinessOrderUprate / 10000));
        $amount     = $this->_roundUpAmountByCurrency($currency, $tempAmount);

        $finalChannelData = [
            'channel_data'        => $channelData,
            'config'              => $config,
            'channelSupplierName' => $channelSupplierName,
            'currency'            => $currency,
            'amount'              => $amount,
        ];

        return $finalChannelData;
    }

    public function _refund(LocalRefundRequest $request, LocalRefund $refund)
    {
        $refundId = LocalOrder::findAvailableNo();
        // 保存商户提交数据
        $refundPost = new LocalRefundPost();
        $refundPost->fill($request->all());
        $refundPost->refund_id = $refundId;
        $refundPost->save();
        
        $order = LocalOrder::with(['refund', 'paymentOrder'])->find($request->order_id);

        // 判断交易状态
        if ($order->status != LocalOrder::STATUS_APPROVED) {
            return response()->json([
                'message' => 'No Available Refund',
                'errors'  => ['orderId' => ['Order Status Error']],
            ]);
        }

        // 币种金额强制转换
        $request->amount = Support::amount_format($request->amount);

        // 获取可退款最高金额
        $availableAmount = LocalTransactionService::getAvailableRefundAmount($order);

        if ($request->amount <= 0 || $availableAmount < $request->amount) {
            return response()->json([
                'message' => 'No Available Refund',
                'errors'  => ['amount' => ['Exceeds Amount Limit']],
            ]);
        }

        // 获取账单标识信息
        $channelData = Channel::with('channelSupplier')->find($order->channel_id);

        // 验证退款合理性验证
        if ($msg = $this->_validateReasonableRefund($channelData->channelSupplier->file_name, $request, $order)) {
            return response()->json([
                'message' => $msg,
                'errors'  => ['refund' => ['Other Errors']],
            ]);
        }

        $refund->fill($request->all());
        $refund->refund_id    = $refundId;
        $refund->completed_at = now();
        $refund->currency     = $order->currency;
        $refund->status       = LocalRefund::STATUS_RECEIVED;
        $refund->code         = '';
        $refund->result       = '';
        $refund->remark       = '';
        $refund->save();

        // 计算提交渠道退款金额
        if ($request->amount == $order->amount) { // 全额退款
            $refundAmount = $order->paymentOrder->amount;
        } else { // 部分退款
            if ($request->amount == $availableAmount) {
                // 退完订单剩余金额
                // 已退款渠道交易金额
                $refundIds                  = $order->refund->whereIn('status', [LocalRefund::STATUS_APPROVED, LocalRefund::STATUS_REVIEW, LocalRefund::STATUS_RECEIVED, LocalRefund::STATUS_PENDING])->pluck('refund_id', 'refund_id');
                $channelRefundedArr         = LocalPaymentRefund::whereIn('refund_id', $refundIds)->get()->pluck('amount', 'id')->toArray();
                $channelRefundedTotalAmount = array_sum($channelRefundedArr);
                $refundAmount               = $order->paymentOrder->amount - $channelRefundedTotalAmount;
            } else {
                // 部分退款
                if ($order->currency == $order->paymentOrder->currency) {
                    // 同币种 部分退款
                    $refundAmount = $request->amount;
                } else {
                    // 不同币种 部分退款
                    // 根据订单退款金额比例计算渠道退款金额，保留两位小数，不四舍五入。
                    $refundAmount = sprintf( "%.2f ", ($request->amount / $order->amount) * $order->paymentOrder->amount);
                }
            }
        }

        // 根据币种对金额进行向上取整
        $refundAmount = $this->_roundUpAmountByCurrency($order->paymentOrder->currency, $refundAmount);

        // 保存网关订单信息
        $refund->paymentRefund()->create([
            'order_number' => $refund->refund_id,
            'currency'     => $order->paymentOrder->currency,
            'amount'       => $refundAmount,
        ]);

        $refund->load('paymentOrder', 'paymentRefund');

        //判断今日退款是否大于限定次数
        $key          = 'Local_Refunds_Number_' . date('Y-m-d');
        $refundNumber = Cache::has($key) ? Cache::get($key) : 0;

        if ($refundNumber < 20) {
            $refundResult = $this->refundResult($refund, $order);
        } else {
            $refundResult['refund']         = [
                'status' => LocalRefund::STATUS_PENDING,
                'code'   => get_system_code('210'),
                'result' => 'Transaction is pending',
                'remark' => '交易待定',
            ];
            $refundResult['payment_refund'] = [
                'payment_refund_id' => '0',
                'code'              => '',
                'result'            => '',
                'remark'            => '',
                'status'            => LocalPaymentRefund::STATUS_PENDING
            ];
        }

        return $this->updateRefund($refund, $refundResult);
    }

    /**
     * 渠道请求
     *
     * @param $refund
     * @param $order
     * @return mixed
     */
    public function refundResult($refund, $order)
    {
        // 获取账单标识信息
        $channelData = Channel::with('channelSupplier')->where('channel', $order->channel)->first();

        // 获取渠道名
        $channelSupplierName = strtolower($channelData->channelSupplier->file_name);

        // 获取账单标识配置信息
        $config = $this->_getConfig($channelData->config, $channelSupplierName);

        //	    $config = config('pay.xborderpay');

        $channelSupplier = Pay::$channelSupplierName($config);

        $refundResult = $channelSupplier->refund($refund->toArray());

        return $refundResult;
    }

    /**
     * 退款查询
     * @param $refundId
     * @return mixed
     */
    public function _refundQuery($refundId)
    {
        $refund = LocalRefund::find($refundId);

        if (empty($refund)) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors'  => ['refundId' => ['The selected refund id is invalid.']],
            ]);
        }

        return $this->packageRefundData($refund);
    }

    public function query($request)
    {
        $order = LocalOrder::find($request);

        if (empty($order)) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors'  => ['orderId' => ['The selected order id is invalid.']],
            ]);
        }

        return $this->packageAuthCaptureData($order);
    }

    /**
     * 交易更新
     *
     * @param LocalOrder $order
     * @param $paymentResult
     * @param bool $source 来源字段 标识终态是否可以更新
     * @param bool $reserved 保留字段 标识是否base64加密返回
     * @return array
     */
    protected function updateOrder(LocalOrder $order, $paymentResult, $source = true, $reserved = true)
    {
        // 是否执行交易后事件
        $flag = true;

        if (count($paymentResult) > 1) {
            // 交易完成时间
            $paymentResult['order'] = Arr::add($paymentResult['order'], 'completed_at', now());

            if ($source) {
                // 更新规则
                switch ($order->status) {
                    case LocalOrder::STATUS_DECLINED: // 终态不更新状态
                    case LocalOrder::STATUS_EXPIRED:  // 终态不更新状态
                        if (!isset($paymentResult['order']['channel']) || $paymentResult['order']['channel'] == $order->channel) {
                            $paymentResult['payment_order'] = Arr::except($paymentResult['payment_order'], ['status']);
                            $paymentResult['order']         = Arr::except($paymentResult['order'], ['status']);
                            $paymentResult['order']         = Arr::except($paymentResult['order'], ['completed_at']);
                            $flag                           = false;

                            if (!isset($paymentResult['extra'])) {
                                // 终态直接返回
                                return $this->packageAuthCaptureData($order);
                            }
                        }
                        break;
                    case LocalOrder::STATUS_APPROVED: // 成功状态的交易不更新状态、完成时间
                        // 终态直接返回
                        return $this->packageAuthCaptureData($order);
                }
            }

            DB::beginTransaction();
            // 添加交易历史
            if (
                $order->code != $paymentResult['order']['code'] ||
                (isset($paymentResult['order']['channel']) && $paymentResult['order']['channel'] != $order->channel)
            ) {
                $order->orderHistory()->create([
                    'order_number'     => $order->paymentOrder->order_number ?? '0',
                    'payment_order_id' => $order->paymentOrder->payment_order_id ?? '0',
                    'channel_id'       => $order->channel_id,
                    'channel'          => $order->channel,
                    'transaction_type' => $order->transaction_type,
                    'status'           => $order->status ?? LocalOrder::STATUS_RECEIVED,
                    'code'             => $order->code ?? '',
                    'result'           => $order->result ?? '',
                    'remark'           => $order->remark ?? '',
                    'channel_type'     => $order->paymentOrder->type,
                    'channel_status'   => $order->paymentOrder->status,
                    'channel_code'     => $order->paymentOrder->code ?? '',
                    'channel_result'   => $order->paymentOrder->result ?? '',
                    'channel_remark'   => $order->paymentOrder->remark ?? '',
                ]);
            }

            // 更新订单信息
            if (isset($paymentResult['order']['channel_id'])) {
                $order->channelObj()->associate($paymentResult['order']['channel_id']);
            }

            $order->paymentOrder()->update($paymentResult['payment_order']);
            $order->update($paymentResult['order']);

            DB::commit();
        } else { // 为空不执行交易后事件
            $flag = false;
        }

        $order->load('paymentOrder', 'channelObj:id,card_limit_time');
        $returnData = $this->packageAuthCaptureData($order, $paymentResult['attach'] ?? '', $reserved);

        if ($flag) {
            $this->afterPaid($order, $returnData);
        }

        return $returnData;
    }

    protected function updateRefund(LocalRefund $refund, $refundResult, $source = true)
    {
        // 是否执行交易后事件
        $flag = true;
        // 完成时间
        $refundResult['refund'] = Arr::add($refundResult['refund'], 'completed_at', now());
        // 更新规则
        if ($source) {
            switch ($refund->status) {
                case LocalRefund::STATUS_DECLINED: // 终态不更新状态
                    $refundResult['payment_refund'] = Arr::except($refundResult['payment_refund'], ['status']);
                    $refundResult['refund']         = Arr::except($refundResult['refund'], ['status']);
                    $refundResult['refund']         = Arr::except($refundResult['refund'], ['completed_at']);
                    $flag                           = false;
                    break;
                case LocalRefund::STATUS_REVIEW:  // 终态不更新状态
                case LocalRefund::STATUS_APPROVED: // 成功状态的退款交易不更新状态、完成时间
                    // 终态直接返回
                    $refund->load('order');
                    return $this->packageRefundData($refund);
            }
        }
        $refund->paymentRefund()->update($refundResult['payment_refund']);
        $refund->update($refundResult['refund']);

        if (isset($refundResult['refund']['status']) && $refundResult['refund']['status'] == LocalRefund::STATUS_APPROVED) {
            $tomorrowTime = strtotime(date('Y-m-d', strtotime('+ 1day')));
            $endTime      = $tomorrowTime - time();
            $key          = 'Local_Refunds_Number_' . date('Y-m-d');
            Cache::has($key) ? Cache::increment($key) : Cache::add($key, 1, $endTime);
        }

        $returnData = $this->packageRefundData($refund);

        if ($flag) {
            $this->afterRefund($refund, $returnData);
        }
        $refund->load('order');

        return $returnData;
    }

    protected function packageAuthCaptureData($order, $attach = '', $reserved = true)
    {
        $reservedJson = '';

        // 组装返回数据
        $returnData = array(
            'merchantId'      => $order->merchant_id,
            'businessId'      => $order->business_id,
            'orderNumber'     => $order->order_number,
            'transactionType' => $order->transaction_type,
            'completeDate'    => $order->completed_at->toDateTimeString(),
            'orderId'         => $order->order_id,
            'currency'        => $order->currency,
            'amount'          => Support::amount_format($order->amount),
            'attach'          => $attach,
            'reserved'        => $reserved ? $reservedJson : base64_encode($reservedJson),
            'status'          => $order->status,
            'code'            => $order->code,
            'result'          => $order->result,
            'remark'          => $order->remark,
            'signType'        => 'MD5'
        );

        // 获取商户信息
        $merchant = Merchant::find($order->merchant_id);

        // 数据签名
        $signStr = implode('', $returnData) . $merchant->api_token;

        // html不参数签名
        $returnData['html'] = !empty($order->paymentOrder->html) && $order->paymentOrder->html != '[]' ? $order->paymentOrder->html : '';

        switch ($returnData['signType']) {
            case 'SHA256':
                $returnData['sign'] = hash('sha256', $signStr);
                break;
            case 'MD5':
                $returnData['sign'] = md5($signStr);
                break;
            default:
                $returnData['sign'] = md5($signStr);
        }

        return $returnData;
    }

    protected function packageRefundData($refund)
    {
        // 组装返回数据
        $returnData = array(
            'merchantId'      => $refund->order->merchant_id,
            'businessId'      => $refund->order->business_id,
            'orderId'         => $refund->order_id,
            'refundId'        => $refund->refund_id,
            'transactionType' => 'refund',
            'currency'        => $refund->currency,
            'amount'          => Support::amount_format($refund->amount),
            'status'          => $refund->status,
            'code'            => $refund->code,
            'result'          => $refund->result,
            'remark'          => $refund->remark,
            'completeDate'    => $refund->completed_at->toDateTimeString(),
            'signType'        => 'MD5'
        );
        // 获取商户信息
        $merchant = Merchant::find($refund->order->merchant_id);
        // 数据签名
        $signStr = implode('', $returnData) . $merchant->api_token;

        switch ($returnData['signType']) {
            case 'SHA256':
                $returnData['sign'] = hash('sha256', $signStr);
                break;
            case 'MD5':
                $returnData['sign'] = md5($signStr);
                break;
            default:
                $returnData['sign'] = md5($signStr);
        }
        return $returnData;
    }


    protected function afterPaid(LocalOrder $order, $returnData)
    {
        event(new LocalOrderPaid($order, $returnData));
    }

    protected function afterRefund(LocalRefund $refund, $returnData)
    {
        event(new LocalRefundPaid($refund, $returnData));
    }


    /**
     * 获取路由列表
     *
     * @param $merchantUrl
     * @return array
     */
    public static function getChannelList($merchantUrl)
    {
        $tempChannelList = [];
        $channelList     = [];

        // 根据网址获取交易路由
        $tempChannelRouteSchemelist = LocalChannelRouteScheme::where('business_id', $merchantUrl->business_id)->where('d_mcc_id', $merchantUrl->d_mcc_id)->get();
        $channelRouteSchemelist     = $tempChannelRouteSchemelist ? $tempChannelRouteSchemelist->toArray() : [];

        if (!empty($channelRouteSchemelist)) {
            foreach ($channelRouteSchemelist as $key => $channelRouteScheme) {

                $tmpRoute = $channelRouteScheme['extra_attributes'];

                foreach ($tmpRoute as $urlId => $routeList) {
                    if (in_array($urlId, array('-', $merchantUrl->id))) {
                        $tempChannelList[$urlId] = $routeList;
                    }

                    if (empty($tempChannelList)) {
                        continue;
                    }

                    foreach ($tempChannelList as $urlId => $routeList) {
                        foreach ($routeList as $type => $mixList) {

                            if ($type == 'directchannel') {
                                continue;
                            }

                            foreach ($mixList as $accessType => $transactionTypeList) {
                                foreach ($transactionTypeList as $transactionType => $groupList) {
                                    foreach ($groupList as $group => $tmpChannelList) {
                                        if (empty($tmpChannelList)) {
                                            continue;
                                        }

                                        foreach ($tmpChannelList as $channel => $weight) {
                                            $channelList[$urlId][$accessType][$transactionType][$group][$channel] = $weight;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return $channelList;
    }

    /**
     * 获取账单标识
     *
     * @param $channelArr
     * @param $availableChannelArr
     * @param array $channelGroup
     * @return int|string
     */
    protected function _getChannel($channelArr, $availableChannelArr, $channelGroup = [])
    {
        $channel = '';

        //channelArr过滤
        $channelArr = $this->channelArrFilter($channelArr, $availableChannelArr, $channelGroup);

        if (empty($channelArr)) {
            return $channel;
        }

        $weightTotal = mt_rand(0, array_sum($channelArr));

        // 根据权重选择抛投账单标识
        foreach ($channelArr as $tempChannel => $value) {
            $weightTotal -= $value;

            if ($weightTotal <= 0) {
                $channel = $tempChannel;
                break;
            }
        }

        return $channel;
    }

    protected function _getConfig($configData, $channelSupplierName)
    {
        $config = [];

        // 循环组装数据
        foreach ($configData as $value) {
            $config[$value['key']] = $value['value'];
        }

        // 增加日志file
        if (!empty($config)) {
            $config['log'] = ['file' => storage_path('logs/' . $channelSupplierName . 'Local' . '.log')];
        }

        return $config;
    }

    /**
     * 退款合理性验证
     *
     * @param $supplierName
     * @param $request
     * @param $order
     * @return string
     */
    protected function _validateReasonableRefund($supplierName, $request, $order)
    {
        $msg = '';

        // BNT、HONY、Hai、Spray 部分退款 需要超24h
        if (
            in_array($supplierName, array('BNT', 'HONY', 'Hai', 'Spray'))
            && $request->amount < $order->amount
            && $order->updated_at >= date('Y-m-d H:i:s', strtotime('-24 hours'))
        ) {
            $msg = 'Orders Require More Than 24 Hours For Partial Refund';
        } else if ($supplierName == 'goodiespay') { // goodiespay 退款需要等前一个退款审核完成之后才能再次发起
            if (!empty($order->refund)) {
                foreach ($order->refund as $refund) {
                    if ($refund->status == LocalRefund::STATUS_PENDING) {
                        $msg = 'This Order Has Been Refunded';
                    }
                }
            }
        } else if (in_array($supplierName, ['Ctp', 'Fdc']) && $request->amount < $order->amount) {
            $msg = 'Orders only supports full refund';
        } else if ($supplierName == 'Acapay') {
            $msg = 'Refund interface under development...';
        }

        return $msg;
    }

    /**
     * 商户订单号重复处理
     *
     * @param $request
     * @return array
     */
    protected function _repeateOrderNumberDeal($request)
    {
        // 1.同一商户订单号有成功订单 2.同一商户订单号十秒内有创建订单
        $orderList  = LocalOrder::where('business_id', $request->business_id)->where('order_number', $request->order_number)->get();
        $infoData   = array();
        $flag       = false;
        $limiteDate = date('Y-m-d H:i:s', strtotime('-10 seconds'));
        $cnt        = 0;

        if (!empty($orderList)) {
            foreach ($orderList as $order) {
                if ($order['status'] == LocalOrder::STATUS_APPROVED) {
                    $flag = true;
                    break;
                }

                if ($order['created_at'] > $limiteDate) {
                    $cnt++;
                }
            }

            if ($cnt >= 1) {
                $flag = true;
            }
        }

        if ($flag) {
            $infoData['type'] = 'orderNumber';
            $infoData['msg']  = 'Duplicate Order Number';
            return $infoData;
        }

        return $infoData;
    }

    /**
     * 根据币种对金额进行向上取整
     *
     * @param $currency
     * @param $amount
     * @return string
     */
    protected function _roundUpAmountByCurrency($currency, $amount)
    {
        // 需要向上取整的币种列表
        $currencyArr = array('TWD');

        if (in_array($currency, $currencyArr)) {
            return Support::amount_format(ceil($amount));
        }

        return $amount;
    }

    /**
     * channelArr过滤
     *
     * @param $channelArr
     * @param $availableChannelArr
     * @param $channelGroup
     * @return array
     */
    public function channelArrFilter($channelArr, $availableChannelArr, $channelGroup): array
    {
        $newChannelArr = [];
        foreach ($channelArr as $tempChannel => $value) {
            if (!in_array($tempChannel, $availableChannelArr)) {
                continue;
            }

            if (in_array($tempChannel, $channelGroup)) {
                continue;
            }

            $newChannelArr[$tempChannel] = $value;
        }

        return $newChannelArr;
    }
}
