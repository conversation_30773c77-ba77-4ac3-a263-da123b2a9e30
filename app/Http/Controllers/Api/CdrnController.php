<?php

namespace App\Http\Controllers\Api;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\ChargebackCase;
use App\Services\AlertService;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Classes\Supports\Traits\HasHttpRequest;

class CdrnController extends Controller
{
    use HasHttpRequest;
    /**
    *CDRN 通知
    */
    public function notice(Request $request)
    {
        $post = $request->all();

        // 路径参数格式转换
        $string = $request->server()['QUERY_STRING'] ?? '';
        if (!empty($string)) {
            parse_str(urldecode($string), $post);
        }

        $config = [
            'file'     => storage_path('logs/cdrnNotice.log'),
            'identify' => 'notice'
        ];
        $logger = new Logger();
        $logger->setConfig($config);

        // 记录日志
        $logger->info('Cdrn Notice: ', ['url' => $request->fullUrl(), 'data' => $post]);
        dispatch(new SendSlsLog(
            ['message' => 'Cdrn Notice:'],
            ['url' => $request->fullUrl(), 'data' => $post],
            'info',
            'cdrn'
        ));

        // 参数转换
        $post = $this->_paramsConvert($post);

        // 转发到CDRN中转地址
        $this->post('http://8.218.201.77:9999/Alert/Cdrn.php', $post);
        // if (empty($post['caseid'])) {
        //     $logger->info('Cdrn Notice: 缺少caseid参数。');
        //     dispatch(new SendSlsLog(
        //         ['message' => 'Cdrn Notice: 缺少caseid参数。'],
        //         [],
        //         'info',
        //         'cdrn'
        //     ));
        // } else {
        //     AlertService::getService(ChargebackCase::ALERT_FROM_VERIFI)->notice($post);
        // }

        echo 'success';
    }

    /**
     * 参数转换
     *
     * @param $params
     * @return array
     */
    protected function _paramsConvert($params)
    {
        $data = [];

        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $data[(string)Str::of($key)->lower()] = is_array($value) ? $this->_paramsConvert($value) : (string)Str::of($value)->upper();
            }
        }

        return $data;
    }
}