<?php

namespace App\Http\Controllers\Api;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use Illuminate\Http\Request;
use App\Services\Shield\EmbracyShieldService;

class EmbracyShieldController extends Controller
{
    /**
     *riskshieId预警系统通知
     */
    public function notice(Request $request)
    {
        $post   = $request->all();
        $config = [
            'file'     => storage_path('logs/embracyShieldNotice.log'),
            'identify' => 'notice'
        ];
        $logger = new Logger();
        $logger->setConfig($config);

        // 记录日志
        $logger->info('EmbracyShield Notice: ', ['url' => $request->fullUrl(), 'data' => $post]);
        dispatch(new SendSlsLog(
            ['message' => 'EmbracyShield Notice:'],
            ['url' => $request->fullUrl(), 'data' => $post],
            'info',
            'EmbracyShield'
        ));

        // 验证所需参数
        $arr = ['caseId', 'merchantId', 'shieldId', 'alertType', 'alertServe', 'reasonCode', 'transactionDate', 'cardNumber', 'cardBill', 'alertCurrency', 'alertAmount'];
        foreach ($arr as $vo) {
            if (!isset($post[$vo]) || $post[$vo] == '') {
                $logger->info('EmbracyShield ' .$vo. '参数为空', ['data' => $post]);
                dispatch(new SendSlsLog(
                    ['message' => 'EmbracyShield ' .$vo. '参数为空'],
                    ['data' => $post],
                    'info',
                    'EmbracyShield'
                ));

                echo 'ok';die;
            }
        }

        $service = new EmbracyShieldService();
        $service->notice($post);
        echo 'ok';die;
    }
}
