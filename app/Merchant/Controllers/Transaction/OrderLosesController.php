<?php

namespace App\Merchant\Controllers\Transaction;

use App\Merchant\Repositories\OrderLoses;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Grid;
use Illuminate\Support\Facades\Auth;

class OrderLosesController extends AdminController
{
    public function title()
    {
        return admin_trans_label('丢失订单');    
    }

    protected function grid()
    {
        return Grid::make(new OrderLoses(), function (Grid $grid) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('id','desc');
            $grid->column('business_id');
            $grid->column('order_number');
            $grid->column('url_name');
            $grid->column('post_data', admin_trans_field('amount'))->display(function ($val) {
                $data = json_decode($val,1);
                return $data['amount'] ?? 0;
            });
            $grid->column('return_data')->display(function ($val) {
                if ($this->merchant_id == '163953459255967'){
                    $data = json_decode($val,1);
                    if ($data['message'] == 'Black Block'){
                        return '{"message":"Black Block","errors":{"system":["Black Block"]}}';
                    }
                }

                return $val;
            });
            $grid->column('reason','风控描述')->display(function ($val) {
                if ($this->merchant_id == '163953459255967'){
                    if (strpos($val, '黑名单拦截') !== false) {
                        return '黑名单拦截';
                    }
                }

                return $val;
            });
            $grid->column('created_at');



            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('order_number');
                $filter->equal('url_name')->select(\App\Models\MerchantUrl::where('merchant_id', Auth::user()->merchant_id)->get()->pluck('url_name', 'url_name')->toArray());
				$filter->like('reason','风控描述');
			});
        });
    }
}
