<?php

namespace App\Merchant\Controllers\CardVirtual;

use App\Admin\Actions\Tools\CardRechargeTool;
use App\Admin\Actions\Tools\CardVirtualSettlementsTool;
use App\Admin\Actions\Tools\CardVirtualTransTool;
use App\Admin\Exceptions\BalanceCheck;
use App\Admin\Exceptions\CardDestroy;
use App\Admin\Exceptions\CardBlock;
use App\Admin\Exceptions\CardDestroyWithdraw;
use App\Admin\Exceptions\CardHolderBind;
use App\Admin\Exceptions\CardUnBlock;
use App\Admin\Exceptions\CardRefund;
use App\Admin\Exceptions\CardTransactions;
use App\Merchant\Actions\Tools\CardVirtual\CardVirtualExportTool;
use App\Merchant\Controllers\MerchantSecondaryValidationController;
use App\Merchant\Repositories\CardVirtual;
use App\Models\CardVirtual as CardVirtualModel;
use App\Models\MerchantCard;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Modal;
use Illuminate\Support\Facades\Auth;
use App\Admin\Exceptions\CardRecharge;
use App\Admin\Exceptions\CardVirtual as CardVirtualDetails;
use DES3;

class CardVirtualController extends MerchantCardBaseController
{
    public function title()
    {
        return admin_trans_label('虚拟卡管理');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        $is2faCheck = $this->is2faCheck();
        return Grid::make(new CardVirtual(), function (Grid $grid) use ($is2faCheck) {
            $grid->model()->where('merchant_id', Auth::user()->merchant_id)->orderBy('created_at', 'desc');
            $grid->column('virtual_id');
            $grid->column('merchant_id');
            $grid->column('cards_id');
            $grid->column('bin');
            $grid->column('batch_id');
            $grid->column('card_number')->display(function ($value) {
                return empty($value) ? '' : get_markcard(DES3::decrypt($value, env('DES3_CARD_VIRTUAL')));
            });
            $grid->column('available_balance');
            $grid->column('card_type')->display(function ($val) {
                return admin_trans_option(CardVirtual::$internalTypetMap[$val] ?? admin_trans_field('未知'), 'internal_type_map');
            });
            $grid->column('day_amount_limit');
            $grid->column('status')->display(function ($val) {
                return admin_trans_option(CardVirtual::$internalMerchantStatusMap[$val] ?? admin_trans_field('未知'), 'internal_status_map');
            });
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->fixColumns(0, -1);

            $grid->actions(function (Grid\Displayers\Actions $actions) use ($is2faCheck) {
                if ($this->status == 1) {
                    $actions->append(new BalanceCheck($this->virtual_id));
                    $actions->append('&emsp;');
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('余额查询') . '</button>&emsp;');
                }

                if (in_array($this->status, [CardVirtualModel::DEACTIVATE, CardVirtualModel::ACTIVATION, CardVirtualModel::WAITDESTROY, CardVirtualModel::DESTROY, CardVirtualModel::DESTROYPENDING, CardVirtualModel::MERCHANT_BLOCK, CardVirtualModel::SYSTEM_BLOCK])) {
                    $actions->append(Modal::make()->xl()->body(CardTransactions::make()->payload(['card_number' => $this->card_number]))->button('<button class="btn-outline-primary btn btn-primary btn-shadow sys-custom-purple">' . admin_trans_label('交易明细') . '</button>&emsp;'));
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('交易明细') . '</button>&emsp;');
                }

                if ($this->status == 1 && $this->card_type == 0) {
                    $actions->append(Modal::make()->lg()->body(CardRecharge::make()->payload(['id' => $this->virtual_id]))->button('<button class="btn-outline-primary btn btn-primary btn-shadow sys-custom-emerald-green">' . admin_trans_label('充值') . '</button>&emsp;'));
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('充值') . '</button>&emsp;');
                }

                if ($this->status == 1 && $this->card_type == 0) {
                    $actions->append(Modal::make()->lg()->body(CardRefund::make()->payload(['id' => $this->virtual_id]))->button('<button class="btn-outline-primary btn btn-primary btn-shadow sys-custom-red">' . admin_trans_label('退值') . '</button>&emsp;'));
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('退值') . '</button>&emsp;');
                }

                if ($this->status == 1) {
                    $actions->append(new CardDestroy($this->virtual_id));
                    $actions->append('&emsp;');
                    $actions->append(new CardBlock($this->virtual_id));
                    $actions->append('&emsp;');
                } elseif ($this->status == 6) {
                    $actions->append(new CardDestroyWithdraw($this->virtual_id));
                    $actions->append('&emsp;');
                } elseif ($this->status == 8 || $this->status == 9) {
                    $actions->append(new CardUnBlock($this->virtual_id));
                    $actions->append('&emsp;');
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('销卡') . '</button>&emsp;');
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('冻结') . '</button>&emsp;');
                }

                if (in_array($this->status, [CardVirtualModel::ACTIVATION, CardVirtualModel::MERCHANT_BLOCK, CardVirtualModel::SYSTEM_BLOCK])) {
                    if ($is2faCheck) {
                        $faView = admin_route('2fa.merchant.verificationPage', ['mark' => 'google2fa', 'mode' => MerchantSecondaryValidationController::MODE_CARD_DETAIL]);
                        $modal  = "<a href={$faView} class='btn-outline-primary btn btn-primary btn-shadow sys-custom-sky-blue'>" . admin_trans_field('详情') . "</a>&emsp;";
                    } else {
                        $modal = Modal::make()->lg()->body(CardVirtualDetails::make()->payload(['id' => $this->virtual_id]))->button('<button class="btn-outline-primary btn btn-primary btn-shadow sys-custom-sky-blue">' . admin_trans_label('详情') . '</button>&emsp;');
                    }
                    $actions->append($modal);
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('详情') . '</button>&emsp;');
                }

                if (empty($this->cardholder_id) && in_array($this->status, [CardVirtualModel::ACTIVATION, CardVirtualModel::MERCHANT_BLOCK, CardVirtualModel::SYSTEM_BLOCK])) {
                    // 持卡人绑定
                    $modal = Modal::make()
                        ->title(admin_trans_label('持卡人绑定'))
                        ->lg()
                        ->body(CardHolderBind::make()->payload(['virtual_id' => $this->virtual_id]))
                        ->button("<button class='btn btn-primary btn-shadow sys-custom-orange'>" . admin_trans_label('持卡人绑定') . "</button>&emsp;");
                    $actions->append($modal . '&nbsp;');
                } else {
                    $actions->append('<button class="btn btn-white btn-radius disabled">' . admin_trans_label('持卡人绑定') . '</button>&emsp;');
                }

            });

            $grid->disableCreateButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableBatchDelete();

            $grid->batchActions(function ($batch) {
                $batch->add(new CardRechargeTool(admin_trans('card-virtual.labels.充值')));
                $batch->add(new CardVirtualTransTool(admin_trans('card-virtual.labels.同步交易')));
                $batch->add(new CardVirtualSettlementsTool(admin_trans('card-virtual.labels.同步结算')));
            });

            $grid->tools(new CardVirtualExportTool());

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('cards_id')->select(MerchantCard::where('merchant_id', Auth::user()['merchant_id'])->pluck('cards_id', 'cards_id'));
                $filter->equal('bin')->select(MerchantCard::where('merchant_id', Auth::user()['merchant_id'])->pluck('bin', 'bin'));
                $filter->equal('batch_id');
                $filter->equal('virtual_id');
                $filter->where('card_number', function ($query) {
                    $query->where('card_number', DES3::encrypt($this->input, env('DES3_CARD_VIRTUAL')));
                });
                $filter->equal('card_type')->select(admin_trans_label('internal_type_map', CardVirtual::$internalTypetMap));

                $statusMap = CardVirtual::$internalMerchantStatusMap;
                unset($statusMap[CardVirtualModel::WAITSUBMIT], $statusMap[CardVirtualModel::SYSTEM_BLOCK], $statusMap[CardVirtualModel::WAITDESTROY]);
                $filter->where('status', function ($query) {
                    $status      = $this->input;
                    $whereStatus = [$status];
                    switch ($status) {
                        case CardVirtualModel::WAITSUBMIT:
                        case CardVirtualModel::PENDING:
                            $whereStatus = [CardVirtualModel::WAITSUBMIT, CardVirtualModel::PENDING];
                            break;
                        case CardVirtualModel::SYSTEM_BLOCK:
                        case CardVirtualModel::MERCHANT_BLOCK:
                            $whereStatus = [CardVirtualModel::SYSTEM_BLOCK, CardVirtualModel::MERCHANT_BLOCK];
                            break;
                        case CardVirtualModel::WAITDESTROY:
                        case CardVirtualModel::DESTROYPENDING:
                            $whereStatus = [CardVirtualModel::WAITDESTROY, CardVirtualModel::DESTROYPENDING];
                            break;
                    }
                    $query->whereIn('status', $whereStatus);
                })->select(admin_trans_label('internal_status_map', $statusMap));

                $filter->between('created_at')->datetime(['sideBySide'=>true]);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CardVirtual(), function (Show $show) {
            $show->field('card_number')->display(function ($value) {
                return empty($value) ? '' : get_markcard(DES3::decrypt($value, env('DES3_CARD_VIRTUAL')));
            });
            $show->field('cvv')->display(function ($value) {
                return empty($value) ? '' : get_mark_data(DES3::decrypt($value, env('DES3_CARD_VIRTUAL')));
            });
            $show->field('expiration_year');
            $show->field('expiration_month');

            $show->panel()
                ->tools(function ($tools) {
                    $tools->disableEdit();
                    $tools->disableList();
                    $tools->disableDelete();
                });
        });
    }
}
