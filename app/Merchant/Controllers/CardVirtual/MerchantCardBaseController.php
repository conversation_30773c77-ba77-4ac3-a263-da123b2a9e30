<?php

namespace App\Merchant\Controllers\CardVirtual;

use Dcat\Admin\Http\Controllers\AdminController;

class MerchantCardBaseController extends AdminController
{
    const MERCHANT_FA_CHECK_KEY          = 'MerchantCardVirtualCheckMFA';
    const MERCHANT_FA_CHECK_CONSUMED_KEY = 'MerchantConsumedCheckMFA';

    const FA_CHECK_AUTH_CODE_KEY          = 'CardVirtualCheckAuthCodeMFA';
    const FA_CHECK_CONSUMED_AUTH_CODE_KEY = 'ConsumedCheckAuthCodeMFA';

    protected function is2faCheck(int $type = 0): bool
    {
        $mfaKey   = '';
        $checkKey = '';
        switch ($type) {
            case 0:// 卡详情
                $mfaKey   = self::MERCHANT_FA_CHECK_KEY;
                $checkKey = self::MERCHANT_FA_CHECK_CONSUMED_KEY;
                break;
            case 1:// 授权码
                $mfaKey   = self::FA_CHECK_AUTH_CODE_KEY;
                $checkKey = self::FA_CHECK_CONSUMED_AUTH_CODE_KEY;
                break;
        }


        // 检查是否已经有一个有效的验证标记
        if (session($mfaKey) && !session($checkKey)) {
            session([$checkKey => false]);
            return true;
        }

        // 获取登录时间
        $logout = session('merchantLogout');
        // 如果 本次登录时间没过期 无需再进行验证
        if (session($mfaKey) && session($checkKey) && $logout > time()) {
            return false;
        }

        // 设置验证标记，但不立即消耗它
        session([$mfaKey => true, $checkKey => false]);

        return true;
    }
}