<?php
namespace App\Merchant\Controllers;

use App\Admin\Repositories\DirectoryDictionary;
use App\Classes\Pay\Exceptions\Exception;
use App\Handlers\ImageUploadHandler;
use App\Models\DirectoryCarrier;
use App\Models\OrderAddress;
use App\Models\OrderComplaint;
use App\Models\OrderProduct;
use App\Models\OrderRelation;
use App\Models\OrderTrack;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Support\Facades\DB;

class CustomCareController extends AdminController
{
    protected $title = 'Custom Care';

    public function index(Content $content)
    {
        // 初始化参数
        $displayData = [
            'order'            => [],
            'orderAddress'     => [],
            'orderProduct'     => [],
            'orderDelivery'    => [],
            'orderComplaint'   => [],
            'trackingList'     => [],
            'complaintTagList' => [],
            'messageList'      => [],
            'token'            => request('token')
        ];

        // 投诉标题
        $dictionaryModel                 = new DirectoryDictionary();
        $displayData['complaintTagList'] = $dictionaryModel->getTypeList('投诉标题')->toArray();

        // 订单信息
        $token     = substr($displayData['token'], 1);
        $order     = OrderRelation::with('order')->firstWhere('custom_token', $token);
        $orderData = !empty($order) ? $order->toArray() : array();

        if (!empty($orderData)) {
            $displayData['order'] = $orderData['order'];

            // 状态显示
            $statusList = [];

            if (isset($orderData['is_refund']) && $orderData['is_refund']) {
                $statusList[] = 'Refunded';
            }

            if (isset($orderData['is_dishonour']) && $orderData['is_dishonour']) {
                $statusList[] = 'Chargebacked';
            }

            $displayData['order']['amount']     = isset($orderData['order']['amount']) ? number_format($orderData['order']['amount'], 2) : '0.00';
            $displayData['order']['status_des'] = empty($statusList) ? 'Approved' : implode('+', $statusList);

            // 订单地址
            $address                     = OrderAddress::with('orders')->where('id', $orderData['order']['address_id'])->first();
            $displayData['orderAddress'] = !empty($address) ? $address->toArray() : [];

            // 商品信息
            $product                     = OrderProduct::where('type', '0')->where('order_id', $orderData['order_id'])->get();
            $displayData['orderProduct'] = !empty($product) ? $product->toArray() : [];

            // 争议工单
            $complaint = OrderComplaint::with('order')->where('order_id', $orderData['order_id'])->first();

            if (!empty($complaint)) {
                $displayData['orderComplaint'] = $complaint->toArray();

                // 对话信息
                $displayData['messageList'] = json_decode($displayData['orderComplaint']['content'], true);
            }

            // 运单信息
            $deliveryTypeList = DirectoryCarrier::getTrackTypeList();
            $orderTrack       = OrderTrack::find($complaint['order_id']);

            if (!empty($orderTrack)) {
                $orderTrack->tracking_type    = $deliveryTypeList[$orderTrack->tracking_type] ?? '';
                $displayData['orderDelivery'] = $orderTrack->toArray();

                if (!empty($orderTrack->api_result_content)) {
                    $tempData = json_decode($orderTrack->api_result_content, true);

                    if (is_array($tempData)) {
                        foreach ($tempData as $list) {
                            $displayData['trackingList'][$list['tracking_number']] = array(
                                'original_country'       => isset($list['original_country']) ? $list['original_country'] : '-',
                                'original_event_list'    => isset($list['original_event_list']) ? $list['original_event_list'] : array(),
                                'destination_country'    => isset($list['destination_country']) ? $list['destination_country'] : '-',
                                'destination_event_list' => isset($list['destination_event_list']) ? $list['destination_event_list'] : array()
                            );
                        }
                    }
                }
            }
        }

        return $content->full()
            ->title($this->title)
            ->description($this->description['show'] ?? trans('admin.show'))
            ->body(view('custom.index', $displayData));
    }

    public function service()
    {
        // 验证
        request()->validate([
            'title'         => 'required',
            'email_address' => 'email|required',
        ]);

        $data = request()->toArray();

        // 数据校验
        $token   = !empty(request('token')) ? substr(request('token'), 1) : '';
        $title   = isset($data['title']) ? $data['title'] : 'Other';
        $email   = isset($data['email_address']) ? $data['email_address'] : '';
        $content = isset($data['text']) ? $data['text'] : '';

        if (empty($token)) {
            return ['error' => true, 'msg' => 'Illegal operation!'];
        }

        // 内容过滤
        $content = preg_replace('/<script[\s\S]*?<\/script>/i', '', $content);

        // 事务
        DB::beginTransaction();

        $dictionaryModel  = new DirectoryDictionary();
        $complaintTagList = $dictionaryModel->getTypeList('投诉标题')->toArray();
        $titleId          = array_search($title, $complaintTagList);

        $order = OrderRelation::with('order')->firstWhere('custom_token', $token);

        if (empty($order)) {
            return ['error' => true, 'msg' => 'Transaction details do not exist!'];
        }

        // 文件上传
        $fileList  = request()->file('imgUpload');
        $imageList = [];
        $uploader  = new ImageUploadHandler();

        if (!empty($fileList)) {
            foreach ($fileList as $key => $file) {
                $result = $uploader->save($file, 'custom_complaints');

                if ($result) {
                    $imageList[$key]['path'] = $result['path'];
                    $imageList[$key]['url']  = $result['url'];
                }
            }
        }

        if (strlen($content) < 1 && empty($imageList)) {
            return ['error' => true, 'msg' => 'Reply content can not be empty!'];
        }

        $orderData   = $order->toArray();
        $addressData = OrderAddress::with('orders')->select(['bill_name', 'bill_email'])->where('id', $orderData['order']['address_id'])->first()->toArray();

        // 争议工单
        $orderComplaint = OrderComplaint::with('order')->where('order_id', $orderData['order_id'])->first();
        $contentList    = !empty($orderComplaint) ? json_decode($orderComplaint->content, true) : [];

        if (strlen($content) > 0) {
            $tempData = [
                'type'       => 'cardholder',
                'content'    => array('text' => $content, 'pic' => array()),
                'device'     => '0',
                'ip'         => request()->getClientIp(),
                'by_added'   => $addressData['bill_name'],
                'date_added' => now()
            ];
            array_unshift($contentList, $tempData);
        } elseif (!empty($imageList)) {
            foreach ($imageList as $key => $list) {
                $tempData = [
                    'type'       => 'cardholder',
                    'content'    => array('text' => '', 'pic' => $list),
                    'device'     => '0',
                    'ip'         => request()->getClientIp(),
                    'by_added'   => $addressData['bill_name'],
                    'date_added' => now()
                ];
                array_unshift($contentList, $tempData);
            }
        }

        // 自动回复
        if (empty($orderComplaint)) {
            $content  = 'Dear customer, the seller has received your dispute and will respond to you ASAP';
            $tempData = array(
                'type'       => 'Platform',
                'content'    => array('text' => $content, 'pic' => array()),
                'device'     => '0',
                'ip'         => '127.0.0.1',
                'by_added'   => 'system',
                'date_added' => now()
            );
            array_unshift($contentList, $tempData);
        }

        // 系统用户
        $roleIds = DB::table('admin_roles')->select(['id'])->get()->pluck('id')->toArray();
        $roleStr = '';

        foreach ($roleIds as $id) {
            $roleStr .= '[' . $id . ']';
        }

        $contentList = clean($contentList, 'default');

        $data = [
            'order_id'      => $orderData['order']['order_id'],
            'merchant_id'   => $orderData['order']['merchant_id'],
            'merchant_name' => $orderData['order']['merchant_name'],
            'business_id'   => $orderData['order']['business_id'],
            'order_number'  => $orderData['order']['order_number'],
            'bill_name'     => $addressData['bill_name'],
            'bill_email'    => $addressData['bill_email'],
            'notice_email'  => $email,
            'd_title_id'    => $titleId,
            'content'       => json_encode($contentList),
            'read_ids'      => $roleStr,
            'is_read'       => '0',
            'is_reply'      => '0'
        ];

        if (empty($orderComplaint)) {
            $data['device']         = '0';
            $data['ip']             = request()->getClientIp();
            $data['confirmed_type'] = substr(request('token'), 0, 1);
        }

        try {
            OrderComplaint::updateOrCreate(['order_id' => $orderData['order_id']], $data);
        } catch (Exception $exception) {
            DB::rollBack();
            return ['error' => true, 'msg' => $exception->getMessage()];
        }

        DB::commit();

        return ['error' => false, 'msg' => 'Reply successful!', 'content' => $content];
    }
}
