<?php

namespace App\Merchant\Forms\User;

use App\Merchant\Repositories\User;
use App\Models\MerchantBusiness;
use App\Models\MerchantRoleBid;
use App\Models\User as ModelsUser;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class BidList extends Form implements LazyRenderable
{
    use LazyWidget;

    protected $data;

    public function handle(array $input): \Dcat\Admin\Http\JsonResponse
    {
        $userId      = $this->payload['user_id'] ?? '';
        $bid         = $input['bid'] ?? [];
        $user        = ModelsUser::query()->with('bid')->where('id', $userId)->first();
        $selectedBid = $user->bid ?? [];

        //需要添加的
        foreach ($bid as $value) {
            MerchantRoleBid::firstOrCreate([
                'user_id'     => $userId,
                'business_id' => $value
            ]);
        }

        //需要删除的
        $delId = [];
        foreach ($selectedBid as $value) {
            if (!in_array($value['business_id'], $bid)) {
                $delId[] = $value['id'];
            }
        }

        if (!empty($delId)) {
            MerchantRoleBid::destroy($delId);
        }

        return $this->response()->success('配置成功')->refresh();
    }

    public function form()
    {
        // 当前页面样式调整
        if (env('APP_NAME', 'Laravel') == 'PunctualPay') {
            Admin::style(
                <<<CSS
                    .flex-wrap {
                        background: #FFFFFF;
                        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.14);
                        padding: 20px;
                    }
                    hr {
                        display: none;
                    }
                CSS
            );
        }

        $merchantId = $this->payload['merchant_id'];
        $this->data = MerchantBusiness::where('merchant_id', $merchantId)->pluck('business_id', 'business_id');

        $userId = $this->payload['user_id'];
        $user   = ModelsUser::query()->with('bid')->where('id', $userId)->first();

        $bid = $user->bid ?? [];
        foreach ($bid as $key => $value) {
            $bid[$key] = $value['business_id'];
        }

        $this->checkbox('bid', trans('admin.bid'))
            ->options($this->data)->canCheckAll()->customFormat(function () use ($bid) {
                return $bid;
            });
    }
}
