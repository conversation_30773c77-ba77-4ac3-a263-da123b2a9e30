<?php

namespace App\Merchant\Actions\Grid\Cards;


use App\Services\VirtualControllerService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\Auth;
use App\Models\MerchantCard;


class TransferOut extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return \Dcat\Admin\Http\JsonResponse
     */
    public function handle(array $input)
    {
        $input['mid'] = Auth::user()->merchant_id;
        $resultData   = VirtualControllerService::cidAmountTransfer($input);
        if (!$resultData['isSuccess']) {
            return $this->response()->error($resultData['message'])->refresh();
        }

        return $this->response()->success($resultData['message'])->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        // 获取CID列表
        $cards       = MerchantCard::where('merchant_id', Auth::user()->merchant_id)->where('status', '1')->get();
        $cardsIdList = $cards->pluck('cards_id', 'cards_id')->toArray();
        $balanceList = $cards->pluck('balance', 'cards_id')->toArray();
        $cardsInput  = $this->select('cards_id', 'CID')->options($cardsIdList)->default(current($cardsIdList))->required();

        // 余额动态显示
        foreach ($balanceList as $cardsId => $balance) {
            $cardsInput->when($cardsId, function (Form $form) use ($balance) {
                $form->text('balance', admin_trans('ticket.labels.账户余额'))->default($balance)->readOnly();
            });
        }

        $this->text('amount', admin_trans('ticket.labels.转出') . admin_trans('global.fields.金额'))->default('0.00')->required();
    }
}
