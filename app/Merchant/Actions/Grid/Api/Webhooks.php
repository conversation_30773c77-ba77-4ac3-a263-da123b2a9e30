<?php

namespace App\Merchant\Actions\Grid\Api;

use App\Models\MerchantApiWebhook;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class Webhooks extends LazyRenderable
{
    public function grid(): Grid
    {
        Form::dialog('配置')
            ->click('.groups')
            ->width('70%')
            ->height('70%')
            ->success('Dcat.reload()')->forceRefresh();

        return Grid::make(new MerchantApiWebhook(), function (Grid $grid) {
            $grid->model()->where('merchant_id', '=', $this->payload['merchant_id'])->orderBy('sort', 'desc')->orderBy('id', 'desc');
            $grid->setResource("/api/{$this->payload['merchant_id']}/webhooks");
            $grid->column('name');
            $grid->column('url');
            $grid->column('sort');
            $grid->column('remarks');
            $grid->column('type')->display(function ($val) {
                return admin_trans('api.options.type_map.' . (MerchantApiWebhook::$internalTypeMap[$val]) ?? admin_trans_field('未知'));
            });
            $grid->column('status')->display(function ($val) {
                return admin_trans('api.options.status_map.' . (MerchantApiWebhook::$internalStartMap[$val]) ?? admin_trans_field('未知'));
            });


            $grid->created_at;
            $grid->updated_at;

            $grid->showCreateButton();
            $grid->showActions();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
        })->addTableClass('card_table');
    }
}
