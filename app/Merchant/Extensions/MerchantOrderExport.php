<?php

namespace App\Merchant\Extensions;

use App\Models\Order;
use App\Models\OrderRelation;
use App\Services\ExportService;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomChunkSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class MerchantOrderExport extends DefaultValueBinder implements WithCustomValueBinder, FromQuery, WithHeadings, WithCustomChunkSize, WithMapping, ShouldAutoSize
{
    use Exportable;

    protected $filter;
    protected $headings;
    protected $exportTitle;
    protected $lang;
    protected $query;

    public function __construct(array $filter = [], $query)
    {
        $this->exportTitle = $filter['export_title'] ?? [];
        $this->lang        = $filter['lang'] ?? 'zh_CN';
        $this->query       = $query;
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function query()
    {
        return $this->query;
    }

    public function bindValue(Cell $cell, $value)
    {
        $cell->setValueExplicit($value, DataType::TYPE_STRING);

        return true;
    }

    /**
     * Add conditions to grid model.
     *
     * @param Builder $query
     * @param array $conditions
     *
     * @return Builder
     */
    public function addConditions(Builder $query, array $conditions)
    {
        sort($conditions);

        foreach ($conditions as $condition) {
            call_user_func_array([$query, key($condition)], current($condition));
        }
        return $query;
    }

    public function map($row): array
    {
        return self::getMap($row, $this->lang);
    }

    public static function getMap($row, $lang): array
    {
        return [
            $row->order_id ?? '',
            Order::$typesMap[$row->type] ?? '未知',
            Order::$statusMap[$row->status] ?? '未知',
			OrderRelation::$isRefundMap[$row['relation']['is_refund']] ?? '未知',
			OrderRelation::$isChargebackMap[$row['relation']['is_chargeback']] ?? '未知',
            $row->merchant_id ?? '',
            $row->business_id ?? '',
            $row->merchant_name ?? '',
            $row->card->cc_type ?? '',
            $row->card->card_mask ?? '',
            $row->card->card_country ?? '',
            $row->url_name ?? '',
            $row->address->bill_name ?? '',
            $row->address->bill_email ?? '',
            $row->address->bill_country ?? '',
            $row->address->bill_state ?? '',
            $row->address->bill_city ?? '',
            $row->address->bill_address ?? '',
            $row->address->bill_postcode ?? '',
            $row->address->bill_phone ?? '',
            $row->address->ship_name ?? '',
            $row->address->ship_email ?? '',
            $row->address->ship_country ?? '',
            $row->address->ship_state ?? '',
            $row->address->ship_city ?? '',
            $row->address->ship_address ?? '',
            $row->address->ship_postcode ?? '',
            $row->address->ship_phone ?? '',
            $row->order_number ?? '',
            $row->currency ?? '',
            $row->amount ?? '',
            $row->code ?? '',
            $row->result ?? '',
            $row->remarks ?? '',
            $row->parent_order_id ?? '',
            ExportService::getFieldTranslation($row->is_3d ? '是' : '否', $lang),
            $row->track->tracking_type ?? '',
            $row->track->tracking_number ?? '',
            $row->expired_at ?? '',
            ExportService::getFieldTranslation($row->settlements ? ($row->settlements->is_settle ? '是' : '否') : '', $lang),
            ExportService::getFieldTranslation($row->settlements ? ($row->settlements->settle_at ?? '') : '', $lang),
            $row->created_at ?? '',
            $row->updated_at ?? '',
        ];
    }

    public function headings(): array
    {
        return $this->exportTitle;
    }
}
