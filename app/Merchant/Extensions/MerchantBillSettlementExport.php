<?php

namespace App\Merchant\Extensions;

use App\Merchant\Extensions\MerchantBillSettlement\BusinessSettleDetailsSheet;
use App\Merchant\Extensions\MerchantBillSettlement\MerchantSettleSummarySheet;
use App\Merchant\Extensions\MerchantBillSettlement\BusinessSettleDetailsTransferSheet;
use App\Merchant\Extensions\MerchantBillSettlement\MerchantSettleDetailsSheet;
use App\Merchant\Extensions\MerchantBillSettlement\SettleTotalSheet;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class MerchantBillSettlementExport implements WithMultipleSheets
{
    use Exportable;

    protected $filter;
    protected $inputs;
    protected $lang;

    public function __construct($params)
    {
        $this->filter = $params['filter'] ?? [];
        $this->inputs = $params['inputs'] ?? [];
        $this->lang   = $params['lang'] ?? [];
    }

    public function sheets(): array
    {
        $sheets[] = new SettleTotalSheet($this->inputs, $this->filter, $this->lang);
        $sheets[] = new MerchantSettleSummarySheet($this->inputs, $this->filter, $this->lang);
        $sheets[] = new BusinessSettleDetailsSheet($this->filter, $this->lang);
        $sheets[] = new BusinessSettleDetailsTransferSheet($this->filter, $this->lang);
        $sheets[] = new MerchantSettleDetailsSheet($this->filter, $this->lang);

        return $sheets;
    }
}
