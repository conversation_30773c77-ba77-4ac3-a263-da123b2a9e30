<?php

namespace App\Console\Commands;

use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\DirectoryCarrier;
use App\Models\OrderTrackTask;
use App\Services\TrackService;
use Illuminate\Console\Command;

class TrackTask extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:trackTask';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        set_time_limit(0);

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        // 运单任务
        $orderTrackTask = OrderTrackTask::where('status', OrderTrackTask::STATUS_WAITING)->get()->toArray();
        $taskList       = [];

        if (empty($orderTrackTask)) {
            $log->info('运单任务执行输出', ['没有需要执行的运单任务1']);
            dispatch(new SendSlsLog(
                ['message' => '运单任务执行输出, 没有需要执行的运单任务1'],
                [],
                'info',
                'task'
            ));
            
            return false;
        }

        foreach ($orderTrackTask as $value) {
            $taskList[$value['api_type']][$value['action']][] = $value;
        }

        $result = [];

        foreach ($taskList as $apiType => $typeList) {
            foreach ($typeList as $action => $actionList) {
                switch ($action) {
                    case OrderTrackTask::ACTION_ADD:
                        $result = TrackService::executeAddTrackTask($actionList, $apiType);
                    break;
                    case OrderTrackTask::ACTION_UPDATE:
                        $result = TrackService::executeUpdateTrackTask($actionList, $apiType);
                    break;
                }

                // 打印日志
                $log->info("运单任务执行输出", [sprintf('[%s][%s]运单成功[%s]条,失败[%s]条',
                    DirectoryCarrier::$typeMap[$apiType],
                    OrderTrackTask::$actionMap[$action],
                    isset($result['succes']) ? $result['succes'] : 0,
                    isset($result['failure']) ? $result['failure'] : 0
                )]);
                
                dispatch(new SendSlsLog(
                    ['message' => '运单任务执行输出'],
                    [sprintf('[%s][%s]运单成功[%s]条,失败[%s]条',
                        DirectoryCarrier::$typeMap[$apiType],
                        OrderTrackTask::$actionMap[$action],
                        isset($result['succes']) ? $result['succes'] : 0,
                        isset($result['failure']) ? $result['failure'] : 0
                    )],
                    'info',
                    'task'
                ));
            }
        }

    }
}
