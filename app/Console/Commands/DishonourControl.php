<?php

namespace App\Console\Commands;

use App\Classes\Pay\Gateways\Xborderpay\Support;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;
use App\Models\CardSource;
use App\Models\DirectoryDictionary;
use App\Models\DishonourControl as DishonourControlModel;
use App\Services\SwipeService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Console\Command;

class DishonourControl extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:dishonourControl';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dishonour Control';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
	    // 在命令行打印一行信息
	    $this->info("拒付调控任务开始");

	    // 初始化日志
	    $log    = new Logger();
	    $config = ['file' => storage_path('logs/consoleTask.log')];
	    $log->setConfig($config);

        $log->info("拒付调控任务开始");
		dispatch(new SendSlsLog(
			['message' => '拒付调控任务开始'],
			[],
			'info',
			'task'
		));

        // 获取调控任务
		$directoryDictionaryList = DirectoryDictionary::get()->where('type' , '拒付调控任务')->where('status', '1')->toArray();

		if (empty($directoryDictionaryList)) {
            $log->info('没有需要拒付调控的任务');
			dispatch(new SendSlsLog(
				['message' => '没有需要拒付调控的任务'],
				[],
				'info',
				'task'
			));
			
			return false;
		}

		// 获取调控子任务列表
	    $channelArr           = array_column($directoryDictionaryList, 'name', 'id');
	    $channelDictionaryIds = array_column($directoryDictionaryList, 'id');
	    $dishonourControlList = DishonourControlModel::get()->whereIn('dictionary_id', $channelDictionaryIds)->where('status', '1')->toArray();

	    if (empty($dishonourControlList)) {
            $log->info('没有需要拒付调控的子任务');
			dispatch(new SendSlsLog(
				['message' => '没有需要拒付调控的子任务'],
				[],
				'info',
				'task'
			));
			
		    return false;
	    }

	    // 获取可用的的信用卡类型
	    $cardTypeList = DirectoryDictionary::get()->where('type' , '信用卡类型')->where('status', '1')->toArray();

	    if (empty($cardTypeList)) {
            $log->info('没有可用的行用卡类型');
			dispatch(new SendSlsLog(
				['message' => '没有可用的行用卡类型'],
				[],
				'info',
				'task'
			));
			
		    return false;
	    }

	    // 获取可以使用卡台账
	    $cardTypeArr        = array_column($cardTypeList, 'name', 'id');
	    $dCardTypeIds       = array_column($cardTypeList, 'id');
	    $tempCardSourceList = CardSource::get()->whereIn('d_card_type_id', $dCardTypeIds)->where('status', CardSource::STATUS_ENABLE)->toArray();
	    $cardSourceData     = [];

	    // 筛选符合条件数的数据
	    if (!empty($tempCardSourceList)) {
		    foreach ($tempCardSourceList as $key => $value) {
			    if ($value['used_times'] < $value['limite_times']) {
				    $cardSourceData[$value['cc_type']][$value['d_card_type_id']][] = $tempCardSourceList[$key];
			    }
		    }
	    }

	    if (empty($cardSourceData)) {
            $log->info('没有可以使用的卡台账');
			dispatch(new SendSlsLog(
				['message' => '没有可以使用的卡台账'],
				[],
				'info',
				'task'
			));
			
		    return false;
	    }

	    sleep(rand(3, 10));

	    $currentDay  = date('Ymd');
	    $currentTime = date('H:i');

	    $swipeService = new SwipeService();

	    foreach ($dishonourControlList as $key => $value) {
		    $redisKey             = 'Dishonour_Control_' . $currentDay . '_' . $value['id'];
		    $cardTypeIdArr        = explode(',', $value['d_card_type_id']);
		    $canUseCardSourceList = array();

		    // 获取信用卡类型对应的卡台账
		    foreach ($cardTypeIdArr as $v) {
			    if (!in_array($v, $dCardTypeIds)) {
				    continue;
			    }

			    if (empty($cardSourceData[$value['cc_type']][$v])) {
                    $log->info(sprintf('%s卡,%s类型暂时没有可用的卡台账', $value['cc_type'], $cardTypeArr[$v]));
					dispatch(new SendSlsLog(
						['message' => sprintf('%s卡,%s类型暂时没有可用的卡台账', $value['cc_type'], $cardTypeArr[$v])],
						[],
						'info',
						'task'
					));
					
				    continue;
			    }

			    if (empty($canUseCardSourceList)) {
				    $canUseCardSourceList = $cardSourceData[$value['cc_type']][$v];
			    } else {
				    $canUseCardSourceList = array_merge($canUseCardSourceList, $cardSourceData[$value['cc_type']][$v]);
			    }
		    }

		    if (empty($canUseCardSourceList)) {
                $log->info(sprintf('账单标识%s,子任务%s卡,没有可用的卡台账', $channelArr[$value['dictionary_id']], $value['cc_type']));
				dispatch(new SendSlsLog(
					['message' => sprintf('账单标识%s,子任务%s卡,没有可用的卡台账', $channelArr[$value['dictionary_id']], $value['cc_type'])],
					[],
					'info',
					'task'
				));
				
			    continue;
		    }

		    // 随机取一条卡台账
		    $rand     = array_rand($canUseCardSourceList);
		    $sourceId = $canUseCardSourceList[$rand]['id'];

		    // 网址为账单标识名字
		    $url = $channelArr[$value['dictionary_id']];

		    // 金额
		    $amountDiffQty = floor(($value['amount_max'] - $value['amount_min']) / $value['amount_interval']);
		    $amountQty     = mt_rand(0, $amountDiffQty);
		    $amount        = Support::amount_format($value['amount_min'] + $amountQty * $value['amount_interval'], 2);

		    // 单笔调控周期时间(分钟)
		    $dayTotalMins = 60 * 24;
		    $cycleTime    = $dayTotalMins / ($value['transaction_times'] * 2);
		    $time         = 60 * 60 * 24;

		    if (Cache::has($redisKey)) {
			    $content     = Cache::get($redisKey);
			    $controlData = json_decode($content, true);

			    // 小于每日的刷卡次数 和 到达单笔刷卡周期时间
			    $swipeTime = strtotime(now()) - strtotime($controlData['last_time_swipe']);
			    if ($controlData['transaction_times'] < $value['transaction_times']
				    && $swipeTime >= ($cycleTime * 60)
				    && $controlData['day_swipe_complete'] == false
				    && $currentTime >= '00:00') {

			    	// 加载刷卡服务
				    $result = $swipeService->swipeCard($sourceId, $url, $amount, '', $value['product_name']);

				    if ($result['error']) {
                        $log->info(sprintf('账单标识%s,%s卡拒付调控失败,原因为%s', $channelArr[$value['dictionary_id']], $value['cc_type'], json_encode($result['msg'])));
						dispatch(new SendSlsLog(
							['message' => sprintf('账单标识%s,%s卡拒付调控失败,原因为%s', $channelArr[$value['dictionary_id']], $value['cc_type'], json_encode($result['msg'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE))],
							[],
							'info',
							'task'
						));

					    continue;
				    }

                    $log->info(sprintf('账单标识%s,%s卡拒付调控成功', $channelArr[$value['dictionary_id']], $value['cc_type']));
					dispatch(new SendSlsLog(
						['message' => sprintf('账单标识%s,%s卡拒付调控成功', $channelArr[$value['dictionary_id']], $value['cc_type'])],
						[],
						'info',
						'task'
					));

				    $content = array(
					    'transaction_times'  => $controlData['transaction_times'] + 1,
					    'last_time_swipe'    => now(),
					    'day_swipe_complete' => $controlData['day_swipe_complete'],
				    );

				    if ($content['transaction_times'] >= $value['transaction_times']) {
					    $content['day_swipe_complete'] = true;
				    }

				    Cache::put($redisKey, json_encode($content), $time);
			    }
		    } else {
			    if ($currentTime >= '00:00') {
				    // 加载刷卡服务
				    $result = $swipeService->swipeCard($sourceId, $url, $amount, '', $value['product_name']);

				    if ($result['error']) {
                        $log->info(sprintf('账单标识%s,%s卡拒付调控失败,原因为%s', $channelArr[$value['dictionary_id']], $value['cc_type'], json_encode($result['msg'])));
						dispatch(new SendSlsLog(
							['message' => sprintf('账单标识%s,%s卡拒付调控失败,原因为%s', $channelArr[$value['dictionary_id']], $value['cc_type'], json_encode($result['msg'], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE))],
							[],
							'info',
							'task'
						));
						
					    continue;
				    }

                    $log->info(sprintf('账单标识%s,%s卡拒付调控成功', $channelArr[$value['dictionary_id']], $value['cc_type']));
					dispatch(new SendSlsLog(
						['message' => sprintf('账单标识%s,%s卡拒付调控成功', $channelArr[$value['dictionary_id']], $value['cc_type'])],
						[],
						'info',
						'task'
					));

				    // 缓存到redis
				    $content = json_encode(
					    array(
						    'transaction_times'  => '1',
						    'last_time_swipe'    => now(),
						    'day_swipe_complete' => false,
					    )
				    );
				    Cache::add($redisKey, $content, $time);
			    }
		    }
	    }

	    $this->info("拒付调控任务结束");
    }
}
