<?php

namespace App\Console\Commands\Virtual;

use App\Classes\Supports\Log;
use App\Classes\Supports\Logger;
use App\Models\CardBin;
use App\Models\CardVirtual;
use App\Services\Virtual\VirtualServiceFacade;
use Illuminate\Console\Command;

class InquireCardTask extends Command
{
	/**
	 * The name and signature of the console command.
	 *
	 * @var string
	 */
	protected $signature = 'card:inquireCardTask';

	/**
	 * The console command description.
	 *
	 * @var string
	 */
	protected $description = '虚拟卡查询';

	/**
	 * Create a new command instance.
	 *
	 * @return void
	 */
	public function __construct()
	{
		parent::__construct();
		// 初始化日志
		$log    = new Logger();
		$config = ['file' => storage_path('logs/consoleCardTask.log')];
		$log->setConfig($config);
		Log::setInstance($log);
	}

	public function handle()
	{
		$this->info("查询虚拟卡开始");
		$cardBinMap = [];
		CardVirtual::where('status', '=', CardVirtual::PROGRESS)
			->chunkById(200, function ($cards) use (&$cardBinMap) {
				$cardsTransIds = [];
				foreach ($cards as $value) {
					if (!isset($cardBinMap[$value->card_bin_id])) {
						$cardBin = CardBin::where('id', $value->card_bin_id)->with('CardBinSupplier')->first()->toArray();
						// 缓存bin信息
						$cardBinMap[$value->card_bin_id] = $cardBin;
					}
					$cardsTransIds[$value->card_bin_id][] = $value->trans_id;
				}

				foreach ($cardsTransIds as $binId => $ids) {
					VirtualServiceFacade::getService($cardBinMap[$binId]['card_bin_supplier']['file_name'])->setGatewayConfig($cardBinMap[$binId]['config'])->batchGetCardInfo($ids);
				}
			});

		$this->info('查询虚拟卡结束');
	}
}
