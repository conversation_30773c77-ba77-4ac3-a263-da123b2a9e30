<?php

namespace App\Console\Commands;

use App\Models\Channel;
use App\Models\ChannelWithdrawal;
use Illuminate\Console\Command;
use App\Classes\Pay\Pay;
use App\Classes\Supports\Logger;
use App\Jobs\SendSlsLog;

class WithdrawalQuery extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'task:withdrawalQuery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Channel Withdrawal Query';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 在命令行打印一行信息
        $this->info("提现订单检索开始");

        // 初始化日志
        $log    = new Logger();
        $config = ['file' => storage_path('logs/consoleTask.log')];
        $log->setConfig($config);

        //获取要检索的提现订单
        $withdrawalData = ChannelWithdrawal::where('status', ChannelWithdrawal::STATUS_PENDING)->where('created_at', '<=', date('Y-m-d H:i:s', strtotime('-30 minutes')))->get();

        $success = 0; //计数
        $outPut  = [];

        if (!empty($withdrawalData)) {
            //获取要检索的Pid
            $pidArr = array_column($withdrawalData->toArray(), 'channel_pid_id');

            //获取账单标识信息
            $channelData = Channel::with('channelSupplier')->whereIn('channel_pid_id', $pidArr)->where('status', 1)
                ->get()->keyBy('channel_pid_id');

            $channelSupplierData = [];
            $tempChannelData     = [];
            $retrieveList        = [];

            foreach ($channelData as $value) {
                $channelSupplierData[$value->channel_pid_id] = strtolower($value->channelSupplier->file_name);
                $tempChannelData[$value->channel_pid_id]     = $value->config;
            }

            //组装检索数据
            foreach ($withdrawalData as $vo) {
                $retrieveList[$channelSupplierData[$vo->channel_pid_id]][] = $vo;
            }

            //加载渠道数据
            foreach ($retrieveList as $supplierName => $orderData) {
                $channelSuccess = 0; //渠道计数
                foreach ($orderData as $order) {
                    $config      = $this->_getConfig($tempChannelData[$order->channel_pid_id], $supplierName);
                    $channelName = Pay::$supplierName($config);
                    $result      = $channelName->withdrawal_query($order->toArray());
                    $outStr      = "[订单检索API请求情况] : " . json_encode($result);
                    $this->info($outStr);

                    if (!empty($result)) {
                        if ($order->status != $result['status']) {
                            $order->update($result->toArray());
                            $success++;
                            $channelSuccess++;
                        }
                    }
                }

                $msgStr = sprintf('%s渠道统计,检索总计:%s条,检索成功:%s条,检索失败:%s条', $supplierName, count($orderData), $channelSuccess, count($orderData) - $channelSuccess);
                $this->info($msgStr);
                $outPut[] = $msgStr;
            }
        }

        $outPut[] = sprintf("向渠道检索提现订单总计:%s条,订单检索成功:%s条,订单检索失败:%s条", count($withdrawalData), $success, count($withdrawalData) - $success);
        $log->info("异步通知执行输出", [$outPut]);
        dispatch(new SendSlsLog(
            ['message' => '异步通知执行输出'],
            [$outPut],
            'info',
            'task'
        ));
        $this->info("提现订单检索结束");
    }

    protected function _getConfig($configData, $channelSupplierName)
    {
        $config = [];

        // 循环组装数据
        foreach ($configData as $value) {
            $config[$value['key']] = $value['value'];
        }

        // 增加日志file
        if (!empty($config)) {
            $config['log'] = ['file' => storage_path('logs/' . $channelSupplierName . '.log')];
        }

        return $config;
    }
}
