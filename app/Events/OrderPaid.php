<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderPaid
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $order;
    protected $returnData;
    protected $orderType = 'transaction';

    /**
     * Create a new event instance.
     *
     * @param Order $order
     */
    public function __construct(Order $order, $returnData)
    {
	    $this->order      = $order;
	    $this->returnData = $returnData;
    }

    public function getOrder()
    {
        return $this->order;
    }

	public function getReturnData()
	{
		return $this->returnData;
	}

	public function getOrderType()
    {
        return $this->orderType;
    }
}
