<?php

namespace App\Events;

use App\Models\LocalRefund;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LocalRefundPaid
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    protected $refund;
    protected $returnData;
    protected $field = 'is_refund';
    protected $orderType = 'refund';

    /**
     * Create a new event instance.
     *
     * @param LocalRefund $refund
     */
    public function __construct(LocalRefund $refund, $returnData)
    {
        $this->refund     = $refund;
        $this->returnData = $returnData;
    }

    public function getOrder()
    {
        return $this->refund;
    }
    
    public function getReturnData()
	{
		return $this->returnData;
	}

    public function getField()
    {
        return $this->field;
    }

    public function getOrderType()
    {
        return $this->orderType;
    }
}
