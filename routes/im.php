<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// OpenIm
Route::name('open_im')->group(function () {
    Route::post('{p}/account/login', 'Api\OpenImController@login');
    Route::post('{p}/friend/add_friend', 'Api\OpenImController@addFriend');
    Route::post('{p}/group/create_group', 'Api\OpenImController@createGroup');
//    Route::post('{p}/group/join_group', 'OpenImController@addGroupMember');
    Route::any('{p}/{url}', 'Api\OpenImController@transferMethod')->where('url', '.*');
});
