/* jshint esversion: 6 */

// UBO自动复制到Director功能
function copyUboToDirector(uboForm) {
    // 收集UBO数据
    const uboData = {};
    const fields = ['cert_type', 'nationality', 'name', 'share_ratio', 'birth_date', 'address', 'cert_file', 'cert_number', 'residence_doc', 'is_signatory', 'hold_cert_photo'];

    fields.forEach(function (field) {
        let element = uboForm.find('[name*="[' + field + ']"]');
        if (element.length > 0) {
            if (element.length > 1){
                element = element.last();
            }

            if (element.is('select')) {
                uboData[field] = element.val();
            } else if (element.is('input[type="radio"]:checked')) {
                uboData[field] = element.val();
            } else if (element.is('input, textarea')) {
                uboData[field] = element.val();
            }
        }
    });
    // 查找Director区域的添加按钮
    const directorFieldset = $('.has-many-director');

    if (directorFieldset.length > 0) {
        const addButton = directorFieldset.find('.director-add').first();
        if (addButton.length > 0) {
            addButton.click();

            // 等待新表单创建后填充数据
            setTimeout(function () {
                const newDirectorForm = directorFieldset.find('.has-many-director-form.fields-group').last();
                if (newDirectorForm.length > 0) {
                    // 填充数据
                    fields.forEach(function (field) {
                        if (uboData[field]) {
                            const targetElement = newDirectorForm.find('[name*="[' + field + ']"]');
                            if (targetElement.length > 0) {
                                if (targetElement.is('select')) {
                                    targetElement.val(uboData[field]).trigger('change');
                                } else if (targetElement.is('input[type="radio"]')) {
                                    targetElement.filter('[value="' + uboData[field] + '"]').prop('checked', true).trigger('change');
                                } else {
                                    targetElement.val(uboData[field]).trigger('change');
                                }
                            }
                        }
                    });

                    // 设置is_listed_in_nar1为true
                    const nar1Element = newDirectorForm.find('[name*="[is_listed_in_nar1]"]');
                    nar1Element.filter('[value="1"]').prop('checked', true).trigger('change');

                    Dcat.swal.success('Success!');
                }
            }, 500);
        }
    }
}

function copyUboInit(text) {
    // 使用事件委托监听所有UBO的is_listed_in_nar1字段
    $(document).on('change', 'input[name*="ubo"][name*="[is_listed_in_nar1]"]', function () {
        if ($(this).val() === '1') {
            const uboForm = $(this).closest('.has-many-ubo-form.fields-group');

            Dcat.swal.confirm(
                'UBO Copy',
                text,
                function () {
                    copyUboToDirector(uboForm);
                }
            );
        }
    });
}