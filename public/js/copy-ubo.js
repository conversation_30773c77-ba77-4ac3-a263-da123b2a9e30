/* jshint esversion: 6 */

// UBO自动复制到Director功能
function copyUboToDirector(uboForm) {
    // 收集UBO数据
    const uboData = {};
    const fields = ['cert_type', 'nationality', 'name', 'share_ratio', 'birth_date', 'address', 'cert_file', 'cert_number', 'residence_doc', 'is_signatory', 'hold_cert_photo'];
    const fileFields = ['cert_file', 'residence_doc', 'hold_cert_photo']; // 文件字段列表

    // 调试：显示源表单的文件字段信息
    debugFileFields(uboForm, 'UBO源表单');

    fields.forEach(function (field) {
        let element = uboForm.find('[name*="[' + field + ']"]');
        if (element.length > 0) {
            if (element.length > 1){
                element = element.last();
            }

            if (fileFields.includes(field)) {
                // 处理文件字段
                uboData[field] = getFileFieldData(uboForm, field);
            } else if (element.is('select')) {
                uboData[field] = element.val();
            } else if (element.is('input[type="radio"]:checked')) {
                uboData[field] = element.val();
            } else if (element.is('input, textarea')) {
                uboData[field] = element.val();
            }
        }
    });
    // 查找Director区域的添加按钮
    const directorFieldset = $('.has-many-director');

    if (directorFieldset.length > 0) {
        const addButton = directorFieldset.find('.director-add').first();
        if (addButton.length > 0) {
            addButton.click();

            // 等待新表单创建后填充数据
            setTimeout(function () {
                const newDirectorForm = directorFieldset.find('.has-many-director-form.fields-group').last();
                if (newDirectorForm.length > 0) {
                    // 调试：显示目标表单的文件字段信息
                    debugFileFields(newDirectorForm, 'Director目标表单');

                    // 填充数据
                    fields.forEach(function (field) {
                        if (uboData[field]) {
                            const targetElement = newDirectorForm.find('[name*="[' + field + ']"]');
                            if (targetElement.length > 0) {
                                if (fileFields.includes(field)) {
                                    // 处理文件字段复制
                                    setFileFieldData(newDirectorForm, field, uboData[field]);
                                } else if (targetElement.is('select')) {
                                    targetElement.val(uboData[field]).trigger('change');
                                } else if (targetElement.is('input[type="radio"]')) {
                                    targetElement.filter('[value="' + uboData[field] + '"]').prop('checked', true).trigger('change');
                                } else {
                                    targetElement.val(uboData[field]).trigger('change');
                                }
                            }
                        }
                    });

                    // 设置is_listed_in_nar1为true
                    const nar1Element = newDirectorForm.find('[name*="[is_listed_in_nar1]"]');
                    nar1Element.filter('[value="1"]').prop('checked', true).trigger('change');

                    // 统计复制结果
                    const copiedFiles = fileFields.filter(field => uboData[field] && uboData[field].fileIds).length;
                    const copiedFields = fields.filter(field => uboData[field]).length;

                    let message = 'UBO数据复制成功！';
                    if (copiedFiles > 0) {
                        message += `\n已复制 ${copiedFiles} 个文件字段，${copiedFields - copiedFiles} 个普通字段。`;
                    } else {
                        message += `\n已复制 ${copiedFields} 个字段。`;
                    }

                    Dcat.swal.success(message);
                }
            }, 500);
        }
    }
}

/**
 * 获取文件字段数据
 * @param {jQuery} form - 表单对象
 * @param {string} fieldName - 字段名称
 * @returns {Object|null} 文件数据对象
 */
function getFileFieldData(form, fieldName) {
    try {
        // 多种方式查找文件上传组件的隐藏input字段
        let hiddenInput = form.find('input[name*="[' + fieldName + ']"][type="hidden"]');

        // 如果没找到，尝试其他可能的选择器
        if (hiddenInput.length === 0) {
            hiddenInput = form.find('input[name$="[' + fieldName + ']"][type="hidden"]');
        }
        if (hiddenInput.length === 0) {
            hiddenInput = form.find('input[name*="' + fieldName + '"][type="hidden"]');
        }

        if (hiddenInput.length === 0) {
            console.log('未找到文件字段的隐藏input:', fieldName);
            return null;
        }

        const fileIds = hiddenInput.val();
        if (!fileIds) {
            console.log('文件字段值为空:', fieldName);
            return null;
        }

        // 多种方式查找文件上传组件的容器
        let fileContainer = hiddenInput.closest('.form-field, .form-group').find('.web-uploader');
        if (fileContainer.length === 0) {
            fileContainer = hiddenInput.siblings('.web-uploader');
        }
        if (fileContainer.length === 0) {
            fileContainer = hiddenInput.parent().find('.web-uploader');
        }

        if (fileContainer.length === 0) {
            console.log('未找到文件上传容器:', fieldName);
            return null;
        }

        // 获取已上传文件的信息
        const uploadedFiles = [];
        fileContainer.find('.filelist li').each(function() {
            const $li = $(this);
            const title = $li.attr('title') || '';
            const imgSrc = $li.find('img').attr('src') || '';
            const fileName = $li.find('.title').first().text().trim();

            // 获取文件ID（从data属性或其他地方）
            const fileId = $li.find('[data-file-act="delete"], [data-file-act="deleteurl"]').data('id') || '';

            if (title || imgSrc || fileName) {
                uploadedFiles.push({
                    title: title,
                    imgSrc: imgSrc,
                    fileName: fileName,
                    fileId: fileId,
                    html: $li.prop('outerHTML')
                });
            }
        });

        console.log('成功获取文件字段数据:', fieldName, {fileIds: fileIds, filesCount: uploadedFiles.length});

        return {
            fileIds: fileIds,
            uploadedFiles: uploadedFiles,
            fieldName: fieldName
        };
    } catch (error) {
        console.error('获取文件字段数据失败:', fieldName, error);
        return null;
    }
}

/**
 * 设置文件字段数据
 * @param {jQuery} form - 目标表单对象
 * @param {string} fieldName - 字段名称
 * @param {Object} fileData - 文件数据对象
 */
function setFileFieldData(form, fieldName, fileData) {
    try {
        if (!fileData || !fileData.fileIds) {
            console.log('文件数据为空，跳过设置:', fieldName);
            return;
        }

        // 多种方式查找目标文件上传组件的隐藏input字段
        let targetHiddenInput = form.find('input[name*="[' + fieldName + ']"][type="hidden"]');
        if (targetHiddenInput.length === 0) {
            targetHiddenInput = form.find('input[name$="[' + fieldName + ']"][type="hidden"]');
        }
        if (targetHiddenInput.length === 0) {
            targetHiddenInput = form.find('input[name*="' + fieldName + '"][type="hidden"]');
        }

        if (targetHiddenInput.length === 0) {
            console.log('未找到目标文件字段的隐藏input:', fieldName);
            return;
        }

        // 设置文件ID值
        targetHiddenInput.val(fileData.fileIds).trigger('change');
        console.log('设置文件ID值:', fieldName, fileData.fileIds);

        // 多种方式查找目标文件上传组件的容器
        let targetFileContainer = targetHiddenInput.closest('.form-field, .form-group').find('.web-uploader');
        if (targetFileContainer.length === 0) {
            targetFileContainer = targetHiddenInput.siblings('.web-uploader');
        }
        if (targetFileContainer.length === 0) {
            targetFileContainer = targetHiddenInput.parent().find('.web-uploader');
        }

        if (targetFileContainer.length === 0) {
            console.log('未找到目标文件上传容器:', fieldName);
            return;
        }

        // 更新文件列表显示
        const targetFileList = targetFileContainer.find('.filelist');
        if (targetFileList.length > 0 && fileData.uploadedFiles.length > 0) {
            // 清空现有文件列表
            targetFileList.empty();

            // 添加复制的文件项
            fileData.uploadedFiles.forEach(function(file, index) {
                if (file.html) {
                    const $newFileItem = $(file.html);

                    // 更新删除按钮的事件处理
                    $newFileItem.find('[data-file-act="delete"], [data-file-act="deleteurl"]').off('click').on('click', function() {
                        const fileId = $(this).data('id') || file.fileId;
                        if (fileId) {
                            // 从隐藏字段中移除文件ID
                            const currentIds = targetHiddenInput.val().split(',').filter(id => id.trim());
                            const newIds = currentIds.filter(id => id !== fileId);
                            targetHiddenInput.val(newIds.join(',')).trigger('change');
                        }
                        $newFileItem.remove();

                        // 如果没有文件了，显示占位符
                        if (targetFileList.children().length === 0) {
                            targetFileContainer.find('.placeholder').removeClass('element-invisible');
                            targetFileContainer.find('.statusBar').addClass('element-invisible');
                        }
                    });

                    // 更新预览按钮的事件处理
                    $newFileItem.find('[data-file-act="preview"]').off('click').on('click', function() {
                        const imgSrc = file.imgSrc || $(this).data('url') || $newFileItem.find('img').attr('src');
                        if (imgSrc && typeof Dcat !== 'undefined' && Dcat.helpers && Dcat.helpers.previewImage) {
                            Dcat.helpers.previewImage(imgSrc, null, file.fileName);
                        }
                    });

                    targetFileList.append($newFileItem);
                }
            });

            // 隐藏占位符，显示文件列表
            targetFileContainer.find('.placeholder').addClass('element-invisible');
            targetFileContainer.find('.statusBar').removeClass('element-invisible');

            console.log('成功设置文件字段显示:', fieldName, fileData.uploadedFiles.length + '个文件');
        }

    } catch (error) {
        console.error('设置文件字段数据失败:', fieldName, error);
    }
}

/**
 * 调试函数：显示表单中的文件字段信息
 * @param {jQuery} form - 表单对象
 * @param {string} prefix - 日志前缀
 */
function debugFileFields(form, prefix) {
    if (typeof console === 'undefined') return;

    console.group(prefix + ' - 文件字段调试信息');

    const fileFields = ['cert_file', 'residence_doc', 'hold_cert_photo'];
    fileFields.forEach(function(fieldName) {
        console.log('=== ' + fieldName + ' ===');

        // 查找隐藏input
        const hiddenInputs = form.find('input[name*="' + fieldName + '"][type="hidden"]');
        console.log('隐藏input数量:', hiddenInputs.length);
        hiddenInputs.each(function(i) {
            console.log('  隐藏input[' + i + ']:', $(this).attr('name'), '值:', $(this).val());
        });

        // 查找文件容器
        const containers = form.find('.web-uploader');
        console.log('文件容器数量:', containers.length);

        // 查找文件列表
        const fileLists = form.find('.filelist li');
        console.log('文件列表项数量:', fileLists.length);
        fileLists.each(function(i) {
            const $li = $(this);
            console.log('  文件项[' + i + ']:', {
                title: $li.attr('title'),
                fileName: $li.find('.title').first().text().trim(),
                hasImg: $li.find('img').length > 0,
                imgSrc: $li.find('img').attr('src')
            });
        });
    });

    console.groupEnd();
}

function copyUboInit(text) {
    // 使用事件委托监听所有UBO的is_listed_in_nar1字段
    $(document).on('change', 'input[name*="ubo"][name*="[is_listed_in_nar1]"]', function () {
        if ($(this).val() === '1') {
            const uboForm = $(this).closest('.has-many-ubo-form.fields-group');

            Dcat.swal.confirm(
                'UBO Copy',
                text,
                function () {
                    copyUboToDirector(uboForm);
                }
            );
        }
    });
}