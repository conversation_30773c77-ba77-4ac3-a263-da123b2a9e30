<?php

use Illuminate\Database\Migrations\Migration;

class DataCardInfoManagerToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
	    if (!is_local()) { // 非本地执行
	    	// 卡库管理
		    $card = Dcat\Admin\Models\Menu::all()->where('parent_id', '0')->where('title', '卡库管理')->first();

		    Dcat\Admin\Models\Menu::insert(
			    [
				    [
					    "parent_id"  => $card->id,
					    "order"      => 2,
					    "title"      => "卡信息管理",
					    "icon"       => NULL,
					    "uri"        => "/card_info",
					    "created_at" => now(),
					    "updated_at" => now()
				    ]
			    ]
		    );
	    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
