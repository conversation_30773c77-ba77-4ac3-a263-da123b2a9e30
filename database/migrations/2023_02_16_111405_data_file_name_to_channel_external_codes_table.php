<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\ChannelSupplier;
use App\Models\ChannelExternalCode;

class DataFileNameToChannelExternalCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        $externalCode = ChannelExternalCode::select('id', 'channel_supplier_id')->get();
        $suppliers    = ChannelSupplier::pluck('file_name', 'id')->toArray();

        $codeNameMapping = [];
        foreach ($externalCode as $code) {
            if (isset($suppliers[$code->channel_supplier_id])) {
                $codeNameMapping[$suppliers[$code->channel_supplier_id]][] = $code->id;
            } else {
                $codeNameMapping['error'][] = $code->id;
            }
        }

        foreach ($codeNameMapping as $fileName => $codes) {
            ChannelExternalCode::whereIn('id', $codes)->update(['file_name' => $fileName]);
        }
    }
}
