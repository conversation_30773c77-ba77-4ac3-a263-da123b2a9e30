<?php

use Illuminate\Database\Migrations\Migration;

class DataCardCountryAndCardLevelToOrderCardsTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		$orderCardList = \App\Models\OrderCard::all();

		if ($orderCardList) {
			foreach ($orderCardList as $orderCard) {
				$cardBin          = substr($orderCard->card_mask, 0, 6);
				$directoryBinbase = \App\Models\DirectoryBinbase::where('bin', $cardBin)->select(['isoa2', 'level'])->first();
				$isoa2            = '';
				$cardLevel        = '-';

				if ($directoryBinbase) {
					$isoa2     = $directoryBinbase->isoa2;
					$cardLevel = $directoryBinbase->level;
				} else {
					$order = \App\Models\Order::where('card_id', $orderCard->id)->select('address_id')->first();
					$ip    = \App\Models\OrderAddress::where('id', $order->address_id)->value('ip');

					if ($ipData = json_decode(file_get_contents('http://*************/DbipService.php?ip=' . $ip), true)) {
						$isoa2 = $ipData['country']['iso_code'] ?? '';
					}
				}

				$orderCard->card_country = '';
				$orderCard->card_level   = $cardLevel;

				if ($isoa2) {
					$orderCard->card_country = \App\Models\DirectoryCountry::getCountryByIsoa2($isoa2);
				}

				$orderCard->save();
			}
		}
	}
}