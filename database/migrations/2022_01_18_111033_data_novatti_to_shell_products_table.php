<?php

use Illuminate\Database\Migrations\Migration;

class DataNovattiToShellProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) {
            $products = [
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different Heart Shaped Ceramic Christmas Ornaments',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8282',
                    'sku'   => '8282'
                ],
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different T-shirt Shaped Ceramic Christmas Ornaments',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8279',
                    'sku'   => '8279'
                ],
                [
                    'name'  => '[Made in USA] Custom Round Pet Tag Dog Tag & Cat Tag',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8229',
                    'sku'   => '8229'
                ],
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different Oval Ceramic Christmas Ornaments',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8283',
                    'sku'   => '8283'
                ],
                [
                    'name'  => '[Made in USA] Custom Love Heart Pet Tag Dog Tag & Cat Tag',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8062',
                    'sku'   => '8062'
                ],
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different Rounded Star Ceramic Christmas Ornaments',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8284',
                    'sku'   => '8284'
                ],
                [
                    'name'  => '[Made in USA] Custom Sign Aluminum Plate 3" x 12"',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7473',
                    'sku'   => '7473'
                ],
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different Rounded 6-pointed Star Ceramic Christmas Ornaments',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8281',
                    'sku'   => '8281'
                ],
                [
                    'name'  => '[Made in USA] Custom 2 Sides Different Round Ceramic Christmas Ornament',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8301',
                    'sku'   => '8301'
                ],
                [
                    'name'  => '[Made in USA] Custom Military Dog Tag Pet Tag Cat Tag',
                    'price' => '4.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8232',
                    'sku'   => '8232'
                ],
                [
                    'name'  => '[Made in USA] Horizontal Custom Sign 14" x 10" Wooden Plaque with Rope',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8342',
                    'sku'   => '8342'
                ],
                [
                    'name'  => '"Flower" Custom Black Straps Adjustable Face Cover with 2 Filters Non-medical',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7212',
                    'sku'   => '7212'
                ],
                [
                    'name'  => '[Made in USA] Custom Sign Wooden Plaque 10" x 14"',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8073',
                    'sku'   => '8073'
                ],
                [
                    'name'  => 'Custom Face Cover Adjustable Nose Clip Face Cover without Filter Non-medical',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7647',
                    'sku'   => '7647'
                ],
                [
                    'name'  => 'Samsung Galaxy A21 S Custom Phone Case Soft TPU Phone Case',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8175',
                    'sku'   => '8175'
                ],
                [
                    'name'  => '[Made in USA] Vertical Custom Sign 12" x 16" Metal Sign Aluminum Plate',
                    'price' => '7.49',
                    'url'   => 'https;//www.thisnew.com/customization/detail/2/8330',
                    'sku'   => '8330'
                ],
                [
                    'name'  => 'Custom Adjustable Face Cover with 2 Filters Non Medical',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7333',
                    'sku'   => '7333'
                ],
                [
                    'name'  => '[Made in USA] Horizontal Custom Sign 14" x 10" Wooden Plaque with Hanging Hook',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8276',
                    'sku'   => '8276'
                ],
                [
                    'name'  => '[Made in USA] "NOTICE" Custom Sign Horizontal Aluminum Plate 16" x 12"',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7167',
                    'sku'   => '7167'
                ],
                [
                    'name'  => '"Leopard print" Custom Adjustable Face Cover with Two Filters Non-medical',
                    'price' => '7.49',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7479',
                    'sku'   => '7479'
                ],
                [
                    'name'  => 'Custom Round Glass Ashtray with 3 Slots Outside Bottom Print',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7891',
                    'sku'   => '7891'
                ],
                [
                    'name'  => 'Custom Anti-Theft License Plate Frame 2 Holes',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/5277',
                    'sku'   => '5277'
                ],
                [
                    'name'  => 'Custom Face Cover Earhook Riding Face Cover with Breathing Valve Non Medical',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7372',
                    'sku'   => '7372'
                ],
                [
                    'name'  => 'Custom Photo Paper Poster 16" x 24"',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7521',
                    'sku'   => '7521'
                ],
                [
                    'name'  => 'Customizable Cotton and Linen Tote Bags 15" × 16.5" 1 Side Printing',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/426',
                    'sku'   => '426'
                ],
                [
                    'name'  => 'Customizable Short Plush Pillowcase 24" x 24" 2 Sides Different Designs',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7449',
                    'sku'   => '7449'
                ],
                [
                    'name'  => 'Custom Sign Wooden Plaque 8" x 8"',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7584',
                    'sku'   => '7584'
                ],
                [
                    'name'  => 'Custom Bag White Hemp Drawstring Backpack Gym Bag 12" x 8"',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7628',
                    'sku'   => '7628'
                ],
                [
                    'name'  => 'Custom Women`s All Over Print Yoga Shorts',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8033',
                    'sku'   => '8033'
                ],
                [
                    'name'  => 'Custom Hats All Over Print Bucket Hat',
                    'price' => '9.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/2016',
                    'sku'   => '2016'
                ],
                [
                    'name'  => 'Custom Sign Aluminum Plate 16" x 12"',
                    'price' => '15.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7459',
                    'sku'   => '7459'
                ],
                [
                    'name'  => 'Horizontal Custom Sign 14" x 10" Wooden Plaque with Hanging Hook',
                    'price' => '15.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8297',
                    'sku'   => '8297'
                ],
                [
                    'name'  => 'Custom 3 Pieces Coral Velvet Floor Mat Set 19.7" x 31.5" x 0.3"/17.7" x 13.8" x 0.3"/19.7" x 15.7" x 0.3"',
                    'price' => '15.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/618',
                    'sku'   => '618'
                ],
                [
                    'name'  => "Customizable Women's All Over Print High Waisted Yoga Leggings",
                    'price' => '15.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/5707',
                    'sku'   => '5707'
                ],
                [
                    'name'  => 'Vertical Custom Sign 12" x 16" Metal Sign Aluminum Plate',
                    'price' => '15.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8331',
                    'sku'   => '8331'
                ],
                [
                    'name'  => 'Custom Christmas 2 Transparent Ball Ornaments & 2 Ceramic Ornaments Set',
                    'price' => '19.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8496',
                    'sku'   => '8496'
                ],
                [
                    'name'  => 'Custom Paintings Wooden Inner Framed Canvas Painting 16" x 24"',
                    'price' => '19.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7537',
                    'sku'   => '7537'
                ],
                [
                    'name'  => 'Custom White Lockable PU Cuboid Jewelry Box Gift Box',
                    'price' => '19.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8268',
                    'sku'   => '8268'
                ],
                [
                    'name'  => 'Wedding Balloon Bunches Green & Gold Foil Balloon Party Balloons Set',
                    'price' => '19.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8358',
                    'sku'   => '8358'
                ],
                [
                    'name'  => 'Custom Jewelry Sun Flower Pendant Photo Projection Necklace Nano Engraving',
                    'price' => '19.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8249',
                    'sku'   => '8249'
                ],
                [
                    'name'  => 'Custom Shoes Unisex Slip On Shoes',
                    'price' => '31.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7071',
                    'sku'   => '7071'
                ],
                [
                    'name'  => '[Mirror Effect] Custom Shoes Unisex Slip On Leisure Shoes',
                    'price' => '31.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7135',
                    'sku'   => '7135'
                ],
                [
                    'name'  => 'Custom Shoes Unisex Low Top Canvas Shoes',
                    'price' => '31.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7986',
                    'sku'   => '7986'
                ],
                [
                    'name'  => 'Custom Shoes Unisex High Top Sneakers',
                    'price' => '31.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7061',
                    'sku'   => '7061'
                ],
                [
                    'name'  => '[Mirror Effect] Custom Shoes Unisex Slip On Canvas Shoes',
                    'price' => '31.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/5001',
                    'sku'   => '5001'
                ],
                [
                    'name'  => "Custom Suits Men's Nylon Sports Suit Hoodie & Pants Set Offset Heat Transfer print",
                    'price' => '36.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8016',
                    'sku'   => '8016'
                ],
                [
                    'name'  => "Christmas Tree Decor 48' Fringed Tree Skirt & Ceramic Ornaments Set",
                    'price' => '36.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8500',
                    'sku'   => '8500'
                ],
                [
                    'name'  => 'Custom Poster Self Adhesive PP Poster 118" x 94"',
                    'price' => '36.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7612',
                    'sku'   => '7612'
                ],
                [
                    'name'  => 'Custom Shoes Unisex Mid Top Breathable Lace-up Sneakers',
                    'price' => '36.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8000',
                    'sku'   => '8000'
                ],
                [
                    'name'  => 'Custom Paintings Wooden Inner Framed Canvas Painting 20" x 28"',
                    'price' => '36.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7540',
                    'sku'   => '7540'
                ],
                [
                    'name'  => 'Custom 3 Pieces King Size Bedding Cover Set 79" x 79" Pillowcases & Quilt Cover Can be Designed Separately',
                    'price' => '41.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7597',
                    'sku'   => '7597'
                ],
                [
                    'name'  => 'Custom 3 Pieces Queen Size Bedding Cover Set 67" x 79" Pillowcases & Quilt Cover Can be Designed Separately',
                    'price' => '41.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/7599',
                    'sku'   => '7599'
                ],
                [
                    'name'  => 'Custom 3 Pieces King Size Bedding Cover Set 79" x 79" Quilt Cover & Each Pillowcase Can be Designed Separately',
                    'price' => '41.99',
                    'url'   => 'https://www.thisnew.com/customization/detail/2/8030',
                    'sku'   => '8030'
                ]
            ];

            foreach ($products as $key => $product) {
                $products[$key]['shell_url']  = 'www.thisnew.com';
                $products[$key]['attribute']  = null;
                $products[$key]['created_at'] = now();
                $products[$key]['updated_at'] = now();
            }

            \App\Models\ShellProduct::insert($products);
        }
    }
}
