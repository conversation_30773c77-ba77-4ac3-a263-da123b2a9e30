<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPhonePrefixColumnToDirectoryCountriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('directory_countries', function (Blueprint $table) {
            $table->string('phone_prefix', 8)->default('')->comment('电话前缀')->after('isoa3');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('phone_prefix', function (Blueprint $table) {
            $table->dropColumn('phone_prefix');
        });
    }
}
