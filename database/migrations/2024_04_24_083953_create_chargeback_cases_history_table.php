<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChargebackCasesHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chargeback_cases_history', function (Blueprint $table) {
            $table->id();
            $table->string('case_id', 64)->index()->comment('case id');
            $table->tinyInteger('is_manual')->default(0)->comment('是否人工添加，1-是，0-否');
            $table->integer('business_history_id')->default(0)->index()->comment('条款历史id')->index();
            $table->string('order_id', 19)->index()->comment('交易订单号');
            $table->string('order_number', 32)->index()->comment('商户订单号');
            $table->char('merchant_id', 15)->index()->comment('MID');
            $table->string('merchant_name', 32)->comment('商户名');
            $table->char('business_id', 18)->comment('BID');
            $table->integer('channel_id')->default(0)->index()->comment('账单标识ID');
            $table->string('channel', 128)->comment('账单标识');
            $table->tinyInteger('alert_type')->default(0)->index()->comment('类型(0:Confirmed Fraud,1:Customer Dispute)');
            $table->string('reason_code', 32)->nullable()->comment('原因码');
            $table->string('result', 32)->comment('处理结果(0:Not Refunded,1:Refunded)');
            $table->string('reply_code', 32)->nullable()->comment('回复渠道代码');
            $table->integer('reply_status')->default(2)->comment('处理状态：0-未处理 1-已处理 待通知 2-已通知');
            $table->string('remarks', 128)->nullable()->comment('备注');
            $table->string('warn_channel', 128)->nullable()->comment('预警渠道账单标识');
            $table->char('warn_currency', 3)->nullable()->comment('预警币种');
            $table->decimal('warn_amount', 15, 2)->default(0.00)->comment('预警金额');
            $table->text('dishonour_warn_info')->comment('预警内容');
            $table->tinyInteger('is_meddle')->default(0)->comment('是否人工干预(0:否,1:是)');
            $table->integer('by_alert_id')->default(0)->comment('添加人ID');
            $table->string('by_alert', 128)->default('')->comment('添加人');
            $table->dateTime('date_complete')->comment('交易时间');
            $table->date('date_settle')->default('1970-01-01')->comment('结算时间');
            $table->tinyInteger('chargeback_from')->default(0)->comment('预警服务(0-CDRN 1-RDR 2-Ethoca)');
            $table->tinyInteger('alert_from')->default(0)->comment('预警来源(0-Verifi 1-Wintranx 3-EmbracyShield)');
            $table->integer('mod_times')->default(0)->comment('预警修改次数');
            $table->timestamps();

            $table->integer('source_cases_id')->index()->comment('预警表主键ID，chargeback_cases.id');
            $table->integer('modified_by_alert_id')->default(0)->comment('修改人ID');
            $table->string('modified_by_alert', 128)->default('')->comment('修改人');
            $table->dateTime('modified_at')->index()->nullable()->comment('修改时间');

            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chargeback_cases_history');
    }
}
