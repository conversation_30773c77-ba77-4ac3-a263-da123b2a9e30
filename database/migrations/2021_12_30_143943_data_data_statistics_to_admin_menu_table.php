<?php

use Illuminate\Database\Migrations\Migration;
use Dcat\Admin\Models\Menu;

class DataDataStatisticsToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()){
            $transaction = Menu::firstWhere('title', '交易管理');

            // 数据统计
            Menu::insert(
                [
                    [
                        "parent_id"  => $transaction->id,
                        "order"      => 7,
                        "title"      => "数据统计",
                        "icon"       => NULL,
                        "uri"        => "/data_statistics",
                        "created_at" => now(),
                        "updated_at" => now()
                    ]
                ]
            );
        }
    }
}
