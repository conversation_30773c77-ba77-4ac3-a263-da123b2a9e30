<?php

use App\Models\MerchantMenu;
use Illuminate\Database\Migrations\Migration;

class DataTrackManagerToMerchantMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) { // 非本地执行
            // 运单管理
            $track = MerchantMenu::create(
                [
                    "parent_id"  => 0,
                    "order"      => 1,
                    "title"      => "运单管理",
                    "icon"       => "fa-cog",
                    "uri"        => NULL,
                    "created_at" => now(),
                    "updated_at" => now()
                ]
            );

            MerchantMenu::insert(
                [
                    [
                        "parent_id"  => $track->id,
                        "order"      => 1,
                        "title"      => "运单申请",
                        "icon"       => NULL,
                        "uri"        => "/track/orders",
                        "created_at" => now(),
                        "updated_at" => now()
                    ],
                    [
                        "parent_id"  => $track->id,
                        "order"      => 1,
                        "title"      => "运单信息",
                        "icon"       => NULL,
                        "uri"        => "/track/tracks",
                        "created_at" => now(),
                        "updated_at" => now()
                    ]
                ]
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
