<?php

use App\Models\ChannelRouteScheme;
use App\Models\DirectoryDictionary;
use App\Models\Merchant;
use Illuminate\Database\Migrations\Migration;

class ModifyNameToChannelRouteSchemesTbale extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $list         = ChannelRouteScheme::selectRaw('id,merchant_id,business_id,d_mcc_id')->get()->toArray();
        $merchantList = Merchant::pluck('merchant_name', 'merchant_id');
        $mccList      = DirectoryDictionary::where('type', 'MCC')->pluck('name', 'id');

        foreach ($list as $vo) {
            $update = [
                'name' => implode('-', [$merchantList[$vo['merchant_id']], substr($vo['business_id'], -3), $mccList[$vo['d_mcc_id']]])
            ];

            ChannelRouteScheme::where('id', $vo['id'])->update($update);
        }
    }
}
