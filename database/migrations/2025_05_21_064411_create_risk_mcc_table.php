<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRiskMccTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('risk_mcc', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('mcc',4)->nullable()->comment('mcc');
            $table->string('title',255)->default('')->comment('标题');
            $table->text('description')->nullable()->comment('描述');
            $table->enum('overall_risk_rating', ['Low', 'Medium', 'High', 'Restricted', 'Prohibited'])->comment('总体风险评级');
            $table->enum('aml_risk_level', ['Low', 'Medium', 'High', 'Prohibited'])->comment('反洗钱风险等级');
            $table->enum('fraud_risk_rating', ['Low', 'Medium', 'High', 'Prohibited'])->comment('欺诈风险评级');
            $table->enum('card_scheme_risk_level', ['Low', 'Medium', 'High', 'Prohibited'])->comment('卡方案风险等级');
            $table->enum('credit_risk_rating', ['Low', 'Medium', 'High', 'Prohibited'])->comment('信用风险评级');
            $table->text('remarks')->nullable()->comment('补充说明');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('risk_mcc');
    }
}
