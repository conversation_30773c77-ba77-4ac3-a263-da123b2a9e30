<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_addresses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('bill_first_name', 64)->index()->default('')->comment('账单人');
            $table->string('bill_last_name', 64)->index()->default('')->comment('账单人');
            $table->string('bill_name', 64)->index()->default('')->comment('账单人');
            $table->string('bill_email', 96)->index()->default('')->comment('账单人邮箱');
            $table->string('bill_address', 255)->index()->default('')->comment('账单人地址');
            $table->string('bill_city', 32)->default('')->comment('账单人城市');
            $table->string('bill_state', 32)->nullable()->comment('账单人地区');
            $table->string('bill_postcode', 10)->default('')->comment('账单人邮编');
            $table->string('bill_country', 64)->index()->default('')->comment('账单人国家');
            $table->char('bill_country_isoa2', 2)->index()->default('')->comment('账单人国家2字代码');
            $table->string('bill_phone', 32)->default('')->comment('账单人电话');
            $table->string('ship_first_name', 64)->index()->default('')->comment('账单人');
            $table->string('ship_last_name', 64)->index()->default('')->comment('账单人');
            $table->string('ship_name', 64)->index()->default('')->comment('收件人');
            $table->string('ship_email', 96)->index()->default('')->comment('收件人邮箱');
            $table->string('ship_address', 255)->index()->default('')->comment('收件人地址');
            $table->string('ship_city', 32)->default('')->comment('收件人城市');
            $table->string('ship_state', 32)->nullable()->comment('收件人地区');
            $table->string('ship_postcode', 10)->default('')->comment('收件人邮编');
            $table->string('ship_country', 64)->index()->default('')->comment('收件人国家');
            $table->char('ship_country_isoa2', 2)->index()->default('')->comment('收件人国家2字代码');
            $table->string('ship_phone', 32)->default('')->comment('收件人电话');
            $table->string('ip', 255)->index()->default('')->comment('IP');
            $table->string('ip_country', 64)->default('')->comment('IP物理国家');
            $table->char('ip_country_isoa2', 2)->default('')->comment('IP物理国家2字代码');
            $table->string('ip_city', 64)->nullable()->comment('IP物理城市');
            $table->string('ip_isp', 255)->nullable()->comment('IP物理互联网服务提供商');
            $table->string('ip_postal_code', 32)->nullable()->comment('IP物理邮编');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_addresses');
    }
}
