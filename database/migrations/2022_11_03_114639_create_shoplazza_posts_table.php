<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShoplazzaPostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shoplazza_posts', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('store_id')->index()->comment('店铺id');
            $table->string('store_url', 128)->index()->comment('店铺网址');
            $table->string('order_number', 64)->index()->comment('店铺支付单号');
            $table->string('merchant_id', 64)->index()->comment('店匠配置merchantId');
            $table->text('post_data')->comment('支付信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shoplazza_posts');
    }
}
