<?php

use App\Models\LocalPaymentOrder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLocalPaymentOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_payment_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->string('order_id', 19)->index()->default('')->comment('订单号');
            $table->string('order_number', 64)->index()->default('')->comment('提交给渠道的订单号');
            $table->string('payment_order_id', 64)->index()->default(0)->comment('渠道返回的订单号');
            $table->char('currency', 3)->comment('币种');
            $table->decimal('amount', 15, 2)->comment('金额');
            $table->string('transaction_type', 64)->index()->comment('交易类型');
            $table->tinyInteger('status')->default(LocalPaymentOrder::STATUS_RECEIVED)->index()->comment('交易状态');
            $table->string('code', 32)->nullable()->comment('返回代码');
            $table->string('result', 128)->nullable()->comment('返回结果');
            $table->string('remark', 128)->nullable()->comment('备注');
            $table->mediumText('html')->nullable()->comment('回调html');
            $table->timestamp('created_at')->index()->nullable();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_payment_orders');
    }
}
