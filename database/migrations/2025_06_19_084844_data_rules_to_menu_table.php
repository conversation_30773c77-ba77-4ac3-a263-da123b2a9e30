<?php

use Dcat\Admin\Models\Menu;
use Dcat\Admin\Models\Permission;
use Dcat\Admin\Models\Role;
use Illuminate\Database\Migrations\Migration;

class DataRulesToMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) { // 非本地执行
            // API管理
            $menuParentId = Menu::where('title', '风控管理')->where('parent_id', 0)->value('id');
            $holderId     = Menu::insertGetId(
                [
                    "parent_id"  => $menuParentId,
                    "order"      => 12,
                    "title"      => "风控规则",
                    "icon"       => NULL,
                    "uri"        => "/risk_control/rules",
                    "created_at" => now(),
                    "updated_at" => now()
                ]
            );

            $permissionParentId = Permission::where('slug', 'fkgl')->value('id');
            $permissionId       = Permission::insertGetId(
                [
                    'name'        => '风控规则',
                    'slug'        => '/risk_control/rules',
                    'http_method' => '',
                    'http_path'   => '/risk_control/rules*',
                    'order'       => 85,
                    'parent_id'   => $permissionParentId,
                    'created_at'  => now(),
                    'updated_at'  => now(),
                ]
            );

            //权限和菜单绑定
            DB::table('admin_permission_menu')->insertOrIgnore(
                [
                    [
                        'permission_id' => $permissionId,
                        'menu_id'       => $holderId,
                    ],
                    [
                        'permission_id' => $permissionId,
                        'menu_id'       => $menuParentId,
                    ],
                ]);

            $cidResourcePerm = Role::where('name', 'Administrator')->value('id');
            // 角色和权限绑定
            DB::table('admin_role_permissions')->insert(
                [
                    [
                        'role_id'       => $cidResourcePerm,
                        'permission_id' => $permissionId,
                        'created_at'    => now(),
                        'updated_at'    => now()
                    ],
                ]);

            $cidResourcePerm = Role::where('name', 'Risk Control Operation')->value('id');
            DB::table('admin_role_permissions')->insert(
                [
                    [
                        'role_id'       => $cidResourcePerm,
                        'permission_id' => $permissionId,
                        'created_at'    => now(),
                        'updated_at'    => now()
                    ],
                ]);
        }
    }
}
