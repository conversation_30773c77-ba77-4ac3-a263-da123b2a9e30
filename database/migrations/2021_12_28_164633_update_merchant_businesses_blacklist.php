<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMerchantBusinessesBlacklist extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->tinyInteger('open_blacklist')->after('open_whitelist')->default(0)->comment('是否开启黑名单拦截（0:关闭 1:开启）');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('merchant_businesses', function (Blueprint $table) {
            $table->dropColumn('open_blacklist');
        });
    }
}
