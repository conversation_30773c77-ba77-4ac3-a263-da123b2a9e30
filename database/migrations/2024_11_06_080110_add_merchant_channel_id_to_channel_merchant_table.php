<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMerchantChannelIdToChannelMerchantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('channel_merchants', function (Blueprint $table) {
            $table->string('merchant_channel_id', 64)->comment('商户渠道ID')->after('channel_merchant_id');
            $table->text('mcc_list')->nullable()->comment('MCC列表')->after('cmcc');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('channel_merchants', function (Blueprint $table) {
            $table->dropColumn('merchant_channel_id');
            $table->dropColumn('mcc_list');
        });
    }
}
