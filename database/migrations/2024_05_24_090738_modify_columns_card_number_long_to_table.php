<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyColumnsCardNumberLongToTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('card_tickets', function (Blueprint $table) {
	        $table->string('card_number', 128)->change();
        });
	    Schema::table('card_transactions', function (Blueprint $table) {
		    $table->string('card_number', 128)->change();
	    });
	    Schema::table('card_virtuals', function (Blueprint $table) {
		    $table->string('card_number', 128)->change();
			$table->string('cvv', 128)->change();
            $table->string('expiration_year', 128)->change();
            $table->string('expiration_month', 128)->change();
	    });
	    Schema::table('settle_detail_cards', function (Blueprint $table) {
		    $table->string('card_number', 128)->change();
	    });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
	    Schema::table('card_tickets', function (Blueprint $table) {
		    $table->string('card_number', 25)->change();
	    });
	    Schema::table('card_transactions', function (Blueprint $table) {
		    $table->string('card_number', 25)->change();
	    });
	    Schema::table('card_virtuals', function (Blueprint $table) {
		    $table->string('card_number', 25)->change();
			$table->char('cvv', 4)->change();
            $table->char('expiration_year', 4)->change();
            $table->char('expiration_month', 2)->change();
	    });
	    Schema::table('settle_detail_cards', function (Blueprint $table) {
		    $table->string('card_number', 25)->change();
	    });
    }
}
