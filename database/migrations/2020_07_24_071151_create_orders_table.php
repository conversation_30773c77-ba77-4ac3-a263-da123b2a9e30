<?php

use App\Models\Order;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->string('order_id', 19)->primary()->comment('订单号');
            $table->string('parent_order_id', 19)->default(0)->index()->comment('原始订单号');
            $table->char('merchant_id', 10)->index()->comment('商户号');
            $table->char('business_id', 13)->index()->comment('业务ID');
            $table->string('merchant_name', 32)->index()->comment('商户名');
            $table->integer('url_id')->unsigned()->default(0)->index()->comment('网站ID');
            $table->string('url_name', 128)->index()->comment('交易网站');
	        $table->integer('channel_id')->unsigned()->default(0)->index()->comment('账单标识ID');
	        $table->string('channel', 128)->index()->comment('账单标识');
            $table->string('order_number', 32)->index()->comment('商户订单号');
            $table->char('currency', 3)->comment('支付货币');
            $table->decimal('amount', 15, 2)->comment('支付金额');
            $table->tinyInteger('type')->default(Order::TYPES_AUTH)->index()->comment('交易类型');
            $table->tinyInteger('status')->default(Order::STATUS_RECEIVED)->comment('支付状态');
            $table->string('code', 32)->nullable()->comment('返回代码');
            $table->string('result', 128)->nullable()->comment('返回结果');
            $table->string('remark', 128)->nullable()->comment('备注');
            $table->bigInteger('address_id')->comment('地址id');
            $table->bigInteger('card_id')->comment('卡信息id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('orders');
    }
}
