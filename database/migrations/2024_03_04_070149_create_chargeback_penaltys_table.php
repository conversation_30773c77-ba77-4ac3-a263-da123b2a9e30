<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChargebackPenaltysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chargeback_penaltys', function (Blueprint $table) {
            $table->id();
            $table->string('order_id', 19)->index()->comment('订单号');
            $table->string('order_number', 32)->index()->comment('商户订单号');
            $table->string('chargeback_id', 19)->comment('拒付ID');
            $table->char('merchant_id', 15)->comment('MID');
            $table->char('business_id', 18)->comment('BID');
            $table->string('merchant_name', 32)->comment('商户名');
            $table->integer('business_history_id')->default(0)->comment('条款历史id');
            $table->tinyInteger('type')->default(0)->comment('罚金类型：0-固定拒付罚金 1-比例拒付罚金 2-固定拒付罚金* 3-比例拒付罚金*');
            $table->decimal('penalty_rate', 15, 2)->default(0.00)->comment('拒付率');
            $table->decimal('penalty_value', 15, 2)->default(0.00)->comment('固定（USD）/比例（数值）');
            $table->decimal('penalty_amount', 15, 2)->default(0.00)->comment('罚金');
            $table->timestamp('chargeback_at')->comment('拒付时间');
            $table->date('settle_at')->comment('结算日期');
            $table->timestamps();
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chargeback_penaltys');
    }
}
