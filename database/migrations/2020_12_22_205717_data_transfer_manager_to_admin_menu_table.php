<?php

use Dcat\Admin\Models\Menu;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DataTransferManagerToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) { // 非本地执行
            // 提现管理
            $transfer = Menu::create(
                [
                    "parent_id"  => 0,
                    "order"      => 15,
                    "title"      => "提现管理",
                    "icon"       => "fa-envelope-o",
                    "uri"        => NULL,
                    "created_at" => now(),
                    "updated_at" => now()
                ]
            );

            Menu::insert(
                [
                    [
                        "parent_id"  => $transfer->id,
                        "order"      => 1,
                        "title"      => "提现信息",
                        "icon"       => NULL,
                        "uri"        => "/transfer/tickets",
                        "created_at" => now(),
                        "updated_at" => now()
                    ],
                    [
                        "parent_id"  => $transfer->id,
                        "order"      => 2,
                        "title"      => "提现审核",
                        "icon"       => NULL,
                        "uri"        => "/transfer/checkTickets",
                        "created_at" => now(),
                        "updated_at" => now()
                    ],
                    [
                        "parent_id"  => $transfer->id,
                        "order"      => 3,
                        "title"      => "提现操作",
                        "icon"       => NULL,
                        "uri"        => "/transfer/processTickets",
                        "created_at" => now(),
                        "updated_at" => now()
                    ],
                    [
                        "parent_id"  => $transfer->id,
                        "order"      => 4,
                        "title"      => "提现复核",
                        "icon"       => NULL,
                        "uri"        => "/transfer/reviewTickets",
                        "created_at" => now(),
                        "updated_at" => now()
                    ]
                ]
            );
        }
    }
}
