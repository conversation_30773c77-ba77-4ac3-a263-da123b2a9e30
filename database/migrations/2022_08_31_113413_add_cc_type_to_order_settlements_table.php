<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCcTypeToOrderSettlementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->char('cc_type', 1)->default('')->comment('卡种')->after('payment_order_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_settlements', function (Blueprint $table) {
            $table->dropColumn('cc_type');
        });
    }
}
