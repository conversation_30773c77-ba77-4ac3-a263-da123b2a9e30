<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class Add0001ToOrderComplaintsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_complaints', function (Blueprint $table) {
            $table->tinyInteger('is_read')->default(0)->after('content')->comment('商户是否已读(0:未读,1:已读)');
            $table->longText('read_ids')->nullable()->after('is_read')->comment('已读角色ID');
            $table->tinyInteger('is_reply')->default(0)->after('read_ids')->comment('是否回复(0:持卡人回复,1:商户回复)');
            $table->tinyInteger('notice_status')->default(0)->after('is_reply')->comment('关注状态(0:未关注,1:!,2:?,3:√.4:o)');
            $table->dateTime('replied_at')->nullable()->comment('回复时间')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order_complaints', function (Blueprint $table) {
            $table->dropColumn('is_read');
            $table->dropColumn('read_ids');
            $table->dropColumn('is_reply');
            $table->dropColumn('notice_status');
        });
    }
}
