<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSettleAdjustmentCardsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('settle_adjustment_cards');
        Schema::create('settle_adjustment_cards', function (Blueprint $table) {
            $table->increments('id');
            $table->char('merchant_id', 15)->index()->comment('MID');
            $table->string('merchant_name', 32)->comment('商户名称');
            $table->char('cards_id', 19)->index()->comment('CID');
            $table->char('virtual_id', 16)->default(0)->index()->comment('虚拟卡ID');
            $table->string('type', 8)->comment('结算调整类型(00:充值金额,01:转出金额,10:卡转入金额,11:卡转出金额,12:退值比例手续费,13:销卡比例手续费,20:卡片开通费,21:卡片月管理费,30:共享卡交易消耗,31:交易单笔处理费,90:其他)');
            $table->char('currency', 3)->comment('货币');
            $table->decimal('past_balance', 15, 2)->comment('调整前CID余额');
            $table->decimal('amount', 15, 2)->default(0.00)->comment('金额');
            $table->decimal('rate', 15, 4)->default(1.0000)->comment('汇率');
            $table->string('remarks')->nullable()->comment('备注');
            $table->date('settle_at')->index()->comment('结算日期');
            $table->bigInteger('operate_id')->comment('操作人id');
            $table->string('operate_name', 120)->comment('操作人名称');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('settle_adjustment_cards');
    }
}
