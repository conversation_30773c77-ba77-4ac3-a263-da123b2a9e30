<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLocalOrderProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('local_order_products', function (Blueprint $table) {
            $table->bigIncrements('id');
	        $table->string('order_id', 19)->index()->comment('系统订单号');
	        $table->tinyInteger('type')->default(0)->comment('类型(0:商户,1:银行)');
	        $table->string('name', 255)->comment('产品名称');
	        $table->integer('qty')->default(1)->comment('产品数量');
	        $table->decimal('price', 15, 2)->comment('产品单价');
	        $table->string('url', 255)->nullable()->comment('产品链接');
	        $table->string('attribute', 255)->nullable()->comment('产品属性');
	        $table->string('sku', 255)->nullable()->comment('SKU');
	        $table->tinyInteger('is_gift')->default(0)->comment('是否为礼品(0:否,1:是)');
	        $table->tinyInteger('is_virtual')->default(0)->comment('是否为虚拟产品(0:否,1:是)');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('local_order_products');
    }
}
