<?php

use Illuminate\Database\Migrations\Migration;

class DataEmailManagerToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
    	if (!is_local()) { // 非本地执行
		    // 邮件管理
		    $card = Dcat\Admin\Models\Menu::create(
			    [
				    "parent_id" => 0,
				    "order" => 14,
				    "title" => "邮件管理",
				    "icon" => "fa-envelope-o",
				    "uri" => NULL,
				    "created_at" => now(),
				    "updated_at" => now()
			    ]
		    );

		    Dcat\Admin\Models\Menu::insert(
			    [
				    [
					    "parent_id"  => $card->id,
					    "order"      => 1,
					    "title"      => "邮件任务",
					    "icon"       => NULL,
					    "uri"        => "/email/tasks",
					    "created_at" => now(),
					    "updated_at" => now()
				    ]
			    ]
		    );
	    }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
}
