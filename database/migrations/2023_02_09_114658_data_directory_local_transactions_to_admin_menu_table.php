<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Dcat\Admin\Models\Menu;
use Dcat\Admin\Models\Permission;

class DataDirectoryLocalTransactionsToAdminMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //本地不执行
        if (!is_local()) {
            $menu_parent_id = Menu::where('title', '参数管理')->value('id');

            $menu_id = Menu::insertGetId([
                "parent_id"  => $menu_parent_id,
                "order"      => 29,
                "title"      => "本地支付类型",
                "icon"       => NULL,
                "uri"        => "/directory_local_transactions",
                "created_at" => now(),
                "updated_at" => now()
            ]);

            $permission_parent_id = Permission::where('slug', 'csgl')->value('id');

            $permission_id = Permission::insertGetId(
                [
                    "name"        => "本地支付类型",
                    "slug"        => "directory_local_transactions",
                    "http_method" => "",
                    "http_path"   => "/directory_local_transactions*",
                    "order"       => 29,
                    "parent_id"   => $permission_parent_id,
                    "created_at"  => now(),
                    "updated_at"  => now()
                ]
            );

            //权限和菜单绑定
            DB::table('admin_permission_menu')->insertOrIgnore([
                [
                    'permission_id' => $permission_id,
                    'menu_id'       => $menu_id,
                ],
                [
                    'permission_id' => $permission_id,
                    'menu_id'       => $menu_parent_id,
                ]
            ]);
        }
    }
}
