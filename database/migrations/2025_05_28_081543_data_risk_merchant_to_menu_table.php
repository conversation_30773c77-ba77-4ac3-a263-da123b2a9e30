<?php

use Dcat\Admin\Models\Permission;
use Dcat\Admin\Models\Role;
use Dcat\Admin\Models\Menu;
use Illuminate\Database\Migrations\Migration;

class DataRiskMerchantToMenuTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!is_local()) { // 非本地执行
            // API管理
            $menuParentId = Menu::where('title', '风控管理')->where('parent_id', 0)->value('id');
            $holderId     = Menu::insertGetId(
                [
                    "parent_id"  => $menuParentId,
                    "order"      => 11,
                    "title"      => "商户信息",
                    "icon"       => NULL,
                    "uri"        => "/risk_control/merchant",
                    "created_at" => now(),
                    "updated_at" => now()
                ]
            );

            $permissionParentId = Permission::where('slug', 'fkgl')->value('id');
            $permissionId       = Permission::insertGetId(
                [
                    'name'        => '商户信息',
                    'slug'        => '/risk_control/merchant',
                    'http_method' => '',
                    'http_path'   => '/risk_control/merchant*',
                    'order'       => 83,
                    'parent_id'   => $permissionParentId,
                    'created_at'  => now(),
                    'updated_at'  => now(),
                ]
            );

            //权限和菜单绑定
            DB::table('admin_permission_menu')->insertOrIgnore(
                [
                    [
                        'permission_id' => $permissionId,
                        'menu_id'       => $holderId,
                    ],
                    [
                        'permission_id' => $permissionId,
                        'menu_id'       => $menuParentId,
                    ],
                ]);

            $cidResourcePerm = Role::where('name', 'Administrator')->value('id');
            // 角色和权限绑定
            DB::table('admin_role_permissions')->insert(
                [
                    [
                        'role_id'       => $cidResourcePerm,
                        'permission_id' => $permissionId,
                        'created_at'    => now(),
                        'updated_at'    => now()
                    ],
                ]);

            $cidResourcePerm = Role::where('name', 'Risk Control Operation')->value('id');
            DB::table('admin_role_permissions')->insert(
                [
                    [
                        'role_id'       => $cidResourcePerm,
                        'permission_id' => $permissionId,
                        'created_at'    => now(),
                        'updated_at'    => now()
                    ],
                ]);
        }
    }

}
