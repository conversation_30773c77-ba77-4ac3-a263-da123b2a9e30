<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateRiskFinancialTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('risk_financial', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('risk_case_id')->comment('风险案例ID');
            $table->char('merchant_id', 15)->comment('商户ID');
            $table->string('file')->default('')->comment('文件');
            $table->string('remark')->default('')->comment('备注');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('risk_financial');
    }
}
