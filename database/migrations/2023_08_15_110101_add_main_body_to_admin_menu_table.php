<?php

use Dcat\Admin\Models\Menu;
use Dcat\Admin\Models\Permission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddMainBodyToAdminMenuTable extends Migration
{
	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		if (!is_local()) {
			$menuParentId       = Menu::where('title', '参数管理')->value('id');
			$permissionParentId = Permission::where('slug', 'csgl')->value('id');

			$menuConfigId = Menu::insertGetId([
				'parent_id' => $menuParentId,
				'order'     => 30, // 用于排序。
				'title'     => '主体管理',
				'icon'      => '',
				'uri'       => '/directory_main_body',
				'show'      => 1,
			]);

			$permissionConfigId = Permission::insertGetId([
				"name"        => '主体管理',
				"slug"        => 'directory_main_body',
				"http_method" => '',
				"http_path"   => '/directory_main_body*',
				"order"       => 30,
				"parent_id"   => $permissionParentId,
				"created_at"  => now(),
				"updated_at"  => now()
			]);

			//权限和菜单绑定
			DB::table('admin_permission_menu')->insertOrIgnore([
				[
					'permission_id' => $permissionConfigId,
					'menu_id'       => $menuParentId,
				],
				[
					'permission_id' => $permissionConfigId,
					'menu_id'       => $menuConfigId,
				]
			]);
		}

	}

}
