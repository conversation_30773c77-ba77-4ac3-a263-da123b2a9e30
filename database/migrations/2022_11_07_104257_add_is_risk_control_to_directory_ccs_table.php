<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIsRiskControlToDirectoryCcsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('directory_ccs', function (Blueprint $table) {
            $table->tinyInteger('is_risk_control')->default(0)->comment('是否风控控制(0:否 1:是)')->after('status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('directory_ccs', function (Blueprint $table) {
            $table->dropColumn('is_risk_control');
        });
    }
}
