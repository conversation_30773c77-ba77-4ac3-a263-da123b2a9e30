<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDirectoryExternalCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('directory_external_codes', function (Blueprint $table) {
	        $table->bigIncrements('id');
	        $table->string('external_code', 32)->comment('外部返回代码');
	        $table->tinyInteger('transaction_status')->default(0)->comment('交易状态');
	        $table->string('external_results', 128)->comment('外部返回结果');
	        $table->string('external_remarks', 128)->comment('外部返回备注');
	        $table->tinyInteger('is_throw')->default(0)->comment('是否可以抛投(0:否,1:是)');
	        $table->integer('notice_cnt')->default(0)->comment('告警次数');
	        $table->integer('sort')->default(0)->comment('排序');
	        $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('directory_external_codes');
    }
}
