<?php

use Illuminate\Database\Seeder;

class DirectoryExternalCodesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('directory_external_codes')->delete();
        
        \DB::table('directory_external_codes')->insert(array (
            0 => 
            array (
                'id' => 1,
                'external_code' => get_system_code('134'),
                'transaction_status' => 0,
                'external_results' => 'Duplicate reversal',
                'external_remarks' => '重复撤销',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'external_code' => get_system_code('135'),
                'transaction_status' => 0,
                'external_results' => 'No Available Payment Channel',
                'external_remarks' => '无可用支付通道',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'external_code' => get_system_code('136'),
                'transaction_status' => 0,
                'external_results' => 'Invalid capture date',
                'external_remarks' => '无效预授权完成日期',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'external_code' => get_system_code('137'),
                'transaction_status' => 0,
                'external_results' => 'Transaction not permitted on card',
                'external_remarks' => '不允许该卡交易',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'external_code' => get_system_code('138'),
                'transaction_status' => 0,
                'external_results' => 'Restricted card',
                'external_remarks' => '受限制卡',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'external_code' => get_system_code('139'),
                'transaction_status' => 2,
                'external_results' => 'Issuer response received too late',
                'external_remarks' => '发卡行响应超时',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'external_code' => get_system_code('140'),
                'transaction_status' => 0,
                'external_results' => 'Issuer does not participate in the service',
                'external_remarks' => '发卡行不提供服务',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'external_code' => get_system_code('141'),
                'transaction_status' => 0,
                'external_results' => 'Unable to verify PIN',
                'external_remarks' => 'PIN验证失败',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'external_code' => get_system_code('142'),
                'transaction_status' => 0,
                'external_results' => 'Issuer not available',
                'external_remarks' => '发卡方不能操作',
                'is_throw' => 0,
                'notice_cnt' => 8,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'external_code' => get_system_code('143'),
                'transaction_status' => 0,
                'external_results' => 'Cardholder is NOT permitted',
                'external_remarks' => '不允许该持卡人交易',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'external_code' => get_system_code('144'),
                'transaction_status' => 0,
                'external_results' => 'Merchant is NOT permitted',
                'external_remarks' => '不允许该商户交易',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'external_code' => get_system_code('145'),
                'transaction_status' => 2,
                'external_results' => 'Card issuer time out',
                'external_remarks' => '发卡行系统超时',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'external_code' => get_system_code('146'),
                'transaction_status' => 0,
                'external_results' => 'System error, Re-enter transaction',
                'external_remarks' => '银行系统错误，请重试',
                'is_throw' => 1,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'external_code' => get_system_code('150'),
                'transaction_status' => 2,
                'external_results' => 'Transaction is processing',
                'external_remarks' => '交易支付中',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'external_code' => get_system_code('151'),
                'transaction_status' => 0,
                'external_results' => 'Can not proceed 3DS transaction',
                'external_remarks' => '无法进行3D交易',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            15 => 
            array (
                'id' => 16,
                'external_code' => get_system_code('152'),
                'transaction_status' => 0,
                'external_results' => '3DS Verification Failed',
                'external_remarks' => '3D验证失败',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'external_code' => get_system_code('195'),
                'transaction_status' => 2,
                'external_results' => 'Time out and automatically reversed',
                'external_remarks' => '超时自动撤销',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'external_code' => get_system_code('200'),
                'transaction_status' => 2,
                'external_results' => 'Transaction is pending',
                'external_remarks' => '交易待定',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'external_code' => get_system_code('210'),
                'transaction_status' => 2,
                'external_results' => 'Transaction is pending',
                'external_remarks' => '交易待定',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'external_code' => get_system_code('400'),
                'transaction_status' => 1,
                'external_results' => 'Exchange rate query succeeds',
                'external_remarks' => '查询汇率成功',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'external_code' => get_system_code('401'),
                'transaction_status' => 0,
                'external_results' => 'Exchange rate query failed',
                'external_remarks' => '查询汇率失败',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'external_code' => get_system_code('502'),
                'transaction_status' => 0,
                'external_results' => 'Transaction Cancelled By Customer',
                'external_remarks' => '用户取消订单',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'external_code' => get_system_code('999'),
                'transaction_status' => 0,
                'external_results' => 'System error',
                'external_remarks' => '系统异常',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'external_code' => get_system_code('093'),
                'transaction_status' => 0,
                'external_results' => 'Exceeds amount limit',
                'external_remarks' => '超出金额限制',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'external_code' => get_system_code('094'),
                'transaction_status' => 0,
                'external_results' => 'Invalid transaction card / issuer /acquirer',
                'external_remarks' => '无效交易或支付信息不完整',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'external_code' => get_system_code('095'),
                'transaction_status' => 2,
                'external_results' => 'Time out',
                'external_remarks' => '交易超时',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            26 => 
            array (
                'id' => 27,
                'external_code' => get_system_code('096'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Expiration Date',
                'external_remarks' => '有效期错误',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            27 => 
            array (
                'id' => 28,
                'external_code' => get_system_code('097'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Card Type',
                'external_remarks' => '无效卡种',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            28 => 
            array (
                'id' => 29,
                'external_code' => get_system_code('098'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Amount',
                'external_remarks' => '无效金额',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            29 => 
            array (
                'id' => 30,
                'external_code' => get_system_code('099'),
                'transaction_status' => 0,
                'external_results' => 'Other errors',
                'external_remarks' => '其他异常',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            30 => 
            array (
                'id' => 31,
                'external_code' => get_system_code('100'),
                'transaction_status' => 0,
                'external_results' => 'Contact your bank or try another card',
                'external_remarks' => '发卡行不予承兑，请联系发卡行，确认银行卡支付限制',
                'is_throw' => 1,
                'notice_cnt' => 8,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            31 => 
            array (
                'id' => 32,
                'external_code' => get_system_code('101'),
                'transaction_status' => 0,
                'external_results' => 'Invalid card number',
                'external_remarks' => '无效卡号',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            32 => 
            array (
                'id' => 33,
                'external_code' => get_system_code('102'),
                'transaction_status' => 0,
                'external_results' => 'Expired card',
                'external_remarks' => '过期卡',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            33 => 
            array (
                'id' => 34,
                'external_code' => get_system_code('103'),
                'transaction_status' => 0,
                'external_results' => 'Invalid account',
                'external_remarks' => '无效账户或持卡人姓名错误',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            34 => 
            array (
                'id' => 35,
                'external_code' => get_system_code('104'),
                'transaction_status' => 0,
                'external_results' => 'Invalid card verification value',
                'external_remarks' => '校验码错误',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            35 => 
            array (
                'id' => 36,
                'external_code' => get_system_code('105'),
                'transaction_status' => 0,
                'external_results' => 'Duplicate transaction',
                'external_remarks' => '重复交易',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            36 => 
            array (
                'id' => 37,
                'external_code' => get_system_code('106'),
                'transaction_status' => 0,
                'external_results' => 'Exceeds PIN retry limit',
                'external_remarks' => 'PIN输入次数超限',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            37 => 
            array (
                'id' => 38,
                'external_code' => get_system_code('108'),
                'transaction_status' => 0,
                'external_results' => 'AVS verification failed',
                'external_remarks' => 'AVS校验失败',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            38 => 
            array (
                'id' => 39,
                'external_code' => get_system_code('110'),
                'transaction_status' => 0,
                'external_results' => 'Refer to card issuer',
                'external_remarks' => '请联系发卡行',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            39 => 
            array (
                'id' => 40,
                'external_code' => get_system_code('111'),
                'transaction_status' => 0,
                'external_results' => 'Invalid or Inactivited Card',
                'external_remarks' => '无效卡或卡片未激活',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            40 => 
            array (
                'id' => 41,
                'external_code' => get_system_code('118'),
                'transaction_status' => 0,
                'external_results' => 'Partial approval',
                'external_remarks' => '部分金额接受',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            41 => 
            array (
                'id' => 42,
                'external_code' => get_system_code('119'),
                'transaction_status' => 0,
                'external_results' => 'Stolen or lost card',
                'external_remarks' => '挂失卡',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            42 => 
            array (
                'id' => 43,
                'external_code' => get_system_code('124'),
                'transaction_status' => 0,
                'external_results' => 'Reversal is NOT allowed',
                'external_remarks' => '不允许撤销',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            43 => 
            array (
                'id' => 44,
                'external_code' => get_system_code('127'),
                'transaction_status' => 0,
                'external_results' => 'No checking account',
                'external_remarks' => '无普通支票账户',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            44 => 
            array (
                'id' => 45,
                'external_code' => get_system_code('128'),
                'transaction_status' => 0,
                'external_results' => 'No saving account',
                'external_remarks' => '无储蓄账户',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            45 => 
            array (
                'id' => 46,
                'external_code' => get_system_code('129'),
                'transaction_status' => 0,
                'external_results' => 'No credit account',
                'external_remarks' => '无信用账户',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            46 => 
            array (
                'id' => 47,
                'external_code' => get_system_code('130'),
                'transaction_status' => 0,
                'external_results' => 'Exceeds acquirer limit',
                'external_remarks' => '超出收单机构限制',
                'is_throw' => 1,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            47 => 
            array (
                'id' => 48,
                'external_code' => get_system_code('131'),
                'transaction_status' => 0,
                'external_results' => 'Retry',
                'external_remarks' => '请重试',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            48 => 
            array (
                'id' => 49,
                'external_code' => get_system_code('000'),
                'transaction_status' => 1,
                'external_results' => 'Transaction is approved',
                'external_remarks' => '交易成功',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'external_code' => get_system_code('010'),
                'transaction_status' => 3,
                'external_results' => 'Approved by issuer but suspected by XBP',
                'external_remarks' => '交易成功但XBP侦测到疑似风险',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'external_code' => get_system_code('053'),
                'transaction_status' => 0,
                'external_results' => 'Issuer detects fraud',
                'external_remarks' => '发卡行侦测风险订单',
                'is_throw' => 1,
                'notice_cnt' => 3,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'external_code' => get_system_code('054'),
                'transaction_status' => 0,
                'external_results' => 'test detects fraud',
                'external_remarks' => 'test侦测风险订单',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'external_code' => get_system_code('071'),
                'transaction_status' => 0,
                'external_results' => 'Exceeds amount or frequency limit',
                'external_remarks' => '超出限额限次',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            53 => 
            array (
                'id' => 54,
                'external_code' => get_system_code('020'),
                'transaction_status' => 1,
                'external_results' => 'Request accepted',
                'external_remarks' => '请求接受成功',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            54 => 
            array (
                'id' => 55,
                'external_code' => get_system_code('330'),
                'transaction_status' => 0,
                'external_results' => 'The original is not completed，not allowed',
                'external_remarks' => '原交易未完成，操作失败',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            55 => 
            array (
                'id' => 56,
                'external_code' => get_system_code('340'),
                'transaction_status' => 0,
                'external_results' => 'Refund is allowed on chargebacked transaction',
                'external_remarks' => '该订单存在拒付，不允许退款',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            56 => 
            array (
                'id' => 57,
                'external_code' => get_system_code('348'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Merchant',
                'external_remarks' => '无效商户',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            57 => 
            array (
                'id' => 58,
                'external_code' => get_system_code('452'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Token',
                'external_remarks' => '无效令牌',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            58 => 
            array (
                'id' => 59,
                'external_code' => get_system_code('503'),
                'transaction_status' => 1,
                'external_results' => 'Revoke successful',
                'external_remarks' => '撤销成功',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            59 => 
            array (
                'id' => 60,
                'external_code' => get_system_code('001'),
                'transaction_status' => 0,
                'external_results' => 'Invalid Parameters',
                'external_remarks' => '参数不合法',
                'is_throw' => 0,
                'notice_cnt' => 2,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            60 => 
            array (
                'id' => 61,
                'external_code' => get_system_code('003'),
                'transaction_status' => 0,
                'external_results' => 'Signature is NOT verified',
                'external_remarks' => '验签异常',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            61 => 
            array (
                'id' => 62,
                'external_code' => get_system_code('023'),
                'transaction_status' => 0,
                'external_results' => 'Insufficient merchant account balance',
                'external_remarks' => '商户可用余额不足',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            62 => 
            array (
                'id' => 63,
                'external_code' => get_system_code('024'),
                'transaction_status' => 0,
                'external_results' => 'Merchant account is abnormal',
                'external_remarks' => '商户账户异常',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            63 => 
            array (
                'id' => 64,
                'external_code' => get_system_code('300'),
                'transaction_status' => 0,
                'external_results' => 'Abnormal Transaction',
                'external_remarks' => '交易异常',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            64 => 
            array (
                'id' => 65,
                'external_code' => get_system_code('301'),
                'transaction_status' => 0,
                'external_results' => 'No original transaction',
                'external_remarks' => '无原始交易信息',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            65 => 
            array (
                'id' => 66,
                'external_code' => get_system_code('305'),
                'transaction_status' => 0,
                'external_results' => 'Account not allowed out',
                'external_remarks' => '账户止出',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            66 => 
            array (
                'id' => 67,
                'external_code' => get_system_code('306'),
                'transaction_status' => 0,
                'external_results' => 'Account not allowed in',
                'external_remarks' => '账户止入',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            67 => 
            array (
                'id' => 68,
                'external_code' => get_system_code('302'),
                'transaction_status' => 0,
                'external_results' => 'Abandoned by Cardholder',
                'external_remarks' => '持卡人放弃交易',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            68 => 
            array (
                'id' => 69,
                'external_code' => get_system_code('084'),
                'transaction_status' => 0,
                'external_results' => 'Insufficient balance',
                'external_remarks' => '可用余额不足',
                'is_throw' => 1,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            69 => 
            array (
                'id' => 70,
                'external_code' => get_system_code('085'),
                'transaction_status' => 0,
                'external_results' => 'CVV Mismatch',
                'external_remarks' => 'CVV错误',
                'is_throw' => 0,
                'notice_cnt' => 10,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            70 => 
            array (
                'id' => 71,
                'external_code' => get_system_code('086'),
                'transaction_status' => 0,
                'external_results' => 'Violation of security policy',
                'external_remarks' => '违反安全规定',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            71 => 
            array (
                'id' => 72,
                'external_code' => get_system_code('088'),
                'transaction_status' => 0,
                'external_results' => 'Exceeds frequency limit',
                'external_remarks' => '超出银行限次',
                'is_throw' => 0,
                'notice_cnt' => 1,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            72 => 
            array (
                'id' => 73,
                'external_code' => get_system_code('089'),
                'transaction_status' => 0,
                'external_results' => 'Suspected fraud',
                'external_remarks' => '有欺诈嫌疑',
                'is_throw' => 0,
                'notice_cnt' => 5,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            73 => 
            array (
                'id' => 74,
                'external_code' => get_system_code('072'),
                'transaction_status' => 0,
                'external_results' => 'Risk rule out',
                'external_remarks' => '规则排除',
                'is_throw' => 1,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            74 => 
            array (
                'id' => 75,
                'external_code' => get_system_code('076'),
                'transaction_status' => 0,
                'external_results' => 'Risk rule out',
                'external_remarks' => '规则排除',
                'is_throw' => 1,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            75 => 
            array (
                'id' => 76,
                'external_code' => get_system_code('078'),
                'transaction_status' => 0,
                'external_results' => 'Risk rule out',
                'external_remarks' => '规则排除',
                'is_throw' => 1,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            76 => 
            array (
                'id' => 77,
                'external_code' => get_system_code('080'),
                'transaction_status' => 0,
                'external_results' => 'Risk rule out',
                'external_remarks' => '规则排除',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            77 => 
            array (
                'id' => 78,
                'external_code' => get_system_code('081'),
                'transaction_status' => 0,
                'external_results' => 'Not on the white list',
                'external_remarks' => '不在白名单上',
                'is_throw' => 1,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            78 => 
            array (
                'id' => 79,
                'external_code' => get_system_code('801'),
                'transaction_status' => 0,
                'external_results' => 'Domestic IP is not allowed',
                'external_remarks' => '不允许国内IP',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            79 => 
            array (
                'id' => 80,
                'external_code' => get_system_code('802'),
                'transaction_status' => 0,
                'external_results' => 'Restricted card',
                'external_remarks' => '受限制卡',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            80 => 
            array (
                'id' => 81,
                'external_code' => get_system_code('803'),
                'transaction_status' => 0,
                'external_results' => 'Invalid card verification value',
                'external_remarks' => '校验码错误',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            81 => 
            array (
                'id' => 82,
                'external_code' => get_system_code('804'),
                'transaction_status' => 0,
                'external_results' => 'Card is being processed',
                'external_remarks' => '并发交易',
                'is_throw' => 0,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            82 => 
            array (
                'id' => 83,
                'external_code' => get_system_code('806'),
                'transaction_status' => 0,
                'external_results' => 'Singapore transaction is not allowed',
                'external_remarks' => '不允许新加坡交易',
                'is_throw' => 1,
                'notice_cnt' => 0,
                'sort' => 0,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
        ));
        
        
    }
}