<?php

use Illuminate\Database\Seeder;

class MerchantUrlsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('merchant_urls')->delete();
        
        \DB::table('merchant_urls')->insert(array (
            0 => 
            array (
                'id' => 1,
                'merchant_id' => '160103346595807',
                'merchant_name' => 'test-new',
                'business_id' => '160103346595807001',
                'url_name' => 'www.new.com',
                'main_url_name' => 'new.com',
                'd_mcc_id' => 20,
                'd_brand_id' => 48,
                'cc_types' => 'V,M',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 19:49:35',
                'updated_at' => '2020-09-25 19:49:56',
            ),
            1 => 
            array (
                'id' => 3,
                'merchant_id' => '160103888962374',
                'merchant_name' => 'aaa',
                'business_id' => '160103888962374001',
                'url_name' => 'www.aaaa.com',
                'main_url_name' => 'aaaa.com',
                'd_mcc_id' => 20,
                'd_brand_id' => 48,
                'cc_types' => 'V,M,J,A,O,P,D',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 21:06:00',
                'updated_at' => '2020-09-25 21:08:28',
            ),
            2 => 
            array (
                'id' => 4,
                'merchant_id' => '160103896376637',
                'merchant_name' => 'bbb',
                'business_id' => '160103896376637001',
                'url_name' => 'www.bbb.com',
                'main_url_name' => 'bbb.com',
                'd_mcc_id' => 22,
                'd_brand_id' => 49,
                'cc_types' => 'V,M',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 21:06:37',
                'updated_at' => '2020-09-25 21:08:36',
            ),
            3 => 
            array (
                'id' => 5,
                'merchant_id' => '160103888962374',
                'merchant_name' => 'aaa',
                'business_id' => '160103888962374001',
                'url_name' => 'www.aaa.com',
                'main_url_name' => 'aaa.com',
                'd_mcc_id' => 20,
                'd_brand_id' => 48,
                'cc_types' => 'V,M,J,A',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 21:07:14',
                'updated_at' => '2020-09-25 21:08:46',
            ),
            4 => 
            array (
                'id' => 6,
                'merchant_id' => '160103888962374',
                'merchant_name' => 'aaa',
                'business_id' => '160103888962374001',
                'url_name' => 'www.aaa3d.com',
                'main_url_name' => 'aaa3d.com',
                'd_mcc_id' => 20,
                'd_brand_id' => 49,
                'cc_types' => 'V,M,J,A,O',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 21:07:22',
                'updated_at' => '2020-09-25 21:09:00',
            ),
            5 => 
            array (
                'id' => 7,
                'merchant_id' => '160103346595807',
                'merchant_name' => 'test-new',
                'business_id' => '160103346595807001',
                'url_name' => 'www.new3d.com',
                'main_url_name' => 'new3d.com',
                'd_mcc_id' => 19,
                'd_brand_id' => 48,
                'cc_types' => 'V,M,J,A,O',
                'url_status' => 1,
                'pid_status' => 0,
                'remarks' => NULL,
                'created_at' => '2020-09-25 21:08:10',
                'updated_at' => '2020-09-25 21:09:14',
            ),
        ));
        
        
    }
}