<?php

use Illuminate\Database\Seeder;

class OrderHistorysTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('order_historys')->delete();
        
        \DB::table('order_historys')->insert(array (
            0 => 
            array (
                'id' => 1,
                'order_id' => '2020092520200184266',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:20:09',
                'updated_at' => '2020-09-25 20:20:09',
            ),
            1 => 
            array (
                'id' => 2,
                'order_id' => '2020092520352535823',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:35:40',
                'updated_at' => '2020-09-25 20:35:40',
            ),
            2 => 
            array (
                'id' => 3,
                'order_id' => '2020092520370808574',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:37:12',
                'updated_at' => '2020-09-25 20:37:12',
            ),
            3 => 
            array (
                'id' => 4,
                'order_id' => '2020092520372656585',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:37:33',
                'updated_at' => '2020-09-25 20:37:33',
            ),
            4 => 
            array (
                'id' => 5,
                'order_id' => '2020092520374072311',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:37:45',
                'updated_at' => '2020-09-25 20:37:45',
            ),
            5 => 
            array (
                'id' => 6,
                'order_id' => '2020092520380042757',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:38:09',
                'updated_at' => '2020-09-25 20:38:09',
            ),
            6 => 
            array (
                'id' => 7,
                'order_id' => '2020092520381574693',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:38:29',
                'updated_at' => '2020-09-25 20:38:29',
            ),
            7 => 
            array (
                'id' => 8,
                'order_id' => '2020092520383982178',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 1,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:38:42',
                'updated_at' => '2020-09-25 20:38:42',
            ),
            8 => 
            array (
                'id' => 9,
                'order_id' => '2020092520385369520',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:38:59',
                'updated_at' => '2020-09-25 20:38:59',
            ),
            9 => 
            array (
                'id' => 10,
                'order_id' => '2020092520390723295',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:39:11',
                'updated_at' => '2020-09-25 20:39:11',
            ),
            10 => 
            array (
                'id' => 11,
                'order_id' => '2020092520391759204',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:39:28',
                'updated_at' => '2020-09-25 20:39:28',
            ),
            11 => 
            array (
                'id' => 12,
                'order_id' => '2020092520393113471',
                'channel_id' => 1,
                'channel' => 'XBP-testXbp-test',
                'type' => 2,
                'status' => 3,
                'code' => '',
                'result' => '',
                'remark' => '',
                'created_at' => '2020-09-25 20:39:36',
                'updated_at' => '2020-09-25 20:39:36',
            ),
        ));
        
        
    }
}