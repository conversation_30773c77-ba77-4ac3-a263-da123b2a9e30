<?php

use Illuminate\Database\Seeder;

class CardSourcesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('card_sources')->delete();
        
        \DB::table('card_sources')->insert(array (
            0 => 
            array (
                'id' => 1,
                'cc_type' => 'V',
                'currency' => 'USD',
                'par_value' => '80.00',
                'card_fee' => '10.00',
                'labor_costs' => '10.00',
                'total_value' => '90.00',
                'cny_rate' => '7.00',
                'total_invest' => '100.00',
                'available_amount' => '80.00',
                'last_transaction_amount' => '0.00',
                'limite_times' => 10,
                'total_limite_times' => 10,
                'total_used_times' => 0,
                'used_times' => 0,
                'd_card_type_id' => 155,
                'card_from' => '-',
                'purchaser' => '-',
                'card_number' => '****************',
                'expiration_month' => '08',
                'expiration_year' => '23',
                'cvv' => '123',
                'url' => 'www.test.com',
                'remarks' => NULL,
                'status' => 1,
                'type' => 0,
                'date_last_time' => NULL,
                'created_at' => '2020-09-25 20:49:14',
                'updated_at' => '2020-09-25 20:49:14',
            ),
            1 => 
            array (
                'id' => 2,
                'cc_type' => 'V',
                'currency' => 'USD',
                'par_value' => '90.00',
                'card_fee' => '10.00',
                'labor_costs' => '10.00',
                'total_value' => '100.00',
                'cny_rate' => '8.00',
                'total_invest' => '110.00',
                'available_amount' => '90.00',
                'last_transaction_amount' => '0.00',
                'limite_times' => 20,
                'total_limite_times' => 20,
                'total_used_times' => 0,
                'used_times' => 0,
                'd_card_type_id' => 158,
                'card_from' => '-',
                'purchaser' => '-',
                'card_number' => '****************',
                'expiration_month' => '12',
                'expiration_year' => '23',
                'cvv' => '125',
                'url' => NULL,
                'remarks' => NULL,
                'status' => 1,
                'type' => 0,
                'date_last_time' => NULL,
                'created_at' => '2020-09-25 20:59:01',
                'updated_at' => '2020-09-25 20:59:01',
            ),
        ));
        
        
    }
}