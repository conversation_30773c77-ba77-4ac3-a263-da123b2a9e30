<?php

use Illuminate\Database\Seeder;

class PaymentOrdersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('payment_orders')->delete();
        
        \DB::table('payment_orders')->insert(array (
            0 => 
            array (
                'id' => 1,
                'order_id' => '2020092520200184266',
                'order_number' => '2020092520200184266',
                'payment_order_id' => '1601036404684240',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:20:02',
                'updated_at' => '2020-09-25 20:20:09',
            ),
            1 => 
            array (
                'id' => 2,
                'order_id' => '2020092520352535823',
                'order_number' => '2020092520352535823',
                'payment_order_id' => '1601037336358509',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:35:25',
                'updated_at' => '2020-09-25 20:35:40',
            ),
            2 => 
            array (
                'id' => 3,
                'order_id' => '2020092520370808574',
                'order_number' => '2020092520370808574',
                'payment_order_id' => '1601037430312138',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:37:08',
                'updated_at' => '2020-09-25 20:37:12',
            ),
            3 => 
            array (
                'id' => 4,
                'order_id' => '2020092520372656585',
                'order_number' => '2020092520372656585',
                'payment_order_id' => '1601037448718539',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:37:26',
                'updated_at' => '2020-09-25 20:37:33',
            ),
            4 => 
            array (
                'id' => 5,
                'order_id' => '2020092520374072311',
                'order_number' => '2020092520374072311',
                'payment_order_id' => '1601037461725094',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:37:40',
                'updated_at' => '2020-09-25 20:37:45',
            ),
            5 => 
            array (
                'id' => 6,
                'order_id' => '2020092520380042757',
                'order_number' => '2020092520380042757',
                'payment_order_id' => '1601037485122048',
                'currency' => 'USD',
                'amount' => '36.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:38:00',
                'updated_at' => '2020-09-25 20:38:09',
            ),
            6 => 
            array (
                'id' => 7,
                'order_id' => '2020092520381574693',
                'order_number' => '2020092520381574693',
                'payment_order_id' => '1601037507214526',
                'currency' => 'USD',
                'amount' => '56.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:38:15',
                'updated_at' => '2020-09-25 20:38:29',
            ),
            7 => 
            array (
                'id' => 8,
                'order_id' => '2020092520383982178',
                'order_number' => '2020092520383982178',
                'payment_order_id' => '1601037520278412',
                'currency' => 'USD',
                'amount' => '86.11',
                'type' => 1,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '{"riskLevel":"0","riskMessage":""}',
                'html' => '[]',
                'created_at' => '2020-09-25 20:38:40',
                'updated_at' => '2020-09-25 20:38:42',
            ),
            8 => 
            array (
                'id' => 9,
                'order_id' => '2020092520385369520',
                'order_number' => '2020092520385369520',
                'payment_order_id' => '1601037534987416',
                'currency' => 'USD',
                'amount' => '16.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:38:53',
                'updated_at' => '2020-09-25 20:38:59',
            ),
            9 => 
            array (
                'id' => 10,
                'order_id' => '2020092520390723295',
                'order_number' => '2020092520390723295',
                'payment_order_id' => '1601037549427225',
                'currency' => 'USD',
                'amount' => '36.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:39:07',
                'updated_at' => '2020-09-25 20:39:11',
            ),
            10 => 
            array (
                'id' => 11,
                'order_id' => '2020092520391759204',
                'order_number' => '2020092520391759204',
                'payment_order_id' => '1601037563961003',
                'currency' => 'USD',
                'amount' => '56.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:39:17',
                'updated_at' => '2020-09-25 20:39:28',
            ),
            11 => 
            array (
                'id' => 12,
                'order_id' => '2020092520393113471',
                'order_number' => '2020092520393113471',
                'payment_order_id' => '1601037573329987',
                'currency' => 'USD',
                'amount' => '86.11',
                'type' => 2,
                'status' => 1,
                'code' => '0000',
                'result' => 'Transaction is approved',
                'remark' => '',
                'html' => '',
                'created_at' => '2020-09-25 20:39:31',
                'updated_at' => '2020-09-25 20:39:36',
            ),
        ));
        
        
    }
}