<?php

use Illuminate\Database\Seeder;

class DirectoryCountriesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('directory_countries')->delete();
        
        \DB::table('directory_countries')->insert(array (
            0 => 
            array (
                'id' => 1,
                'name' => 'Aaland Islands',
                'isoa2' => 'AX',
                'isoa3' => '',
                'isonumber' => '248',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'name' => 'Afghanistan',
                'isoa2' => 'AF',
                'isoa3' => '',
                'isonumber' => '004',
                'areanum' => '93',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'name' => 'Albania',
                'isoa2' => 'AL',
                'isoa3' => '',
                'isonumber' => '008',
                'areanum' => '355',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'name' => 'Algeria',
                'isoa2' => 'DZ',
                'isoa3' => '',
                'isonumber' => '012',
                'areanum' => '213',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            4 => 
            array (
                'id' => 5,
                'name' => 'American Samoa',
                'isoa2' => 'AS',
                'isoa3' => '',
                'isonumber' => '016',
                'areanum' => '1684',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            5 => 
            array (
                'id' => 6,
                'name' => 'Andorra',
                'isoa2' => 'AD',
                'isoa3' => '',
                'isonumber' => '020',
                'areanum' => '376',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            6 => 
            array (
                'id' => 7,
                'name' => 'Angola',
                'isoa2' => 'AO',
                'isoa3' => '',
                'isonumber' => '024',
                'areanum' => '244',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            7 => 
            array (
                'id' => 8,
                'name' => 'Anguilla',
                'isoa2' => 'AI',
                'isoa3' => '',
                'isonumber' => '660',
                'areanum' => '1264',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            8 => 
            array (
                'id' => 9,
                'name' => 'Antarctica',
                'isoa2' => 'AQ',
                'isoa3' => '',
                'isonumber' => '010',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            9 => 
            array (
                'id' => 10,
                'name' => 'Antigua and Barbuda',
                'isoa2' => 'AG',
                'isoa3' => '',
                'isonumber' => '028',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            10 => 
            array (
                'id' => 11,
                'name' => 'Argentina',
                'isoa2' => 'AR',
                'isoa3' => '',
                'isonumber' => '032',
                'areanum' => '54',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            11 => 
            array (
                'id' => 12,
                'name' => 'Armenia',
                'isoa2' => 'AM',
                'isoa3' => '',
                'isonumber' => '051',
                'areanum' => '374',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            12 => 
            array (
                'id' => 13,
                'name' => 'Aruba',
                'isoa2' => 'AW',
                'isoa3' => '',
                'isonumber' => '533',
                'areanum' => '297',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            13 => 
            array (
                'id' => 14,
                'name' => 'Australia',
                'isoa2' => 'AU',
                'isoa3' => '',
                'isonumber' => '036',
                'areanum' => '61',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            14 => 
            array (
                'id' => 15,
                'name' => 'Austria',
                'isoa2' => 'AT',
                'isoa3' => '',
                'isonumber' => '040',
                'areanum' => '43',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            15 => 
            array (
                'id' => 16,
                'name' => 'Azerbaijan',
                'isoa2' => 'AZ',
                'isoa3' => '',
                'isonumber' => '031',
                'areanum' => '994',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            16 => 
            array (
                'id' => 17,
                'name' => 'Bahamas',
                'isoa2' => 'BS',
                'isoa3' => '',
                'isonumber' => '044',
                'areanum' => '1242',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            17 => 
            array (
                'id' => 18,
                'name' => 'Bahrain',
                'isoa2' => 'BH',
                'isoa3' => '',
                'isonumber' => '048',
                'areanum' => '973',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            18 => 
            array (
                'id' => 19,
                'name' => 'Bangladesh',
                'isoa2' => 'BD',
                'isoa3' => '',
                'isonumber' => '050',
                'areanum' => '880',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            19 => 
            array (
                'id' => 20,
                'name' => 'Barbados',
                'isoa2' => 'BB',
                'isoa3' => '',
                'isonumber' => '052',
                'areanum' => '1246',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            20 => 
            array (
                'id' => 21,
                'name' => 'Belarus',
                'isoa2' => 'BY',
                'isoa3' => '',
                'isonumber' => '112',
                'areanum' => '375',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            21 => 
            array (
                'id' => 22,
                'name' => 'Belgium',
                'isoa2' => 'BE',
                'isoa3' => '',
                'isonumber' => '056',
                'areanum' => '32',
                'fee' => 7,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            22 => 
            array (
                'id' => 23,
                'name' => 'Belize',
                'isoa2' => 'BZ',
                'isoa3' => '',
                'isonumber' => '084',
                'areanum' => '501',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            23 => 
            array (
                'id' => 24,
                'name' => 'Benin',
                'isoa2' => 'BJ',
                'isoa3' => '',
                'isonumber' => '204',
                'areanum' => '229',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            24 => 
            array (
                'id' => 25,
                'name' => 'Bermuda',
                'isoa2' => 'BM',
                'isoa3' => '',
                'isonumber' => '060',
                'areanum' => '1441',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            25 => 
            array (
                'id' => 26,
                'name' => 'Bhutan',
                'isoa2' => 'BT',
                'isoa3' => '',
                'isonumber' => '064',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            26 => 
            array (
                'id' => 27,
                'name' => 'Bolivia',
                'isoa2' => 'BO',
                'isoa3' => '',
                'isonumber' => '068',
                'areanum' => '591',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            27 => 
            array (
                'id' => 28,
                'name' => 'Bonaire, Sint Eustatius and Saba',
                'isoa2' => 'BQ',
                'isoa3' => '',
                'isonumber' => '535',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            28 => 
            array (
                'id' => 29,
                'name' => 'Bosnia and Herzegovina',
                'isoa2' => 'BA',
                'isoa3' => '',
                'isonumber' => '070',
                'areanum' => '387',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            29 => 
            array (
                'id' => 30,
                'name' => 'Botswana',
                'isoa2' => 'BW',
                'isoa3' => '',
                'isonumber' => '072',
                'areanum' => '267',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            30 => 
            array (
                'id' => 31,
                'name' => 'Bouvet Island',
                'isoa2' => 'BV',
                'isoa3' => '',
                'isonumber' => '074',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            31 => 
            array (
                'id' => 32,
                'name' => 'Brazil',
                'isoa2' => 'BR',
                'isoa3' => '',
                'isonumber' => '076',
                'areanum' => '55',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            32 => 
            array (
                'id' => 33,
                'name' => 'British Indian Ocean Territory',
                'isoa2' => 'IO',
                'isoa3' => '',
                'isonumber' => '086',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            33 => 
            array (
                'id' => 34,
                'name' => 'Brunei Darussalam',
                'isoa2' => 'BN',
                'isoa3' => '',
                'isonumber' => '096',
                'areanum' => '673',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            34 => 
            array (
                'id' => 35,
                'name' => 'Bulgaria',
                'isoa2' => 'BG',
                'isoa3' => '',
                'isonumber' => '100',
                'areanum' => '359',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            35 => 
            array (
                'id' => 36,
                'name' => 'Burkina Faso',
                'isoa2' => 'BF',
                'isoa3' => '',
                'isonumber' => '854',
                'areanum' => '226',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            36 => 
            array (
                'id' => 37,
                'name' => 'Burundi',
                'isoa2' => 'BI',
                'isoa3' => '',
                'isonumber' => '108',
                'areanum' => '257',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            37 => 
            array (
                'id' => 38,
                'name' => 'Cambodia',
                'isoa2' => 'KH',
                'isoa3' => '',
                'isonumber' => '116',
                'areanum' => '855',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            38 => 
            array (
                'id' => 39,
                'name' => 'Cameroon',
                'isoa2' => 'CM',
                'isoa3' => '',
                'isonumber' => '120',
                'areanum' => '237',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            39 => 
            array (
                'id' => 40,
                'name' => 'Canada',
                'isoa2' => 'CA',
                'isoa3' => '',
                'isonumber' => '124',
                'areanum' => '1',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            40 => 
            array (
                'id' => 41,
                'name' => 'Canary Islands',
                'isoa2' => 'IC',
                'isoa3' => '',
                'isonumber' => '000',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            41 => 
            array (
                'id' => 42,
                'name' => 'Cape Verde',
                'isoa2' => 'CV',
                'isoa3' => '',
                'isonumber' => '132',
                'areanum' => '238',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            42 => 
            array (
                'id' => 43,
                'name' => 'Cayman Islands',
                'isoa2' => 'KY',
                'isoa3' => '',
                'isonumber' => '136',
                'areanum' => '1345',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            43 => 
            array (
                'id' => 44,
                'name' => 'Central African Republic',
                'isoa2' => 'CF',
                'isoa3' => '',
                'isonumber' => '140',
                'areanum' => '236',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            44 => 
            array (
                'id' => 45,
                'name' => 'Chad',
                'isoa2' => 'TD',
                'isoa3' => '',
                'isonumber' => '148',
                'areanum' => '235',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            45 => 
            array (
                'id' => 46,
                'name' => 'Chile',
                'isoa2' => 'CL',
                'isoa3' => '',
                'isonumber' => '152',
                'areanum' => '56',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            46 => 
            array (
                'id' => 47,
                'name' => 'China',
                'isoa2' => 'CN',
                'isoa3' => '',
                'isonumber' => '156',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            47 => 
            array (
                'id' => 48,
                'name' => 'Christmas Island',
                'isoa2' => 'CX',
                'isoa3' => '',
                'isonumber' => '162',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            48 => 
            array (
                'id' => 49,
            'name' => 'Cocos (Keeling) Islands',
                'isoa2' => 'CC',
                'isoa3' => '',
                'isonumber' => '166',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            49 => 
            array (
                'id' => 50,
                'name' => 'Colombia',
                'isoa2' => 'CO',
                'isoa3' => '',
                'isonumber' => '170',
                'areanum' => '57',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            50 => 
            array (
                'id' => 51,
                'name' => 'Comoros',
                'isoa2' => 'KM',
                'isoa3' => '',
                'isonumber' => '174',
                'areanum' => '269',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            51 => 
            array (
                'id' => 52,
                'name' => 'Congo',
                'isoa2' => 'CG',
                'isoa3' => '',
                'isonumber' => '178',
                'areanum' => '242',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            52 => 
            array (
                'id' => 53,
                'name' => 'Cook Islands',
                'isoa2' => 'CK',
                'isoa3' => '',
                'isonumber' => '184',
                'areanum' => '682',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            53 => 
            array (
                'id' => 54,
                'name' => 'Costa Rica',
                'isoa2' => 'CR',
                'isoa3' => '',
                'isonumber' => '188',
                'areanum' => '506',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            54 => 
            array (
                'id' => 55,
                'name' => 'Cote D\'Ivoire',
                'isoa2' => 'CI',
                'isoa3' => '',
                'isonumber' => '384',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            55 => 
            array (
                'id' => 56,
                'name' => 'Croatia',
                'isoa2' => 'HR',
                'isoa3' => '',
                'isonumber' => '191',
                'areanum' => '385',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            56 => 
            array (
                'id' => 57,
                'name' => 'Cuba',
                'isoa2' => 'CU',
                'isoa3' => '',
                'isonumber' => '192',
                'areanum' => '53',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            57 => 
            array (
                'id' => 58,
                'name' => 'Curacao',
                'isoa2' => 'CW',
                'isoa3' => '',
                'isonumber' => '000',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            58 => 
            array (
                'id' => 59,
                'name' => 'Cyprus',
                'isoa2' => 'CY',
                'isoa3' => '',
                'isonumber' => '196',
                'areanum' => '357',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            59 => 
            array (
                'id' => 60,
                'name' => 'Czech Republic',
                'isoa2' => 'CZ',
                'isoa3' => '',
                'isonumber' => '203',
                'areanum' => '420',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            60 => 
            array (
                'id' => 61,
                'name' => 'Democratic Republic of Congo',
                'isoa2' => 'CD',
                'isoa3' => '',
                'isonumber' => '180',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            61 => 
            array (
                'id' => 62,
                'name' => 'Denmark',
                'isoa2' => 'DK',
                'isoa3' => '',
                'isonumber' => '208',
                'areanum' => '45',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            62 => 
            array (
                'id' => 63,
                'name' => 'Djibouti',
                'isoa2' => 'DJ',
                'isoa3' => '',
                'isonumber' => '262',
                'areanum' => '253',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            63 => 
            array (
                'id' => 64,
                'name' => 'Dominica',
                'isoa2' => 'DM',
                'isoa3' => '',
                'isonumber' => '212',
                'areanum' => '1767',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            64 => 
            array (
                'id' => 65,
                'name' => 'Dominican Republic',
                'isoa2' => 'DO',
                'isoa3' => '',
                'isonumber' => '214',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            65 => 
            array (
                'id' => 66,
                'name' => 'East Timor',
                'isoa2' => 'TL',
                'isoa3' => '',
                'isonumber' => '626',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            66 => 
            array (
                'id' => 67,
                'name' => 'Ecuador',
                'isoa2' => 'EC',
                'isoa3' => '',
                'isonumber' => '218',
                'areanum' => '593',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            67 => 
            array (
                'id' => 68,
                'name' => 'Egypt',
                'isoa2' => 'EG',
                'isoa3' => '',
                'isonumber' => '818',
                'areanum' => '20',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            68 => 
            array (
                'id' => 69,
                'name' => 'El Salvador',
                'isoa2' => 'SV',
                'isoa3' => '',
                'isonumber' => '222',
                'areanum' => '503',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            69 => 
            array (
                'id' => 70,
                'name' => 'Equatorial Guinea',
                'isoa2' => 'GQ',
                'isoa3' => '',
                'isonumber' => '226',
                'areanum' => '240',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            70 => 
            array (
                'id' => 71,
                'name' => 'Eritrea',
                'isoa2' => 'ER',
                'isoa3' => '',
                'isonumber' => '232',
                'areanum' => '291',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            71 => 
            array (
                'id' => 72,
                'name' => 'Estonia',
                'isoa2' => 'EE',
                'isoa3' => '',
                'isonumber' => '233',
                'areanum' => '372',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            72 => 
            array (
                'id' => 73,
                'name' => 'Ethiopia',
                'isoa2' => 'ET',
                'isoa3' => '',
                'isonumber' => '231',
                'areanum' => '251',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            73 => 
            array (
                'id' => 74,
            'name' => 'Falkland Islands (Malvinas)',
                'isoa2' => 'FK',
                'isoa3' => '',
                'isonumber' => '238',
                'areanum' => '500',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            74 => 
            array (
                'id' => 75,
                'name' => 'Faroe Islands',
                'isoa2' => 'FO',
                'isoa3' => '',
                'isonumber' => '234',
                'areanum' => '298',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            75 => 
            array (
                'id' => 76,
                'name' => 'Fiji',
                'isoa2' => 'FJ',
                'isoa3' => '',
                'isonumber' => '242',
                'areanum' => '679',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            76 => 
            array (
                'id' => 77,
                'name' => 'Finland',
                'isoa2' => 'FI',
                'isoa3' => '',
                'isonumber' => '246',
                'areanum' => '358',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            77 => 
            array (
                'id' => 78,
                'name' => 'France',
                'isoa2' => 'FR',
                'isoa3' => '',
                'isonumber' => '250',
                'areanum' => '33',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            78 => 
            array (
                'id' => 79,
                'name' => 'French Guiana',
                'isoa2' => 'GF',
                'isoa3' => '',
                'isonumber' => '254',
                'areanum' => '594',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            79 => 
            array (
                'id' => 80,
                'name' => 'French Polynesia',
                'isoa2' => 'PF',
                'isoa3' => '',
                'isonumber' => '258',
                'areanum' => '689',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            80 => 
            array (
                'id' => 81,
                'name' => 'French Southern Territories',
                'isoa2' => 'TF',
                'isoa3' => '',
                'isonumber' => '260',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            81 => 
            array (
                'id' => 82,
                'name' => 'FYROM',
                'isoa2' => 'MK',
                'isoa3' => '',
                'isonumber' => '807',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            82 => 
            array (
                'id' => 83,
                'name' => 'Gabon',
                'isoa2' => 'GA',
                'isoa3' => '',
                'isonumber' => '266',
                'areanum' => '241',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            83 => 
            array (
                'id' => 84,
                'name' => 'Gambia',
                'isoa2' => 'GM',
                'isoa3' => '',
                'isonumber' => '270',
                'areanum' => '220',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            84 => 
            array (
                'id' => 85,
                'name' => 'Georgia',
                'isoa2' => 'GE',
                'isoa3' => '',
                'isonumber' => '268',
                'areanum' => '995',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            85 => 
            array (
                'id' => 86,
                'name' => 'Germany',
                'isoa2' => 'DE',
                'isoa3' => '',
                'isonumber' => '276',
                'areanum' => '49',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            86 => 
            array (
                'id' => 87,
                'name' => 'Ghana',
                'isoa2' => 'GH',
                'isoa3' => '',
                'isonumber' => '288',
                'areanum' => '233',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            87 => 
            array (
                'id' => 88,
                'name' => 'Gibraltar',
                'isoa2' => 'GI',
                'isoa3' => '',
                'isonumber' => '292',
                'areanum' => '350',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            88 => 
            array (
                'id' => 89,
                'name' => 'Greece',
                'isoa2' => 'GR',
                'isoa3' => '',
                'isonumber' => '300',
                'areanum' => '30',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            89 => 
            array (
                'id' => 90,
                'name' => 'Greenland',
                'isoa2' => 'GL',
                'isoa3' => '',
                'isonumber' => '304',
                'areanum' => '299',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            90 => 
            array (
                'id' => 91,
                'name' => 'Grenada',
                'isoa2' => 'GD',
                'isoa3' => '',
                'isonumber' => '308',
                'areanum' => '1473',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            91 => 
            array (
                'id' => 92,
                'name' => 'Guadeloupe',
                'isoa2' => 'GP',
                'isoa3' => '',
                'isonumber' => '312',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            92 => 
            array (
                'id' => 93,
                'name' => 'Guam',
                'isoa2' => 'GU',
                'isoa3' => '',
                'isonumber' => '316',
                'areanum' => '1671',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            93 => 
            array (
                'id' => 94,
                'name' => 'Guatemala',
                'isoa2' => 'GT',
                'isoa3' => '',
                'isonumber' => '320',
                'areanum' => '502',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            94 => 
            array (
                'id' => 95,
                'name' => 'Guernsey',
                'isoa2' => 'GG',
                'isoa3' => '',
                'isonumber' => '831',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            95 => 
            array (
                'id' => 96,
                'name' => 'Guinea',
                'isoa2' => 'GN',
                'isoa3' => '',
                'isonumber' => '324',
                'areanum' => '224',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            96 => 
            array (
                'id' => 97,
                'name' => 'Guinea-Bissau',
                'isoa2' => 'GW',
                'isoa3' => '',
                'isonumber' => '624',
                'areanum' => '245',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            97 => 
            array (
                'id' => 98,
                'name' => 'Guyana',
                'isoa2' => 'GY',
                'isoa3' => '',
                'isonumber' => '328',
                'areanum' => '592',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            98 => 
            array (
                'id' => 99,
                'name' => 'Haiti',
                'isoa2' => 'HT',
                'isoa3' => '',
                'isonumber' => '332',
                'areanum' => '509',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            99 => 
            array (
                'id' => 100,
                'name' => 'Heard and Mc Donald Islands',
                'isoa2' => 'HM',
                'isoa3' => '',
                'isonumber' => '334',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            100 => 
            array (
                'id' => 101,
                'name' => 'Honduras',
                'isoa2' => 'HN',
                'isoa3' => '',
                'isonumber' => '340',
                'areanum' => '504',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            101 => 
            array (
                'id' => 102,
                'name' => 'Hong Kong',
                'isoa2' => 'HK',
                'isoa3' => '',
                'isonumber' => '344',
                'areanum' => '852',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            102 => 
            array (
                'id' => 103,
                'name' => 'Hungary',
                'isoa2' => 'HU',
                'isoa3' => '',
                'isonumber' => '348',
                'areanum' => '36',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            103 => 
            array (
                'id' => 104,
                'name' => 'Iceland',
                'isoa2' => 'IS',
                'isoa3' => '',
                'isonumber' => '352',
                'areanum' => '354',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            104 => 
            array (
                'id' => 105,
                'name' => 'India',
                'isoa2' => 'IN',
                'isoa3' => '',
                'isonumber' => '356',
                'areanum' => '91',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            105 => 
            array (
                'id' => 106,
                'name' => 'Indonesia',
                'isoa2' => 'ID',
                'isoa3' => '',
                'isonumber' => '360',
                'areanum' => '62',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            106 => 
            array (
                'id' => 107,
            'name' => 'Iran (Islamic Republic of)',
                'isoa2' => 'IR',
                'isoa3' => '',
                'isonumber' => '364',
                'areanum' => '98',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            107 => 
            array (
                'id' => 108,
                'name' => 'Iraq',
                'isoa2' => 'IQ',
                'isoa3' => '',
                'isonumber' => '368',
                'areanum' => '964',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            108 => 
            array (
                'id' => 109,
                'name' => 'Ireland',
                'isoa2' => 'IE',
                'isoa3' => '',
                'isonumber' => '372',
                'areanum' => '353',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            109 => 
            array (
                'id' => 110,
                'name' => 'Israel',
                'isoa2' => 'IL',
                'isoa3' => '',
                'isonumber' => '376',
                'areanum' => '972',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            110 => 
            array (
                'id' => 111,
                'name' => 'Italy',
                'isoa2' => 'IT',
                'isoa3' => '',
                'isonumber' => '380',
                'areanum' => '39',
                'fee' => 4,
                'status' => 1,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            111 => 
            array (
                'id' => 112,
                'name' => 'Jamaica',
                'isoa2' => 'JM',
                'isoa3' => '',
                'isonumber' => '388',
                'areanum' => '1876',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            112 => 
            array (
                'id' => 113,
                'name' => 'Japan',
                'isoa2' => 'JP',
                'isoa3' => '',
                'isonumber' => '392',
                'areanum' => '81',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            113 => 
            array (
                'id' => 114,
                'name' => 'Jersey',
                'isoa2' => 'JE',
                'isoa3' => '',
                'isonumber' => '832',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            114 => 
            array (
                'id' => 115,
                'name' => 'Jordan',
                'isoa2' => 'JO',
                'isoa3' => '',
                'isonumber' => '400',
                'areanum' => '962',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            115 => 
            array (
                'id' => 116,
                'name' => 'Kazakhstan',
                'isoa2' => 'KZ',
                'isoa3' => '',
                'isonumber' => '398',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            116 => 
            array (
                'id' => 117,
                'name' => 'Kenya',
                'isoa2' => 'KE',
                'isoa3' => '',
                'isonumber' => '404',
                'areanum' => '254',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            117 => 
            array (
                'id' => 118,
                'name' => 'Kiribati',
                'isoa2' => 'KI',
                'isoa3' => '',
                'isonumber' => '296',
                'areanum' => '686',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            118 => 
            array (
                'id' => 119,
                'name' => 'Korea, Republic of',
                'isoa2' => 'KR',
                'isoa3' => '',
                'isonumber' => '410',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            119 => 
            array (
                'id' => 120,
                'name' => 'Kuwait',
                'isoa2' => 'KW',
                'isoa3' => '',
                'isonumber' => '414',
                'areanum' => '965',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            120 => 
            array (
                'id' => 121,
                'name' => 'Kyrgyzstan',
                'isoa2' => 'KG',
                'isoa3' => '',
                'isonumber' => '417',
                'areanum' => '996',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            121 => 
            array (
                'id' => 122,
                'name' => 'Lao People\'s Democratic Republic',
                'isoa2' => 'LA',
                'isoa3' => '',
                'isonumber' => '418',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            122 => 
            array (
                'id' => 123,
                'name' => 'Latvia',
                'isoa2' => 'LV',
                'isoa3' => '',
                'isonumber' => '428',
                'areanum' => '371',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            123 => 
            array (
                'id' => 124,
                'name' => 'Lebanon',
                'isoa2' => 'LB',
                'isoa3' => '',
                'isonumber' => '422',
                'areanum' => '961',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            124 => 
            array (
                'id' => 125,
                'name' => 'Lesotho',
                'isoa2' => 'LS',
                'isoa3' => '',
                'isonumber' => '426',
                'areanum' => '266',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            125 => 
            array (
                'id' => 126,
                'name' => 'Liberia',
                'isoa2' => 'LR',
                'isoa3' => '',
                'isonumber' => '430',
                'areanum' => '231',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            126 => 
            array (
                'id' => 127,
                'name' => 'Libyan Arab Jamahiriya',
                'isoa2' => 'LY',
                'isoa3' => '',
                'isonumber' => '434',
                'areanum' => '218',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            127 => 
            array (
                'id' => 128,
                'name' => 'Liechtenstein',
                'isoa2' => 'LI',
                'isoa3' => '',
                'isonumber' => '438',
                'areanum' => '423',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            128 => 
            array (
                'id' => 129,
                'name' => 'Lithuania',
                'isoa2' => 'LT',
                'isoa3' => '',
                'isonumber' => '440',
                'areanum' => '370',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            129 => 
            array (
                'id' => 130,
                'name' => 'Luxembourg',
                'isoa2' => 'LU',
                'isoa3' => '',
                'isonumber' => '442',
                'areanum' => '352',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            130 => 
            array (
                'id' => 131,
                'name' => 'Macau',
                'isoa2' => 'MO',
                'isoa3' => '',
                'isonumber' => '446',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            131 => 
            array (
                'id' => 132,
                'name' => 'Madagascar',
                'isoa2' => 'MG',
                'isoa3' => '',
                'isonumber' => '450',
                'areanum' => '261',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            132 => 
            array (
                'id' => 133,
                'name' => 'Malawi',
                'isoa2' => 'MW',
                'isoa3' => '',
                'isonumber' => '454',
                'areanum' => '265',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            133 => 
            array (
                'id' => 134,
                'name' => 'Malaysia',
                'isoa2' => 'MY',
                'isoa3' => '',
                'isonumber' => '458',
                'areanum' => '60',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            134 => 
            array (
                'id' => 135,
                'name' => 'Maldives',
                'isoa2' => 'MV',
                'isoa3' => '',
                'isonumber' => '462',
                'areanum' => '960',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            135 => 
            array (
                'id' => 136,
                'name' => 'Mali',
                'isoa2' => 'ML',
                'isoa3' => '',
                'isonumber' => '466',
                'areanum' => '223',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            136 => 
            array (
                'id' => 137,
                'name' => 'Malta',
                'isoa2' => 'MT',
                'isoa3' => '',
                'isonumber' => '470',
                'areanum' => '356',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            137 => 
            array (
                'id' => 138,
                'name' => 'Marshall Islands',
                'isoa2' => 'MH',
                'isoa3' => '',
                'isonumber' => '584',
                'areanum' => '692',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            138 => 
            array (
                'id' => 139,
                'name' => 'Martinique',
                'isoa2' => 'MQ',
                'isoa3' => '',
                'isonumber' => '474',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            139 => 
            array (
                'id' => 140,
                'name' => 'Mauritania',
                'isoa2' => 'MR',
                'isoa3' => '',
                'isonumber' => '478',
                'areanum' => '222',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            140 => 
            array (
                'id' => 141,
                'name' => 'Mauritius',
                'isoa2' => 'MU',
                'isoa3' => '',
                'isonumber' => '480',
                'areanum' => '230',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            141 => 
            array (
                'id' => 142,
                'name' => 'Mayotte',
                'isoa2' => 'YT',
                'isoa3' => '',
                'isonumber' => '175',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            142 => 
            array (
                'id' => 143,
                'name' => 'Mexico',
                'isoa2' => 'MX',
                'isoa3' => '',
                'isonumber' => '484',
                'areanum' => '52',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            143 => 
            array (
                'id' => 144,
                'name' => 'Micronesia, Federated States of',
                'isoa2' => 'FM',
                'isoa3' => '',
                'isonumber' => '583',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            144 => 
            array (
                'id' => 145,
                'name' => 'Moldova, Republic of',
                'isoa2' => 'MD',
                'isoa3' => '',
                'isonumber' => '498',
                'areanum' => '373',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            145 => 
            array (
                'id' => 146,
                'name' => 'Monaco',
                'isoa2' => 'MC',
                'isoa3' => '',
                'isonumber' => '492',
                'areanum' => '377',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            146 => 
            array (
                'id' => 147,
                'name' => 'Mongolia',
                'isoa2' => 'MN',
                'isoa3' => '',
                'isonumber' => '496',
                'areanum' => '976',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            147 => 
            array (
                'id' => 148,
                'name' => 'Montenegro',
                'isoa2' => 'ME',
                'isoa3' => '',
                'isonumber' => '499',
                'areanum' => '382',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            148 => 
            array (
                'id' => 149,
                'name' => 'Montserrat',
                'isoa2' => 'MS',
                'isoa3' => '',
                'isonumber' => '500',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            149 => 
            array (
                'id' => 150,
                'name' => 'Morocco',
                'isoa2' => 'MA',
                'isoa3' => '',
                'isonumber' => '504',
                'areanum' => '212',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            150 => 
            array (
                'id' => 151,
                'name' => 'Mozambique',
                'isoa2' => 'MZ',
                'isoa3' => '',
                'isonumber' => '508',
                'areanum' => '258',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            151 => 
            array (
                'id' => 152,
                'name' => 'Myanmar',
                'isoa2' => 'MM',
                'isoa3' => '',
                'isonumber' => '104',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            152 => 
            array (
                'id' => 153,
                'name' => 'Namibia',
                'isoa2' => 'NA',
                'isoa3' => '',
                'isonumber' => '516',
                'areanum' => '264',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            153 => 
            array (
                'id' => 154,
                'name' => 'Nauru',
                'isoa2' => 'NR',
                'isoa3' => '',
                'isonumber' => '520',
                'areanum' => '674',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            154 => 
            array (
                'id' => 155,
                'name' => 'Nepal',
                'isoa2' => 'NP',
                'isoa3' => '',
                'isonumber' => '524',
                'areanum' => '977',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            155 => 
            array (
                'id' => 156,
                'name' => 'Netherlands',
                'isoa2' => 'NL',
                'isoa3' => '',
                'isonumber' => '528',
                'areanum' => '31',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            156 => 
            array (
                'id' => 157,
                'name' => 'Netherlands Antilles',
                'isoa2' => 'AN',
                'isoa3' => '',
                'isonumber' => '000',
                'areanum' => '599',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            157 => 
            array (
                'id' => 158,
                'name' => 'New Caledonia',
                'isoa2' => 'NC',
                'isoa3' => '',
                'isonumber' => '540',
                'areanum' => '687',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            158 => 
            array (
                'id' => 159,
                'name' => 'New Zealand',
                'isoa2' => 'NZ',
                'isoa3' => '',
                'isonumber' => '554',
                'areanum' => '64',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            159 => 
            array (
                'id' => 160,
                'name' => 'Nicaragua',
                'isoa2' => 'NI',
                'isoa3' => '',
                'isonumber' => '558',
                'areanum' => '505',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            160 => 
            array (
                'id' => 161,
                'name' => 'Niger',
                'isoa2' => 'NE',
                'isoa3' => '',
                'isonumber' => '562',
                'areanum' => '227',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            161 => 
            array (
                'id' => 162,
                'name' => 'Nigeria',
                'isoa2' => 'NG',
                'isoa3' => '',
                'isonumber' => '566',
                'areanum' => '234',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            162 => 
            array (
                'id' => 163,
                'name' => 'Niue',
                'isoa2' => 'NU',
                'isoa3' => '',
                'isonumber' => '570',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            163 => 
            array (
                'id' => 164,
                'name' => 'Norfolk Island',
                'isoa2' => 'NF',
                'isoa3' => '',
                'isonumber' => '574',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            164 => 
            array (
                'id' => 165,
                'name' => 'North Korea',
                'isoa2' => 'KP',
                'isoa3' => '',
                'isonumber' => '408',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            165 => 
            array (
                'id' => 166,
                'name' => 'Northern Mariana Islands',
                'isoa2' => 'MP',
                'isoa3' => '',
                'isonumber' => '580',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            166 => 
            array (
                'id' => 167,
                'name' => 'Norway',
                'isoa2' => 'NO',
                'isoa3' => '',
                'isonumber' => '578',
                'areanum' => '47',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            167 => 
            array (
                'id' => 168,
                'name' => 'Oman',
                'isoa2' => 'OM',
                'isoa3' => '',
                'isonumber' => '512',
                'areanum' => '968',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            168 => 
            array (
                'id' => 169,
                'name' => 'Pakistan',
                'isoa2' => 'PK',
                'isoa3' => '',
                'isonumber' => '586',
                'areanum' => '92',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            169 => 
            array (
                'id' => 170,
                'name' => 'Palau',
                'isoa2' => 'PW',
                'isoa3' => '',
                'isonumber' => '585',
                'areanum' => '680',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            170 => 
            array (
                'id' => 171,
                'name' => 'Palestinian Territory, Occupied',
                'isoa2' => 'PS',
                'isoa3' => '',
                'isonumber' => '275',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            171 => 
            array (
                'id' => 172,
                'name' => 'Panama',
                'isoa2' => 'PA',
                'isoa3' => '',
                'isonumber' => '591',
                'areanum' => '507',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            172 => 
            array (
                'id' => 173,
                'name' => 'Papua New Guinea',
                'isoa2' => 'PG',
                'isoa3' => '',
                'isonumber' => '598',
                'areanum' => '675',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            173 => 
            array (
                'id' => 174,
                'name' => 'Paraguay',
                'isoa2' => 'PY',
                'isoa3' => '',
                'isonumber' => '600',
                'areanum' => '595',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            174 => 
            array (
                'id' => 175,
                'name' => 'Peru',
                'isoa2' => 'PE',
                'isoa3' => '',
                'isonumber' => '604',
                'areanum' => '51',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            175 => 
            array (
                'id' => 176,
                'name' => 'Philippines',
                'isoa2' => 'PH',
                'isoa3' => '',
                'isonumber' => '608',
                'areanum' => '63',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            176 => 
            array (
                'id' => 177,
                'name' => 'Pitcairn',
                'isoa2' => 'PN',
                'isoa3' => '',
                'isonumber' => '612',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            177 => 
            array (
                'id' => 178,
                'name' => 'Poland',
                'isoa2' => 'PL',
                'isoa3' => '',
                'isonumber' => '616',
                'areanum' => '48',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            178 => 
            array (
                'id' => 179,
                'name' => 'Portugal',
                'isoa2' => 'PT',
                'isoa3' => '',
                'isonumber' => '620',
                'areanum' => '351',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            179 => 
            array (
                'id' => 180,
                'name' => 'Puerto Rico',
                'isoa2' => 'PR',
                'isoa3' => '',
                'isonumber' => '630',
                'areanum' => '1787',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            180 => 
            array (
                'id' => 181,
                'name' => 'Qatar',
                'isoa2' => 'QA',
                'isoa3' => '',
                'isonumber' => '634',
                'areanum' => '974',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            181 => 
            array (
                'id' => 182,
                'name' => 'Reunion',
                'isoa2' => 'RE',
                'isoa3' => '',
                'isonumber' => '638',
                'areanum' => '262',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            182 => 
            array (
                'id' => 183,
                'name' => 'Romania',
                'isoa2' => 'RO',
                'isoa3' => '',
                'isonumber' => '642',
                'areanum' => '40',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            183 => 
            array (
                'id' => 184,
                'name' => 'Russian Federation',
                'isoa2' => 'RU',
                'isoa3' => '',
                'isonumber' => '643',
                'areanum' => '7',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            184 => 
            array (
                'id' => 185,
                'name' => 'Rwanda',
                'isoa2' => 'RW',
                'isoa3' => '',
                'isonumber' => '646',
                'areanum' => '250',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            185 => 
            array (
                'id' => 186,
                'name' => 'Saint Kitts and Nevis',
                'isoa2' => 'KN',
                'isoa3' => '',
                'isonumber' => '659',
                'areanum' => '1869',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            186 => 
            array (
                'id' => 187,
                'name' => 'Saint Lucia',
                'isoa2' => 'LC',
                'isoa3' => '',
                'isonumber' => '662',
                'areanum' => '1758',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            187 => 
            array (
                'id' => 188,
                'name' => 'Saint Vincent and the Grenadines',
                'isoa2' => 'VC',
                'isoa3' => '',
                'isonumber' => '670',
                'areanum' => '1784',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            188 => 
            array (
                'id' => 189,
                'name' => 'Samoa',
                'isoa2' => 'WS',
                'isoa3' => '',
                'isonumber' => '882',
                'areanum' => '685',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            189 => 
            array (
                'id' => 190,
                'name' => 'San Marino',
                'isoa2' => 'SM',
                'isoa3' => '',
                'isonumber' => '674',
                'areanum' => '378',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            190 => 
            array (
                'id' => 191,
                'name' => 'Sao Tome and Principe',
                'isoa2' => 'ST',
                'isoa3' => '',
                'isonumber' => '678',
                'areanum' => '239',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            191 => 
            array (
                'id' => 192,
                'name' => 'Saudi Arabia',
                'isoa2' => 'SA',
                'isoa3' => '',
                'isonumber' => '682',
                'areanum' => '966',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            192 => 
            array (
                'id' => 193,
                'name' => 'Senegal',
                'isoa2' => 'SN',
                'isoa3' => '',
                'isonumber' => '686',
                'areanum' => '221',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            193 => 
            array (
                'id' => 194,
                'name' => 'Serbia',
                'isoa2' => 'RS',
                'isoa3' => '',
                'isonumber' => '688',
                'areanum' => '381',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            194 => 
            array (
                'id' => 195,
                'name' => 'Seychelles',
                'isoa2' => 'SC',
                'isoa3' => '',
                'isonumber' => '690',
                'areanum' => '248',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            195 => 
            array (
                'id' => 196,
                'name' => 'Sierra Leone',
                'isoa2' => 'SL',
                'isoa3' => '',
                'isonumber' => '694',
                'areanum' => '232',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            196 => 
            array (
                'id' => 197,
                'name' => 'Singapore',
                'isoa2' => 'SG',
                'isoa3' => '',
                'isonumber' => '702',
                'areanum' => '65',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            197 => 
            array (
                'id' => 198,
                'name' => 'Slovak Republic',
                'isoa2' => 'SK',
                'isoa3' => '',
                'isonumber' => '703',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            198 => 
            array (
                'id' => 199,
                'name' => 'Slovenia',
                'isoa2' => 'SI',
                'isoa3' => '',
                'isonumber' => '705',
                'areanum' => '386',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            199 => 
            array (
                'id' => 200,
                'name' => 'Solomon Islands',
                'isoa2' => 'SB',
                'isoa3' => '',
                'isonumber' => '090',
                'areanum' => '677',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            200 => 
            array (
                'id' => 201,
                'name' => 'Somalia',
                'isoa2' => 'SO',
                'isoa3' => '',
                'isonumber' => '706',
                'areanum' => '252',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            201 => 
            array (
                'id' => 202,
                'name' => 'South Africa',
                'isoa2' => 'ZA',
                'isoa3' => '',
                'isonumber' => '710',
                'areanum' => '27',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            202 => 
            array (
                'id' => 203,
                'name' => 'South Georgia &amp, South Sandwich Islands',
                'isoa2' => 'GS',
                'isoa3' => '',
                'isonumber' => '239',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            203 => 
            array (
                'id' => 204,
                'name' => 'South Sudan',
                'isoa2' => 'SS',
                'isoa3' => '',
                'isonumber' => '728',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            204 => 
            array (
                'id' => 205,
                'name' => 'Spain',
                'isoa2' => 'ES',
                'isoa3' => '',
                'isonumber' => '724',
                'areanum' => '34',
                'fee' => 6,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            205 => 
            array (
                'id' => 206,
                'name' => 'Sri Lanka',
                'isoa2' => 'LK',
                'isoa3' => '',
                'isonumber' => '144',
                'areanum' => '94',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            206 => 
            array (
                'id' => 207,
                'name' => 'St. Barthelemy',
                'isoa2' => 'BL',
                'isoa3' => '',
                'isonumber' => '652',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            207 => 
            array (
                'id' => 208,
                'name' => 'St. Helena',
                'isoa2' => 'SH',
                'isoa3' => '',
                'isonumber' => '654',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            208 => 
            array (
                'id' => 209,
            'name' => 'St. Martin (French part)',
                'isoa2' => 'MF',
                'isoa3' => '',
                'isonumber' => '663',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            209 => 
            array (
                'id' => 210,
                'name' => 'St. Pierre and Miquelon',
                'isoa2' => 'PM',
                'isoa3' => '',
                'isonumber' => '666',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            210 => 
            array (
                'id' => 211,
                'name' => 'Sudan',
                'isoa2' => 'SD',
                'isoa3' => '',
                'isonumber' => '729',
                'areanum' => '249',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            211 => 
            array (
                'id' => 212,
                'name' => 'Suriname',
                'isoa2' => 'SR',
                'isoa3' => '',
                'isonumber' => '740',
                'areanum' => '597',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            212 => 
            array (
                'id' => 213,
                'name' => 'Svalbard and Jan Mayen Islands',
                'isoa2' => 'SJ',
                'isoa3' => '',
                'isonumber' => '744',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            213 => 
            array (
                'id' => 214,
                'name' => 'Swaziland',
                'isoa2' => 'SZ',
                'isoa3' => '',
                'isonumber' => '748',
                'areanum' => '268',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            214 => 
            array (
                'id' => 215,
                'name' => 'Sweden',
                'isoa2' => 'SE',
                'isoa3' => '',
                'isonumber' => '752',
                'areanum' => '46',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            215 => 
            array (
                'id' => 216,
                'name' => 'Switzerland',
                'isoa2' => 'CH',
                'isoa3' => '',
                'isonumber' => '756',
                'areanum' => '41',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            216 => 
            array (
                'id' => 217,
                'name' => 'Syrian Arab Republic',
                'isoa2' => 'SY',
                'isoa3' => '',
                'isonumber' => '760',
                'areanum' => '963',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            217 => 
            array (
                'id' => 218,
                'name' => 'Taiwan',
                'isoa2' => 'TW',
                'isoa3' => '',
                'isonumber' => '158',
                'areanum' => '886',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            218 => 
            array (
                'id' => 219,
                'name' => 'Tajikistan',
                'isoa2' => 'TJ',
                'isoa3' => '',
                'isonumber' => '762',
                'areanum' => '992',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            219 => 
            array (
                'id' => 220,
                'name' => 'Tanzania, United Republic of',
                'isoa2' => 'TZ',
                'isoa3' => '',
                'isonumber' => '834',
                'areanum' => '255',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            220 => 
            array (
                'id' => 221,
                'name' => 'Thailand',
                'isoa2' => 'TH',
                'isoa3' => '',
                'isonumber' => '764',
                'areanum' => '66',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            221 => 
            array (
                'id' => 222,
                'name' => 'Togo',
                'isoa2' => 'TG',
                'isoa3' => '',
                'isonumber' => '768',
                'areanum' => '228',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            222 => 
            array (
                'id' => 223,
                'name' => 'Tokelau',
                'isoa2' => 'TK',
                'isoa3' => '',
                'isonumber' => '772',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            223 => 
            array (
                'id' => 224,
                'name' => 'Tonga',
                'isoa2' => 'TO',
                'isoa3' => '',
                'isonumber' => '776',
                'areanum' => '676',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            224 => 
            array (
                'id' => 225,
                'name' => 'Trinidad and Tobago',
                'isoa2' => 'TT',
                'isoa3' => '',
                'isonumber' => '780',
                'areanum' => '1868',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            225 => 
            array (
                'id' => 226,
                'name' => 'Tunisia',
                'isoa2' => 'TN',
                'isoa3' => '',
                'isonumber' => '788',
                'areanum' => '216',
                'fee' => 5,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            226 => 
            array (
                'id' => 227,
                'name' => 'Turkey',
                'isoa2' => 'TR',
                'isoa3' => '',
                'isonumber' => '792',
                'areanum' => '90',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            227 => 
            array (
                'id' => 228,
                'name' => 'Turkmenistan',
                'isoa2' => 'TM',
                'isoa3' => '',
                'isonumber' => '795',
                'areanum' => '993',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            228 => 
            array (
                'id' => 229,
                'name' => 'Turks and Caicos Islands',
                'isoa2' => 'TC',
                'isoa3' => '',
                'isonumber' => '796',
                'areanum' => '1649',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            229 => 
            array (
                'id' => 230,
                'name' => 'Tuvalu',
                'isoa2' => 'TV',
                'isoa3' => '',
                'isonumber' => '798',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            230 => 
            array (
                'id' => 231,
                'name' => 'Uganda',
                'isoa2' => 'UG',
                'isoa3' => '',
                'isonumber' => '800',
                'areanum' => '256',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            231 => 
            array (
                'id' => 232,
                'name' => 'Ukraine',
                'isoa2' => 'UA',
                'isoa3' => '',
                'isonumber' => '804',
                'areanum' => '380',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            232 => 
            array (
                'id' => 233,
                'name' => 'United Arab Emirates',
                'isoa2' => 'AE',
                'isoa3' => '',
                'isonumber' => '784',
                'areanum' => '971',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            233 => 
            array (
                'id' => 234,
                'name' => 'United Kingdom',
                'isoa2' => 'GB',
                'isoa3' => '',
                'isonumber' => '826',
                'areanum' => '44',
                'fee' => 3,
                'status' => 1,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            234 => 
            array (
                'id' => 235,
                'name' => 'United States',
                'isoa2' => 'US',
                'isoa3' => '',
                'isonumber' => '840',
                'areanum' => '1',
                'fee' => 1,
                'status' => 1,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            235 => 
            array (
                'id' => 236,
                'name' => 'United States Minor Outlying Islands',
                'isoa2' => 'UM',
                'isoa3' => '',
                'isonumber' => '581',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            236 => 
            array (
                'id' => 237,
                'name' => 'Uruguay',
                'isoa2' => 'UY',
                'isoa3' => '',
                'isonumber' => '858',
                'areanum' => '598',
                'fee' => 4,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            237 => 
            array (
                'id' => 238,
                'name' => 'Uzbekistan',
                'isoa2' => 'UZ',
                'isoa3' => '',
                'isonumber' => '860',
                'areanum' => '998',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            238 => 
            array (
                'id' => 239,
                'name' => 'Vanuatu',
                'isoa2' => 'VU',
                'isoa3' => '',
                'isonumber' => '548',
                'areanum' => '678',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            239 => 
            array (
                'id' => 240,
            'name' => 'Vatican City State (Holy See)',
                'isoa2' => 'VA',
                'isoa3' => '',
                'isonumber' => '336',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            240 => 
            array (
                'id' => 241,
                'name' => 'Venezuela',
                'isoa2' => 'VE',
                'isoa3' => '',
                'isonumber' => '862',
                'areanum' => '58',
                'fee' => 2,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            241 => 
            array (
                'id' => 242,
                'name' => 'Viet Nam',
                'isoa2' => 'VN',
                'isoa3' => '',
                'isonumber' => '704',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            242 => 
            array (
                'id' => 243,
            'name' => 'Virgin Islands (British)',
                'isoa2' => 'VG',
                'isoa3' => '',
                'isonumber' => '092',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            243 => 
            array (
                'id' => 244,
            'name' => 'Virgin Islands (U.S.)',
                'isoa2' => 'VI',
                'isoa3' => '',
                'isonumber' => '850',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            244 => 
            array (
                'id' => 245,
                'name' => 'Wallis and Futuna Islands',
                'isoa2' => 'WF',
                'isoa3' => '',
                'isonumber' => '876',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            245 => 
            array (
                'id' => 246,
                'name' => 'Western Sahara',
                'isoa2' => 'EH',
                'isoa3' => '',
                'isonumber' => '732',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            246 => 
            array (
                'id' => 247,
                'name' => 'Yemen',
                'isoa2' => 'YE',
                'isoa3' => '',
                'isonumber' => '887',
                'areanum' => '967',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            247 => 
            array (
                'id' => 248,
                'name' => 'Zambia',
                'isoa2' => 'ZM',
                'isoa3' => '',
                'isonumber' => '894',
                'areanum' => '260',
                'fee' => 1,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            248 => 
            array (
                'id' => 249,
                'name' => 'Zimbabwe',
                'isoa2' => 'ZW',
                'isoa3' => '',
                'isonumber' => '716',
                'areanum' => '263',
                'fee' => 3,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
            249 => 
            array (
                'id' => 250,
                'name' => 'France, Metropolitan',
                'isoa2' => 'FX',
                'isoa3' => '',
                'isonumber' => '000',
                'areanum' => '',
                'fee' => 0,
                'status' => 0,
                'created_at' => '2020-07-20 10:30:49',
                'updated_at' => NULL,
            ),
        ));
        
        
    }
}